#!/bin/sh

# Collect static files
echo "Collect static files"
python manage.py collectstatic --noinput

# Apply database migrations
echo "Apply database migrations"
#python manage.py makemigrations --merge
python manage.py migrate --noinput

# Create cache table
echo "Creating cache table"
python manage.py createcachetable

# Initialize admin
python manage.py initadmin

# Start server
echo "Starting server"
python manage.py runserver 0.0.0.0:8000
