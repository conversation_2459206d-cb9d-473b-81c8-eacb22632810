# PeerDB Setup Guide for MES PostgreSQL to ClickHouse Sync

## Overview
This guide sets up real-time data synchronization between your MES PostgreSQL database and ClickHouse using PeerDB.

## Current Status ✅
- **PeerDB**: Running at http://localhost:3000
- **PostgreSQL**: localhost:5432 (mes_db)
- **ClickHouse**: localhost:9000 (default database)

## Step-by-Step Configuration

### Step 1: Access PeerDB Dashboard
1. Open your browser to: http://localhost:3000
2. You should see the PeerDB dashboard

### Step 2: Create PostgreSQL Peer Connection
1. Click **"Peers"** in the sidebar
2. Click **"Create Peer"** button
3. Select **"PostgreSQL"** as the database type
4. Fill in the connection details:
   ```
   Name: postgres_mes_source
   Host: host.docker.internal
   Port: 5432
   Database: mes_db
   Username: postgres
   Password: postgres
   ```
5. Click **"Validate"** to test the connection
6. Click **"Create"** to save the peer

### Step 3: Create ClickHouse Peer Connection
1. Click **"Create Peer"** again
2. Select **"ClickHouse"** as the database type
3. Fill in the connection details:
   ```
   Name: clickhouse_mes_target
   Host: host.docker.internal
   Port: 9000
   Database: default
   Username: default
   Password: password
   ```
4. Click **"Validate"** to test the connection
5. Click **"Create"** to save the peer

### Step 4: Create Mirrors for Data Sync

#### Mirror 1: Tables with UPDATE Support (have updated_at column)
1. Click **"Mirrors"** in the sidebar
2. Click **"New Mirror"**
3. Select **"CDC"** mode
4. Configure:
   ```
   Mirror Name: mes_entities_mirror
   Source Peer: postgres_mes_source
   Target Peer: clickhouse_mes_target
   ```
5. Select these tables for sync:
   - mes_commodities
   - mes_components
   - mes_products
   - mes_product_parts
   - mes_product_components
   - mes_scanners
   - mes_areas
   - mes_assembly_lines
   - mes_factory
   - mes_process_blocks
   - mes_form_config
   - mes_routing
   - mes_routing_product
   - mes_routing_execution
   - mes_bom_header
   - mes_bom_item
   - mes_work_orders
   - mes_users
   - mes_modules
   - mes_groups
   - mes_access_scopes
   - mes_analytics_dashboards
   - mes_analytics_charts
   - mes_analytics_chart_groups
   - mes_aoi_daily_yield
   - mes_aoi_rejection

6. Set **Mode**: UPSERT (for updates)
7. Click **"Validate"** and then **"Create"**

#### Mirror 2: Append-Only Tables (no updated_at column)
1. Create another mirror with:
   ```
   Mirror Name: mes_events_mirror
   Source Peer: postgres_mes_source
   Target Peer: clickhouse_mes_target
   ```
2. Select these tables:
   - mes_manufacturing_events
   - mes_event_request_logs
   - mes_fifo_violation_logs
   - mes_cache

3. Set **Mode**: APPEND (insert-only)
4. Click **"Validate"** and then **"Create"**

### Step 5: Verify Sync Status
1. Go to **"Mirrors"** page
2. Click on each mirror to view sync status
3. Check the **"Sync Status"** tab for real-time statistics

## Alternative: SQL-Based Setup
If you prefer SQL commands, connect to PeerDB and run the commands in `setup_peerdb_sync.sql`

```bash
psql "port=9900 host=localhost password=peerdb"
```

Then execute the SQL file content.

## Verification Commands

### Test PostgreSQL Connection
```bash
psql -h localhost -p 5432 -U postgres -d mes_db -c "SELECT count(*) FROM mes_products;"
```

### Test ClickHouse Connection  
```bash
echo "SELECT count(*) FROM default.mes_products" | curl --data-binary @- "http://localhost:8123/" -u default:password
```

### Monitor Sync Progress
Check the PeerDB dashboard at http://localhost:3000 for real-time sync statistics.

## Troubleshooting

### Connection Issues
- Ensure `host.docker.internal` resolves to your host machine
- Verify PostgreSQL allows connections from Docker containers
- Check ClickHouse is accessible on port 9000

### Sync Issues
- Check mirror logs in PeerDB dashboard
- Verify table permissions in both databases
- Monitor PeerDB container logs: `docker logs peerdb-server`

## Next Steps
1. Test data sync by inserting/updating records in PostgreSQL
2. Verify changes appear in ClickHouse within 1-2 minutes
3. Set up monitoring and alerting for sync failures
4. Configure additional tables as needed
