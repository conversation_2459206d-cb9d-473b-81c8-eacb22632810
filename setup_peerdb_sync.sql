-- PeerDB Configuration Script for MES PostgreSQL to ClickHouse Sync
-- This script sets up real-time data synchronization between PostgreSQL and ClickHouse

-- =====================================================
-- STEP 1: Create PostgreSQL Peer Connection
-- =====================================================

CREATE PEER postgres_mes_source AS POSTGRES WITH (
  host = 'host.docker.internal',
  port = 5432,
  user = 'postgres',
  password = 'postgres',
  database = 'mes_db'
);

-- =====================================================
-- STEP 2: Create ClickHouse Peer Connection  
-- =====================================================

CREATE PEER clickhouse_mes_target AS CLICKHOUSE WITH (
  host = 'host.docker.internal',
  port = 9000,
  user = 'default',
  password = 'password',
  database = 'default'
);

-- =====================================================
-- STEP 3: Create Mirror for Tables WITH updated_at Column
-- These tables support UPDATE operations
-- =====================================================

CREATE MIRROR mes_entities_mirror
FROM postgres_mes_source TO clickhouse_mes_target
FOR $$
  SELECT 
    'mes_commodities',
    'mes_components', 
    'mes_products',
    'mes_product_parts',
    'mes_product_components',
    'mes_scanners',
    'mes_areas',
    'mes_assembly_lines',
    'mes_factory',
    'mes_process_blocks',
    'mes_form_config',
    'mes_routing',
    'mes_routing_product',
    'mes_routing_execution',
    'mes_bom_header',
    'mes_bom_item',
    'mes_work_orders',
    'mes_users',
    'mes_modules',
    'mes_groups',
    'mes_access_scopes',
    'mes_user_mes_groups',
    'mes_group_module_permissions',
    'mes_group_object_permissions',
    'mes_user_module_permissions',
    'mes_user_object_permissions',
    'mes_reference_categories',
    'mes_reference_values',
    'mes_master_program',
    'mes_master_program_product_param',
    'mes_sop',
    'mes_analytics_dashboards',
    'mes_analytics_charts',
    'mes_analytics_chart_groups',
    'mes_aoi_daily_yield',
    'mes_aoi_rejection'
$$ WITH (
  mode = 'upsert',
  unique_key_columns = ['id'],
  soft_delete = false,
  sync_data_format = 'avro'
);

-- =====================================================
-- STEP 4: Create Mirror for Append-Only Tables
-- These tables do NOT have updated_at and are insert-only
-- =====================================================

CREATE MIRROR mes_events_mirror  
FROM postgres_mes_source TO clickhouse_mes_target
FOR $$
  SELECT
    'mes_manufacturing_events',
    'mes_event_request_logs',
    'mes_fifo_violation_logs',
    'mes_cache'
$$ WITH (
  mode = 'append',
  sync_data_format = 'avro'
);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check peer connections
SELECT * FROM peers;

-- Check mirror status  
SELECT * FROM mirrors;

-- Check sync status
SELECT 
  mirror_name,
  source_table_name,
  target_table_name,
  sync_status,
  last_sync_time,
  rows_synced
FROM mirror_stats;
