-- PeerDB Configuration Commands for MES PostgreSQL to ClickHouse Sync
-- Use these commands in the PeerDB UI or via SQL interface

-- =====================================================
-- STEP 1: Create PostgreSQL Peer Connection
-- =====================================================
-- Use PeerDB UI at http://localhost:3000
-- Go to Peers > Create Peer > PostgreSQL
--
-- Name: postgres_mes_source
-- Host: host.docker.internal
-- Port: 5432
-- Database: mes_db
-- Username: postgres
-- Password: postgres

-- =====================================================
-- STEP 2: Create ClickHouse Peer Connection
-- =====================================================
-- Use PeerDB UI at http://localhost:3000
-- Go to Peers > Create Peer > ClickHouse
--
-- Name: clickhouse_mes_target
-- Host: host.docker.internal
-- Port: 9000
-- Database: default
-- Username: default
-- Password: password

-- =====================================================
-- STEP 3: Tables to Sync (Reference List)
-- =====================================================

-- Tables WITH updated_at column (supports UPDATE mode):
-- mes_commodities, mes_components, mes_products, mes_product_parts,
-- mes_product_components, mes_scanners, mes_areas, mes_assembly_lines,
-- mes_factory, mes_process_blocks, mes_form_config, mes_routing,
-- mes_routing_product, mes_routing_execution, mes_bom_header, mes_bom_item,
-- mes_work_orders, mes_users, mes_modules, mes_groups, mes_access_scopes,
-- mes_user_mes_groups, mes_group_module_permissions, mes_group_object_permissions,
-- mes_user_module_permissions, mes_user_object_permissions, mes_reference_categories,
-- mes_reference_values, mes_master_program, mes_master_program_product_param,
-- mes_sop, mes_analytics_dashboards, mes_analytics_charts,
-- mes_analytics_chart_groups, mes_aoi_daily_yield, mes_aoi_rejection

-- Tables WITHOUT updated_at column (INSERT-only mode):
-- mes_manufacturing_events, mes_event_request_logs,
-- mes_fifo_violation_logs, mes_cache

-- =====================================================
-- STEP 4: Create Mirrors via UI
-- =====================================================
-- Go to Mirrors > New Mirror > CDC
--
-- Mirror 1: mes_entities_mirror
-- Source: postgres_mes_source
-- Target: clickhouse_mes_target
-- Mode: CDC with UPSERT
-- Tables: Select all tables WITH updated_at column from list above
--
-- Mirror 2: mes_events_mirror
-- Source: postgres_mes_source
-- Target: clickhouse_mes_target
-- Mode: CDC with APPEND
-- Tables: Select all tables WITHOUT updated_at column from list above

-- =====================================================
-- VERIFICATION COMMANDS (Run in PeerDB psql)
-- =====================================================

-- Check if we can connect to PeerDB
SELECT version();

-- List available schemas/tables (if any)
\dt

-- Note: Peer and Mirror management is primarily done through the UI
-- The SQL interface is mainly for querying and monitoring
