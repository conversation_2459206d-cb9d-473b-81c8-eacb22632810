-- PeerDB Programmatic Configuration SQL Script
-- Execute these commands directly in PeerDB to create peers and mirrors
-- Usage: psql "port=9900 host=localhost password=peerdb" -f setup_peerdb_sync.sql

-- =====================================================
-- STEP 1: Create PostgreSQL Peer Connection
-- =====================================================

CREATE PEER postgres_mes_source FROM POSTGRES WITH (
    host = 'host.docker.internal',
    port = 5432,
    user = 'postgres',
    password = 'postgres',
    database = 'mes_db'
);

-- =====================================================
-- STEP 2: Create ClickHouse Peer Connection
-- =====================================================

CREATE PEER clickhouse_mes_target FROM CLICKHOUSE WITH (
    host = 'host.docker.internal',
    port = 9000,
    user = 'default',
    password = 'password',
    database = 'default',
    disable_tls = true
);

-- =====================================================
-- STEP 3: Create Mirror for Entity Tables (UPDATE Mode)
-- Tables WITH updated_at column support UPDATE operations
-- =====================================================

CREATE MIRROR IF NOT EXISTS mes_entities_mirror
FROM postgres_mes_source TO clickhouse_mes_target
WITH TABLE MAPPING (
    public.mes_commodities:mes_commodities,
    public.mes_components:mes_components,
    public.mes_products:mes_products,
    public.mes_product_parts:mes_product_parts,
    public.mes_product_components:mes_product_components,
    public.mes_scanners:mes_scanners,
    public.mes_areas:mes_areas,
    public.mes_assembly_lines:mes_assembly_lines,
    public.mes_factory:mes_factory,
    public.mes_process_blocks:mes_process_blocks,
    public.mes_form_config:mes_form_config,
    public.mes_routing:mes_routing,
    public.mes_routing_product:mes_routing_product,
    public.mes_routing_execution:mes_routing_execution,
    public.mes_bom_header:mes_bom_header,
    public.mes_bom_item:mes_bom_item,
    public.mes_work_orders:mes_work_orders,
    public.mes_users:mes_users,
    public.mes_modules:mes_modules,
    public.mes_groups:mes_groups,
    public.mes_access_scopes:mes_access_scopes,
    public.mes_user_mes_groups:mes_user_mes_groups,
    public.mes_group_module_permissions:mes_group_module_permissions,
    public.mes_group_object_permissions:mes_group_object_permissions,
    public.mes_user_module_permissions:mes_user_module_permissions,
    public.mes_user_object_permissions:mes_user_object_permissions,
    public.mes_reference_categories:mes_reference_categories,
    public.mes_reference_values:mes_reference_values,
    public.mes_master_program:mes_master_program,
    public.mes_master_program_product_param:mes_master_program_product_param,
    public.mes_sop:mes_sop,
    public.mes_analytics_dashboards:mes_analytics_dashboards,
    public.mes_analytics_charts:mes_analytics_charts,
    public.mes_analytics_chart_groups:mes_analytics_chart_groups,
    public.mes_aoi_daily_yield:mes_aoi_daily_yield,
    public.mes_aoi_rejection:mes_aoi_rejection
)
WITH (
    do_initial_copy = true,
    max_batch_size = 10000,
    sync_interval = 30,
    snapshot_num_rows_per_partition = 500000,
    snapshot_max_parallel_workers = 4,
    snapshot_num_tables_in_parallel = 4,
    soft_delete = true,
    synced_at_col_name = '_PEERDB_SYNCED_AT',
    soft_delete_col_name = '_PEERDB_IS_DELETED'
);

-- =====================================================
-- STEP 4: Create Mirror for Event Tables (APPEND Mode)
-- Tables WITHOUT updated_at column are append-only
-- =====================================================

CREATE MIRROR IF NOT EXISTS mes_events_mirror
FROM postgres_mes_source TO clickhouse_mes_target
WITH TABLE MAPPING (
    public.mes_manufacturing_events:mes_manufacturing_events,
    public.mes_event_request_logs:mes_event_request_logs,
    public.mes_fifo_violation_logs:mes_fifo_violation_logs,
    public.mes_cache:mes_cache
)
WITH (
    do_initial_copy = true,
    max_batch_size = 10000,
    sync_interval = 30,
    snapshot_num_rows_per_partition = 500000,
    snapshot_max_parallel_workers = 4,
    snapshot_num_tables_in_parallel = 4,
    soft_delete = false,
    synced_at_col_name = '_PEERDB_SYNCED_AT'
);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check peer connections
SELECT name, type FROM peers ORDER BY name;

-- Check mirror status
SELECT flow_job_name FROM flows ORDER BY flow_job_name;

-- Show detailed mirror information (if available)
\d+ flows;
