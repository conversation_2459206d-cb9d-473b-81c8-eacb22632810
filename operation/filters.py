import django_filters
from .models import ManufacturingEvent


class ManufacturingEventFilter(django_filters.FilterSet):
    start_date = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    end_date = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    part_no = django_filters.CharFilter(field_name='part__part_no')
    form_code = django_filters.CharFilter(field_name='form__code')
    inspection_status = django_filters.BooleanFilter()

    class Meta:
        model = ManufacturingEvent
        fields = {
            'form': ['exact'],
            'serial_number': ['exact', 'contains'],
            'event_type': ['exact'],
            'work_order': ['exact', 'contains'],
            'board': ['exact'],
            'created_at': ['exact', 'lt', 'gt'],
        }
