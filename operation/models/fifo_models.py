from django.db import models
from django.conf import settings
from catalog.models import Product


class FIFOViolationLog(models.Model):
    """
    Logs FIFO violations for auditing and reporting
    """
    event = models.ForeignKey(
        'operation.ManufacturingEvent', 
        on_delete=models.CASCADE, 
        related_name='fifo_violations', 
        null=True
    )
    serial_number = models.CharField(max_length=100)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='fifo_violations')
    
    VIOLATION_TYPES = [
        ('sequence', 'Sequence Violation'),
        ('time_min', 'Minimum Time Violation'),
        ('time_max', 'Maximum Time Violation'),
        ('fifo_order', 'FIFO Order Violation'),
    ]
    violation_type = models.CharField(max_length=20, choices=VIOLATION_TYPES)
    violation_details = models.JSONField()
    violation_timestamp = models.DateTimeField(auto_now_add=True)
    
    # User who acknowledged/overrode the violation
    acknowledged_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='acknowledged_fifo_violations'
    )
    override_reason = models.TextField(null=True, blank=True)
    
    class Meta:
        db_table = 'mes_fifo_violation_logs'
        ordering = ['-violation_timestamp']
    
    def __str__(self):
        return f"{self.serial_number} - {self.get_violation_type_display()}"
