from rest_framework import viewsets, permissions, status
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import MultiPartParser
from django_filters.rest_framework import DjangoFilterBackend
from core.pagination import CustomPageNumberPagination
from core.validators import ExcelValida<PERSON>
from authentication.decorators import requires_permission

from ..models import WorkOrder
from ..serializers.work_order_serializers import WorkOrderSerializer
from ..config.work_order import WorkOrderImportConfig
from ..services.work_order_service import WorkOrderImportService


class WorkOrderViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows Work Orders to be viewed or edited.
    
    list:
    Return a list of all Work Orders.
    
    create:
    Create a new Work Order instance.
    
    retrieve:
    Return the given Work Order.
    
    update:
    Update the given Work Order.
    
    partial_update:
    Partially update the given Work Order.
    
    destroy:
    Delete the given Work Order.
    """
    queryset = WorkOrder.objects.all().order_by('-order_date', 'line')
    serializer_class = WorkOrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['line', 'order_date', 'order_no']
    search_fields = ['part_no', 'customer']
    ordering_fields = ['order_date', 'created_at',]
    ordering = ['-order_date',]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @requires_permission(('work_order', 'get'))
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @requires_permission(('work_order', 'create'))
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @requires_permission(('work_order', 'get'))
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @requires_permission(('work_order', 'update'))
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @requires_permission(('work_order', 'update'))
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @requires_permission(('work_order', 'delete'))
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class WorkOrderExcelUploadView(APIView):
    """
    API endpoint for uploading work orders from an Excel file.
    """
    parser_classes = (MultiPartParser,)

    @requires_permission(('work_order', 'create'))
    def post(self, request):
        if 'file' not in request.FILES:
            return Response({
                'success': False,
                'error': 'No file provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        file_obj = request.FILES['file']
        validator = ExcelValidator(WorkOrderImportConfig())
        import_service = WorkOrderImportService(validator, user=request.user)
        result = import_service.process_file(file_obj)
        status_code = status.HTTP_200_OK if result['success'] else status.HTTP_400_BAD_REQUEST
        return Response(result, status=status_code)
