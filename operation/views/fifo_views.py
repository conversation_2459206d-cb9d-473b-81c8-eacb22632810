from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from authentication.decorators import requires_permission
from core.pagination import CustomPageNumberPagination

from operation.models import FIFOViolationLog
from operation.serializers.fifo_serializers import (
    FIFOViolationLogSerializer,
    FIFOViolationOverrideSerializer
)


class FIFOViolationLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing FIFO violation logs
    """
    queryset = FIFOViolationLog.objects.all()
    serializer_class = FIFOViolationLogSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['serial_number', 'product', 'violation_type']
    
    @requires_permission(('fifo', 'override'))
    @action(detail=True, methods=['post'])
    def override(self, request, pk=None):
        """
        Override a FIFO violation
        """
        violation = self.get_object()
        
        # Check if already acknowledged
        if violation.acknowledged_by:
            return Response(
                {"error": "This violation has already been acknowledged"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate override reason
        serializer = FIFOViolationOverrideSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        # Update the violation
        violation.acknowledged_by = request.user
        violation.override_reason = serializer.validated_data['override_reason']
        violation.save()
        
        return Response({
            "message": "Violation override recorded",
            "violation": FIFOViolationLogSerializer(violation).data
        })
