from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError
from django.core.exceptions import ValidationError as DjValidationError
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from authentication.decorators import requires_permission
from core.pagination import CustomPageNumberPagination

from ..models import ManufacturingEvent, FIFOViolationLog
from ..services.event_service import ManufacturingEventService
from ..services.schema_service import EventSchemaService
from ..services.fifo_validation_service import validate_event_with_fifo
from ..serializers.event_serializers import ManufacturingEventSerializer
from ..filters import ManufacturingEventFilter


class ManufacturingEventViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing manufacturing events.
    Handles CRUD operations with specialized event creation logic.
    """
    permission_classes = [IsAuthenticated]
    queryset = ManufacturingEvent.objects.all().order_by('-created_at')
    serializer_class = ManufacturingEventSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = ManufacturingEventFilter
    pagination_class = CustomPageNumberPagination

    def perform_create(self, serializer):
        """Set the authenticated user as creator"""
        serializer.save(created_by=self.request.user)

    @requires_permission(('event', 'get'))
    def list(self, request, *args, **kwargs):
        """
        Override list method to include display schema in response.
        This helps client applications render the event data appropriately.
        """
        # Get regular paginated queryset
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)

            # Get schema for the current form_id if specified
            # form_id = request.query_params.get('form')
            # schema = EventSchemaService.get_display_schema(form_id)

            return self.get_paginated_response({
                # 'schema': schema,
                'data': serializer.data
            })

        serializer = self.get_serializer(queryset, many=True)
        schema = EventSchemaService.get_display_schema()

        return Response({
            'schema': schema,
            'data': serializer.data
        })

    @requires_permission(('event', 'create'), ('form', 'get', lambda req: req.data.get('form')))
    def create(self, request, *args, **kwargs):
        """
        Create a manufacturing event with support for corrected events.
        For corrected events, creates both failed and success entries.
        """
        try:
            # Validate FIFO rules
            is_valid, fifo_message = validate_event_with_fifo(request.data)
            print("__FIFO RETURNED__:", is_valid, fifo_message)

            if not is_valid:
                return Response({'error': fifo_message}, status=status.HTTP_400_BAD_REQUEST)

            # Transform event data
            event_records = ManufacturingEventService.transform_event_data(request.data)
            serializer = self.get_serializer(data=event_records, many=True)
            serializer.is_valid(raise_exception=True)
            events = serializer.save()

            # Update routing execution with event IDs
            from workflow_config.services.routing_validation_service import RoutingValidationService

            # Group events by serial number
            events_by_serial = {}
            for event in events:
                if event.serial_number not in events_by_serial:
                    events_by_serial[event.serial_number] = []
                events_by_serial[event.serial_number].append(event.id)

                # Update any FIFO violation logs with the created event
                FIFOViolationLog.objects.filter(
                    serial_number=event.serial_number,
                    event__isnull=True
                ).update(event=event)

            # Update routing execution with all event IDs for each serial number
            for serial_number, event_ids in events_by_serial.items():
                RoutingValidationService.update_routing_execution_with_event_ids(
                    serial_number,
                    event_ids
                )

            return Response({"events" : serializer.data}, status=status.HTTP_201_CREATED)
        except ValidationError as e:
            print("Event Submission ValidationError: ", e, "| Request Event:", request.data)
            return Response({'error': e.detail}, status=status.HTTP_400_BAD_REQUEST)
        except DjValidationError as e:
            print("Event Submission DjValidationError: ", e, "| Request Event:", request.data)
            return Response({'error': e.message}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            print("Event Submission Exception: ", e, "|Request Event:", request.data)
            return Response({'error': e.message}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def schema(self, request):
        """
        Get the display schema for events.
        Optionally filtered by form query parameter.
        """
        form_id = request.query_params.get('form')
        schema = EventSchemaService.get_display_schema(form_id)
        return Response(schema)
