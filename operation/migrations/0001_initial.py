# Generated by Django 5.1 on 2024-11-30 19:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('catalog', '0001_initial'),
        ('workflow_config', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ManufacturingEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_number', models.CharField(max_length=100)),
                ('event_type', models.CharField(choices=[('rework', 'Rework'), ('main', 'Main')], default='main', max_length=100)),
                ('next_action', models.CharField(choices=[('send_rework', 'Send Rework'), ('main_forward', 'Main Forward'), ('corrected', 'Corrected on Fail')], default='main_forward', max_length=100)),
                ('work_order', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('board', models.Integer<PERSON>ield()),
                ('event_data', models.JSONField()),
                ('inspection_status', models.BooleanField(default=True)),
                ('validation_status', models.Char<PERSON>ield(default='pending', max_length=20)),
                ('validation_errors', models.JSONField(blank=True, default=list)),
                ('timestamp', models.DateTimeField()),
                ('created_by', models.EmailField(max_length=254)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('form', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='events', to='workflow_config.formconfig')),
                ('part', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='event_data', to='catalog.productpart')),
            ],
            options={
                'db_table': 'mes_manufacturing_events',
                'ordering': ['-id'],
            },
        ),
    ]
