# Generated by Django 5.1 on 2024-12-01 05:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0002_scanner'),
        ('operation', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='manufacturingevent',
            name='scanner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scanned_events', to='catalog.scanner'),
        ),
        migrations.AlterField(
            model_name='manufacturingevent',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='submitted_events', to=settings.AUTH_USER_MODEL),
        ),
    ]
