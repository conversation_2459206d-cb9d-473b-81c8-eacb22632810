# Generated by Django 5.1 on 2025-05-31 19:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0006_remove_time_constraint_fields'),
        ('operation', '0011_fifoviolationlog'),
    ]

    operations = [
        migrations.AddField(
            model_name='workorder',
            name='product',
            field=models.ForeignKey(blank=True, help_text='Product being manufactured', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='work_orders', to='catalog.product'),
        ),
        migrations.AddField(
            model_name='workorder',
            name='remaining_quantity',
            field=models.IntegerField(blank=True, help_text='Remaining quantity to produce', null=True),
        ),
    ]
