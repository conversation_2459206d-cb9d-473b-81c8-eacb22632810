# Generated by Django 5.1 on 2025-04-06 16:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0005_product_fifo_custom_config_product_fifo_enabled_and_more'),
        ('operation', '0010_workorder'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FIFOViolationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_number', models.CharField(max_length=100)),
                ('violation_type', models.CharField(choices=[('sequence', 'Sequence Violation'), ('time_min', 'Minimum Time Violation'), ('time_max', 'Maximum Time Violation'), ('fifo_order', 'FIFO Order Violation')], max_length=20)),
                ('violation_details', models.J<PERSON>NField()),
                ('violation_timestamp', models.DateTimeField(auto_now_add=True)),
                ('override_reason', models.TextField(blank=True, null=True)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acknowledged_fifo_violations', to=settings.AUTH_USER_MODEL)),
                ('event', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='fifo_violations', to='operation.manufacturingevent')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fifo_violations', to='catalog.product')),
            ],
            options={
                'db_table': 'mes_fifo_violation_logs',
                'ordering': ['-violation_timestamp'],
            },
        ),
    ]
