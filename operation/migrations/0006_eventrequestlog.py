# Generated by Django 5.1 on 2025-02-05 16:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operation', '0005_alter_manufacturingevent_work_order'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventRequestLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_data', models.J<PERSON><PERSON>ield(help_text='Raw request data as received')),
                ('url', models.Char<PERSON><PERSON>(help_text='URL of the request', max_length=255)),
                ('method', models.<PERSON><PERSON><PERSON><PERSON>(help_text='HTTP method used', max_length=10)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
            options={
                'db_table': 'mes_event_request_logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
