# Generated by Django 5.1 on 2025-03-23 16:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operation', '0009_alter_manufacturingevent_scanner'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('line', models.CharField(default='SMT', help_text='Production line', max_length=100)),
                ('part_no', models.CharField(help_text='Part number', max_length=100)),
                ('customer', models.CharField(help_text='Customer name', max_length=100)),
                ('order_date', models.DateField(help_text='Date when the order was placed')),
                ('cf', models.IntegerField(blank=True, help_text='Carry forward value', null=True)),
                ('order_no', models.<PERSON>r<PERSON><PERSON>(help_text='Order number', max_length=100)),
                ('plan', models.IntegerField(blank=True, help_text='Planned production quantity', null=True)),
                ('actual', models.IntegerField(blank=True, help_text='Actual production quantity', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_work_orders', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'mes_work_orders',
                'ordering': ['-order_date', 'line'],
            },
        ),
    ]
