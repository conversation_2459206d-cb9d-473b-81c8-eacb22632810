import os
import importlib
from pathlib import Path
from functools import lru_cache
from abc import ABC, abstractmethod
from operation.config.transformer_config import FORM_TO_TRANSFORMER_MAPPING


class BaseEventTransformerPlugin(ABC):
    """Abstract base class for event transformer plugins."""
    transformer_id = "default"  # Each plugin must provide a unique transformer_id

    @abstractmethod
    def transform(self, event_data: dict) -> list:
        """Transforms incoming event data into a list of canonical event dictionaries."""
        pass


@lru_cache(maxsize=1)
def load_plugins(plugin_dir: str = None) -> dict:
    """
    Dynamically loads all event transformer plugins from the specified directory.
    Caches the result so that subsequent calls return the cached plugin mapping.

    Returns:
        dict: Mapping from transformer_id to plugin instance
    """
    plugins = {}

    # If no plugin directory is provided, default to the 'event_transformers_plugins' folder
    if plugin_dir is None:
        current_dir = Path(__file__).parent
        plugin_dir = current_dir / "event_transformers_plugins"

    if not plugin_dir.exists():
        return plugins

    for file_name in os.listdir(plugin_dir):
        if file_name.endswith(".py") and not file_name.startswith("__"):
            module_name = file_name[:-3]  # Remove '.py'
            full_module_name = f"operation.services.event_transformers_plugins.{module_name}"
            module = importlib.import_module(full_module_name)

            # Iterate over attributes in the module
            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)
                if (isinstance(attribute, type) and 
                    issubclass(attribute, BaseEventTransformerPlugin) and 
                    attribute is not BaseEventTransformerPlugin):
                    plugin_instance = attribute()
                    plugins[plugin_instance.transformer_id] = plugin_instance
    return plugins


def get_plugin(form_code: str) -> BaseEventTransformerPlugin:
    """
    Retrieve the appropriate event transformer plugin based on form_code.
    If no dedicated plugin exists, fallback to the default transformer.
    """
    plugins = load_plugins()
    transformer_id = FORM_TO_TRANSFORMER_MAPPING.get(form_code, "default")

    return plugins.get(transformer_id, plugins.get("default"))
