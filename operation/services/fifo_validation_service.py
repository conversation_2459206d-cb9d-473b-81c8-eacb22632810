import logging
from operation.services.fifo_service import FIFOService

# Set up logger
logger = logging.getLogger('fifo_validation')


def validate_event_with_fifo(event_data):
    """
    Validate an event with FIFO rules

    Args:
        event_data: The event data to validate

    Returns:
        (bool, str): (is_valid, violation_message)
    """
    logger.info("Starting FIFO validation for event")
    logger.debug(f"Event data: {event_data}")

    # Extract serial number
    serial_number = event_data.get('serial_number')
    logger.info(f"Serial number: {serial_number}")

    if not serial_number:
        logger.warning("No serial number in event data, skipping FIFO validation")
        return True, None  # Can't validate without serial number

    # Validate against FIFO rules
    logger.info(f"Calling FIFOService.validate_fifo for serial number: {serial_number}")
    is_valid, violation_reason = FIFOService.validate_fifo(serial_number, event_data)
    logger.info(f"FIFO validation result: {is_valid}, Reason: {violation_reason}")

    if not is_valid:
        logger.warning(f"FIFO validation failed: {violation_reason}")

        # Handle violation based on enforcement level
        logger.info(f"Handling FIFO violation for serial number: {serial_number}")
        can_proceed, message = FIFOService.handle_fifo_violation(
            None,  # Event not created yet
            serial_number,
            violation_reason
        )
        logger.info(f"Violation handling result: can_proceed={can_proceed}, message={message}")

        if not can_proceed:
            logger.warning(f"Operation blocked due to FIFO violation: {message}")
            return False, message

        # If can proceed, continue with warning
        logger.info(f"Operation allowed to proceed with warning: {message}")
        return True, message

    logger.info("FIFO validation passed successfully")
    return True, None
