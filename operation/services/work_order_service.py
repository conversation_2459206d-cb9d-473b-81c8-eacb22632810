from typing import Dict
import pandas as pd
from django.db import transaction
from django.contrib.auth import get_user_model
from datetime import datetime
from core.services.import_service import ExcelImportService
from core.utils import get_column_value

User = get_user_model()


class WorkOrderImportService(ExcelImportService):
    """
    Service for importing work orders from Excel files
    """

    def __init__(self, validator, user=None):
        super().__init__(validator)
        self.user = user

    def _import_sheet_data(self, df: pd.DataFrame, config):
        """
        Override the base method to handle work order specific import logic
        """
        records = []
        for _, row in df.iterrows():
            record = self._transform_work_order_record(row)
            if record:
                records.append(record)

        if records:
            with transaction.atomic():
                # Use bulk_create for efficiency
                WorkOrder = config.model_class
                WorkOrder.objects.bulk_create(records)

        return len(records)

    def _transform_work_order_record(self, row: pd.Series) -> Dict:
        """
        Transform an Excel row into a WorkOrder object
        """
        from ..models import WorkOrder

        line = get_column_value(row, 'Line', 'SMT')
        part_no = get_column_value(row, 'Part no')
        customer = get_column_value(row, 'Customer')
        order_no = get_column_value(row, 'Order no')

        if not all([part_no, customer, order_no]):
            return None

        order_date_val = get_column_value(row, 'Date')
        if pd.isna(order_date_val):
            return None

        # Convert to date if it's a datetime object
        order_date = order_date_val.date() if isinstance(order_date_val, (datetime, pd.Timestamp)) else None
        if not order_date:
            return None

        # Get optional fields with defaults
        cf = get_column_value(row, 'c/f')
        cf = int(cf) if pd.notna(cf) and str(cf).strip() else None

        plan = get_column_value(row, 'Plan')
        plan = int(plan) if pd.notna(plan) and str(plan).strip() else None

        actual = get_column_value(row, 'Actual')
        actual = int(actual) if pd.notna(actual) and str(actual).strip() else None

        work_order = WorkOrder(
            line=line,
            part_no=part_no,
            customer=customer,
            order_date=order_date,
            order_no=order_no,
            cf=cf,
            plan=plan,
            actual=actual,
            created_by=self.user
        )

        return work_order
