import logging
from abc import ABC, abstractmethod
from datetime import datetime
from django.utils import timezone as django_timezone
from catalog.models import Product
from workflow_config.models import RoutingExecution, Routing
from operation.models import FIFOViolationLog
from operation.services.serial_number_service import parse_serial_number

# Set up logger
logger = logging.getLogger('fifo_validation')


class FIFOStrategy(ABC):
    """Abstract base class for FIFO strategies"""

    @abstractmethod
    def validate_fifo(self, serial_number, event_data, routing_execution):
        """
        Validate if an event complies with FIFO rules

        Args:
            serial_number: The serial number being processed
            event_data: The event data being validated
            routing_execution: The current routing execution for this serial number

        Returns:
            (bool, str): (is_valid, violation_reason)
        """
        pass


class DefaultFIFOStrategy(FIFOStrategy):
    """Default FIFO strategy - always valid"""

    def validate_fifo(self, serial_number, event_data, routing_execution):
        """Default implementation always returns valid"""
        # Parameters are unused but required by the interface
        _ = serial_number, event_data, routing_execution
        return True, None


class SolderPasteFIFOStrategy(FIFOStrategy):
    """FIFO strategy for solder paste products"""

    def validate_fifo(self, serial_number, event_data, routing_execution):
        """
        Validate if a solder paste event complies with FIFO rules
        Only considers process blocks that have event_required: true
        """
        logger.info(f"Starting FIFO validation for serial number: {serial_number}")
        logger.debug(f"Event data: {event_data}")
        logger.debug(f"Routing execution: {routing_execution}")

        try:
            # Get current process block from event data
            form_id = event_data.get('form')
            logger.info(f"Form ID: {form_id}")
            if not form_id:
                logger.warning("Form ID is missing")
                return False, "Form ID is required for FIFO validation"

            from workflow_config.services.routing_validation_service import RoutingValidationService
            current_process_block, _ = RoutingValidationService.get_process_block_from_form(form_id)
            logger.info(f"Current process block: {current_process_block}")
            if not current_process_block:
                logger.warning(f"Process block not found for form ID: {form_id}")
                return False, f"Process block not found for form ID: {form_id}"

            # Get product from serial number
            try:
                product_code = parse_serial_number(serial_number).get('part_code')
                logger.info(f"Product code from serial number: {product_code}")
                product = Product.objects.get(code=product_code)
                logger.info(f"Product found: {product.code} - {product.name}")
                logger.info(f"FIFO enabled: {product.fifo_enabled}, Strict enforcement: {product.fifo_strict_enforcement}")
            except (ValueError, Product.DoesNotExist) as e:
                logger.warning(f"Invalid product or serial number: {str(e)}")
                return False, f"Invalid product or serial number: {str(e)}"

            # Get routing for this product
            routing = Routing.objects.filter(products=product).first()
            logger.info(f"Routing found: {routing}")
            if not routing:
                logger.warning(f"No routing found for product: {product.code}")
                return False, f"No routing found for product: {product.code}"

            # Determine current stage in the routing (only considering event-required process blocks)
            current_stage = self._get_current_stage(routing_execution, current_process_block.code, routing)
            logger.info(f"Current stage determined: {current_stage}")

            # If current_stage is -1, it means the process block doesn't require events
            # or is not part of the routing
            if current_stage < 0:
                # Check if the process block is in the routing but doesn't require events
                _, _, components = self._get_routing_elements(routing)
                component = components.get(current_process_block.code, {})
                event_required = component.get('event_required', False)

                if component and not event_required:
                    logger.info(f"Process block {current_process_block.code} is in the routing but doesn't require events, skipping FIFO validation")
                    return True, None
                else:
                    logger.warning(f"Process block {current_process_block.code} is not part of the routing for this product")
                    return False, f"Process block {current_process_block.code} is not part of the routing for this product"

            # For first stage, always valid
            if current_stage == 0:
                logger.info("First stage detected, FIFO validation passed")
                return True, None

            # Check time constraints
            logger.info(f"Checking time constraints for stage {current_stage} and process block {current_process_block.code}")
            time_valid, time_reason = self._check_time_constraints(
                routing_execution,
                current_stage,
                event_data.get('timestamp', django_timezone.now()),
                product,
                routing
            )
            logger.info(f"Time constraints check result: {time_valid}, Reason: {time_reason}")
            if not time_valid:
                logger.warning(f"Time constraint violation: {time_reason}")
                return False, time_reason

            # Check FIFO order
            logger.info(f"Checking FIFO order for stage {current_stage}")
            fifo_valid, fifo_reason = self._check_fifo_order_db(
                routing_execution,
                current_stage,
                product
            )
            logger.info(f"FIFO order check result: {fifo_valid}, Reason: {fifo_reason}")
            if not fifo_valid:
                logger.warning(f"FIFO order violation: {fifo_reason}")
                return False, fifo_reason

            logger.info("FIFO validation passed successfully")
            return True, None

        except Exception as e:
            logger.error(f"Error in FIFO validation: {str(e)}", exc_info=True)
            return False, f"Error validating FIFO rules: {str(e)}"

    def _get_routing_elements(self, routing):
        """
        Helper method to extract routing elements from the routing schema

        Args:
            routing: The routing object

        Returns:
            tuple: (schema, route, components)
        """
        schema = routing.schema.get('routing_schema', {})
        route = schema.get('route', {})
        components = schema.get('components', {})
        logger.debug(f"Routing schema: {schema}")
        logger.debug(f"Route: {route}")
        logger.debug(f"Components: {components}")
        return schema, route, components

    def _get_current_stage(self, routing_execution, process_block, routing):
        """
        Determine the current stage in the routing sequence
        Only considers process blocks that have event_required: true

        Returns:
            int: The index of the current stage (0 for first stage)
        """
        logger.info(f"Determining current stage for process block: {process_block}")

        # Get the routing schema elements
        _, route, components = self._get_routing_elements(routing)

        # Check if the current process block requires events
        component = components.get(process_block, {})
        event_required = component.get('event_required', False)
        logger.debug(f"Current process block {process_block} event_required: {event_required}")

        if not event_required:
            logger.warning(f"Process block {process_block} does not require events")
            # If the process block doesn't require events, we need to find the previous
            # process block in the sequence that does require events
            # For now, we'll just return -1 to indicate it's not a valid event-requiring process block
            return -1

        # Get the sequence of process blocks from the routing (only those that require events)
        sequence = self._extract_process_sequence(route, components)
        logger.info(f"Event-required process block sequence from routing: {sequence}")

        # Find the index of the current process block in the event-required sequence
        try:
            stage = sequence.index(process_block)
            logger.info(f"Found process block at stage {stage} in event-required sequence")
            return stage
        except ValueError:
            logger.warning(f"Process block {process_block} not found in event-required sequence {sequence}")
            # If not found, check if this is a new execution
            if not routing_execution or not routing_execution.executed_sequence:
                logger.info("No routing execution or executed sequence, assuming first stage")
                return 0
            logger.warning("Process block not in event-required sequence, returning -1")
            return -1

    def _extract_process_sequence(self, route, components=None):
        """
        Extract the sequence of process blocks from the routing schema
        Only includes process blocks that have event_required: true

        Args:
            route: The route object from the routing schema
            components: The components object from the routing schema (optional)

        Returns:
            list: Ordered list of process block codes that require events
        """
        logger.info("Extracting process sequence from routing")

        # Get connections from the route
        connections = route.get('connections', {})
        logger.debug(f"Connections from routing: {connections}")

        # Start with the entry point
        all_sequence = []
        event_required_sequence = []
        current = route.get('start')
        logger.info(f"Entry point: {current}")

        # Traverse the route until we reach the end
        visited = set()
        while current and current not in visited and current != 'end':
            logger.debug(f"Processing process block: {current}")
            all_sequence.append(current)
            visited.add(current)

            # Check if this process block requires events
            if components is not None:
                component = components.get(current, {})
                event_required = component.get('event_required', False)
                logger.debug(f"Process block {current} event_required: {event_required}")
            else:
                # If components not provided, assume all blocks require events
                event_required = True
                logger.debug(f"Components not provided, assuming process block {current} requires events")

            if event_required:
                logger.debug(f"Adding process block to event sequence: {current}")
                event_required_sequence.append(current)

            # Get the next node
            connection = connections.get(current, {})
            towards = connection.get('towards', {})
            next_node = towards.get('default')
            logger.debug(f"Next node from {current}: {next_node}")
            current = next_node

            # Stop if we reach the end
            if current == 'end':
                logger.debug("Reached end of routing")
                break

        logger.info(f"All process blocks in sequence: {all_sequence}")
        logger.info(f"Event-required process blocks in sequence: {event_required_sequence}")
        return event_required_sequence

    def _check_time_constraints(self, routing_execution, current_stage, timestamp, product, routing):
        """
        Check if the event meets time constraints between stages
        Only considers process blocks that have event_required: true

        Args:
            routing_execution: The routing execution object
            current_stage: The current stage index (0-based)
            timestamp: The timestamp of the current event
            product: The product object
            routing: The routing object (optional)

        Returns:
            (bool, str): (is_valid, violation_reason)
        """
        logger.info(f"Checking time constraints for stage {current_stage}")
        try:
            # Skip check for invalid stage (not in event-required sequence)
            if current_stage < 0:
                logger.warning("Invalid stage (not in event-required sequence), skipping time constraint check")
                return True, None

            # Get the executed sequence
            if not routing_execution:
                logger.info("No routing execution, skipping time constraint check")
                return True, None

            executed_sequence = routing_execution.executed_sequence
            logger.debug(f"Executed sequence: {executed_sequence}")

            # If this is the first stage or we don't have previous stages, no constraints to check
            if current_stage == 0 or not executed_sequence:
                logger.info("First stage or no executed sequence, skipping time constraint check")
                return True, None

            # Get the previous stage execution
            prev_stage_idx = current_stage - 1
            logger.info(f"Previous stage index: {prev_stage_idx}")
            if prev_stage_idx >= len(executed_sequence):
                logger.warning(f"Previous stage index {prev_stage_idx} exceeds executed sequence length {len(executed_sequence)}")
                return True, None  # Can't validate

            prev_execution = executed_sequence[prev_stage_idx]
            logger.debug(f"Previous execution step: {prev_execution}")
            prev_time = prev_execution.get('time')
            logger.info(f"Previous stage timestamp: {prev_time}")

            if not prev_time:
                logger.warning("No timestamp in previous stage execution")
                return True, None  # Can't validate without previous time

            # Convert string time to datetime if needed
            if isinstance(prev_time, str):
                try:
                    prev_time = datetime.fromisoformat(prev_time.replace('Z', '+00:00'))
                    logger.debug(f"Converted previous time to datetime: {prev_time}")
                except ValueError as e:
                    logger.error(f"Invalid timestamp format in previous stage data: {e}")
                    return False, "Invalid timestamp format in previous stage data"

            # Ensure timestamp is a datetime object
            logger.info(f"Current event timestamp: {timestamp}, type: {type(timestamp)}")
            if isinstance(timestamp, str):
                try:
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    logger.debug(f"Converted current timestamp to datetime: {timestamp}")
                except ValueError as e:
                    logger.error(f"Invalid timestamp format in current event: {e}")
                    return False, "Invalid timestamp format in current event"

            # Check if trying to submit an event for a stage that's already been processed
            # This prevents duplicate submissions for the same stage
            if current_stage < len(executed_sequence):
                logger.warning(f"Event for stage {current_stage + 1} has already been processed. Cannot resubmit.")
                return False, f"Event for stage {current_stage + 1} has already been processed. Cannot resubmit."

            # Calculate time difference in minutes
            time_diff = (timestamp - prev_time).total_seconds() / 60
            logger.info(f"Time difference between stages: {time_diff:.1f} minutes")

            # Get the current process block from the executed sequence or event data
            current_process_block = None
            if current_stage < len(executed_sequence):
                current_process_block = executed_sequence[current_stage].get('executed')
            else:
                # If we're adding a new stage, get the process block from the event data
                # This would typically be passed in from the validate_fifo method
                current_process_block = routing_execution.next_executable

            logger.info(f"Current process block: {current_process_block}")

            # Get the previous process block
            prev_process_block = executed_sequence[prev_stage_idx].get('executed')
            logger.info(f"Previous process block: {prev_process_block}")

            # Use the routing passed as a parameter
            if not routing:
                logger.warning(f"No routing provided for product {product.code}")
                # Try to get the routing if not provided
                from workflow_config.models import Routing
                routing = Routing.objects.filter(products=product).first()

                if not routing:
                    logger.warning(f"No routing found for product {product.code}")
                    return True, None

            logger.info(f"Routing before getting time constraints: {routing}")
            min_time, max_time = self._get_time_constraints(current_process_block, routing)
            logger.info(f"Time constraints for process block {current_process_block}: min={min_time}, max={max_time}")

            if min_time and time_diff < min_time:
                logger.warning(f"Minimum time not met: required {min_time} min, actual {time_diff:.1f} min")
                return False, f"Minimum time between process blocks {prev_process_block} and {current_process_block} not met. Required: {min_time} min, Actual: {time_diff:.1f} min"

            if max_time and time_diff > max_time:
                logger.warning(f"Maximum time exceeded: limit {max_time} min, actual {time_diff:.1f} min")
                return False, f"Maximum time between process blocks {prev_process_block} and {current_process_block} exceeded. Limit: {max_time} min, Actual: {time_diff:.1f} min"

            logger.info("Time constraints check passed")
            return True, None

        except Exception as e:
            # Catch any unexpected errors and return a meaningful message
            logger.error(f"Error in time constraints check: {str(e)}", exc_info=True)
            return False, f"Error validating time constraints: {str(e)}"

    def _get_time_constraints(self, current_process_block, routing):
        """
        Get the min/max time constraints from the routing schema's fifo_config

        Args:
            current_process_block: The current process block code
            routing: The routing object

        Returns:
            (int, int): (min_time, max_time) in minutes
        """
        logger.info(f"Getting time constraints for process block {current_process_block}")

        _, _, components = self._get_routing_elements(routing)

        current_block_config = components.get(current_process_block, {})
        fifo_config = current_block_config.get('fifo_config', {})
        delay_time = fifo_config.get('delay_time', {})
        min_time = delay_time.get('min', None)
        max_time = delay_time.get('max', None)

        logger.info(f"Time constraints for process block {current_process_block}: min={min_time}, max={max_time}")
        return min_time, max_time

    def _check_fifo_order_db(self, routing_execution, current_stage, product):
        """
        Check if the event maintains FIFO order using database filtering
        Only considers process blocks that have event_required: true

        This method uses database queries for filtering instead of in-memory processing
        for better performance with large datasets.

        Returns:
            (bool, str): (is_valid, violation_reason)
        """
        logger.info(f"Checking FIFO order using DB filtering for stage {current_stage}")

        # Skip check if FIFO not enabled for this product
        if not product.fifo_enabled:
            logger.info(f"FIFO not enabled for product {product.code}, skipping check")
            return True, None

        # Skip check for first stage
        if current_stage == 0:
            logger.info("First stage, skipping FIFO order check")
            return True, None

        # Skip check for invalid stage (not in event-required sequence)
        if current_stage < 0:
            logger.warning("Invalid stage (not in event-required sequence), skipping FIFO order check")
            return True, None

        # Get the current serial number
        if not routing_execution:
            logger.warning("No routing execution, skipping FIFO order check")
            return True, None

        serial_number = routing_execution.serial_number
        logger.info(f"Checking FIFO order for serial number: {serial_number}")

        # Get the timestamp when the current bottle entered the system (first stage)
        if not routing_execution.executed_sequence or len(routing_execution.executed_sequence) == 0:
            logger.warning("No executed sequence for current bottle, skipping FIFO order check")
            return True, None  # Can't validate without execution history

        current_first_step = routing_execution.executed_sequence[0]
        current_first_time_str = current_first_step.get('time')
        logger.debug(f"Current bottle first step: {current_first_step}")

        if not current_first_time_str:
            logger.warning("No timestamp in first step of current bottle, skipping FIFO order check")
            return True, None  # Can't validate without timestamp

        # Convert string time to datetime
        try:
            if isinstance(current_first_time_str, str):
                current_first_time = datetime.fromisoformat(current_first_time_str.replace('Z', '+00:00'))
                logger.debug(f"Converted current bottle first time to datetime: {current_first_time}")
            else:
                current_first_time = current_first_time_str
                logger.debug(f"Current bottle first time already datetime: {current_first_time}")
        except ValueError as e:
            logger.error(f"Invalid timestamp format in current bottle first step: {e}")
            return True, None  # Can't validate with invalid time format

        # Use raw SQL to filter bottles that entered before current bottle and are at earlier stages
        # We'll also filter out bottles that have completed their journey for better performance
        from django.db import connection

        # Define the main query with <= operator for stage comparison
        # This is the correct approach for FIFO validation based on our testing
        # Filter out bottles that have completed their journey (where next_executable is NULL OR empty)
        # So we keep only bottles where next_executable is NOT NULL AND NOT empty
        query = """
        SELECT re.id, re.serial_number, re.executed_sequence
        FROM mes_routing_execution re
        WHERE re.product_id = %s
        AND re.serial_number != %s
        AND (re.executed_sequence->0->>'time')::timestamp < %s
        AND jsonb_array_length(re.executed_sequence) <= %s
        AND (re.next_executable IS NOT NULL AND re.next_executable != '')
        ORDER BY (re.executed_sequence->0->>'time')::timestamp ASC
        """

        # Log the query for debugging
        logger.debug(f"Executing SQL query: {query}")

        # Execute the query
        with connection.cursor() as cursor:
            logger.debug(f"Executing SQL query with parameters: {product.id}, {serial_number}, {current_first_time}, {current_stage}")
            try:
                # Log the data types of parameters for debugging
                logger.debug(f"Parameter types: product.id={type(product.id)}, serial_number={type(serial_number)}, current_first_time={type(current_first_time)}, current_stage={type(current_stage)}")

                # Ensure current_stage is an integer
                current_stage_int = int(current_stage)
                logger.debug(f"Converted current_stage to int: {current_stage_int}")

                # Make current_first_time timezone-naive if it's timezone-aware
                # This is because PostgreSQL timestamp without timezone can't be compared with timezone-aware Python datetime
                if current_first_time.tzinfo is not None:
                    # Keep the local time but remove timezone info
                    # Don't convert to UTC as that changes the actual time value
                    current_first_time_naive = current_first_time.replace(tzinfo=None)
                    logger.debug(f"Converted current_first_time to naive (keeping local time): {current_first_time_naive}")
                else:
                    current_first_time_naive = current_first_time
                    logger.debug(f"current_first_time is already naive: {current_first_time_naive}")

                # Execute the query
                cursor.execute(query, [product.id, serial_number, current_first_time_naive, current_stage_int])
                earlier_bottles_data = cursor.fetchall()
                logger.debug(f"Query returned {len(earlier_bottles_data)} rows")
                for i, row in enumerate(earlier_bottles_data):
                    logger.debug(f"Row {i+1}: id={row[0]}, serial_number={row[1]}, sequence_length={len(row[2]) if row[2] else 0}")
            except Exception as e:
                logger.error(f"Error executing SQL query: {str(e)}", exc_info=True)
                earlier_bottles_data = []

        logger.info(f"Found {len(earlier_bottles_data)} bottles that entered before current bottle and are at earlier stages")

        # If no earlier bottles found, no FIFO violation
        if not earlier_bottles_data:
            logger.info("No FIFO violation detected")
            return True, None

        # Get the earliest bottle (first in the result set due to ORDER BY)
        earliest_bottle_data = earlier_bottles_data[0]
        earliest_bottle_sn = earliest_bottle_data[1]
        earliest_bottle_sequence = earliest_bottle_data[2]

        # Calculate the stage of the earliest bottle
        try:
            if isinstance(earliest_bottle_sequence, str):
                # If it's a string, try to parse it as JSON
                import json
                earliest_bottle_sequence = json.loads(earliest_bottle_sequence)
                logger.debug(f"Parsed earliest bottle sequence from string: {earliest_bottle_sequence}")
            elif isinstance(earliest_bottle_sequence, dict):
                # If it's a dict, convert to list
                earliest_bottle_sequence = [earliest_bottle_sequence]
                logger.debug(f"Converted earliest bottle sequence from dict to list: {earliest_bottle_sequence}")

            logger.debug(f"Earliest bottle sequence type: {type(earliest_bottle_sequence)}, value: {earliest_bottle_sequence}")
            earliest_bottle_stage = len(earliest_bottle_sequence) - 1
            logger.debug(f"Calculated earliest bottle stage: {earliest_bottle_stage}")
        except Exception as e:
            logger.error(f"Error calculating earliest bottle stage: {str(e)}", exc_info=True)
            earliest_bottle_stage = 0

        # Create violation message
        violation_msg = f"FIFO violation: {earliest_bottle_sn} (at stage {earliest_bottle_stage+1}) should be processed to stage {current_stage+1} before {serial_number}"
        logger.warning(violation_msg)
        return False, violation_msg

    def _check_fifo_order(self, routing_execution, current_stage, product):
        """
        Check if the event maintains FIFO order
        Only considers process blocks that have event_required: true

        Returns:
            (bool, str): (is_valid, violation_reason)
        """
        logger.info(f"Checking FIFO order for stage {current_stage}")

        # Skip check if FIFO not enabled for this product
        if not product.fifo_enabled:
            logger.info(f"FIFO not enabled for product {product.code}, skipping check")
            return True, None

        # Skip check for first stage
        if current_stage == 0:
            logger.info("First stage, skipping FIFO order check")
            return True, None

        # Skip check for invalid stage (not in event-required sequence)
        if current_stage < 0:
            logger.warning("Invalid stage (not in event-required sequence), skipping FIFO order check")
            return True, None

        # Get the current serial number
        if not routing_execution:
            logger.warning("No routing execution, skipping FIFO order check")
            return True, None

        serial_number = routing_execution.serial_number
        logger.info(f"Checking FIFO order for serial number: {serial_number}")

        # Find all routing executions for this product
        # Also filter out bottles that have completed their journey for better performance
        # In Django ORM, we need to use Q objects to combine conditions with OR
        from django.db.models import Q

        # Create a filter for bottles that have not completed their journey
        # Filter out bottles where next_executable is NULL OR empty
        # So we keep only bottles where next_executable is NOT NULL AND NOT empty
        active_bottles_filter = ~Q(next_executable__isnull=True) & ~Q(next_executable="")

        product_executions = RoutingExecution.objects.filter(
            product=product
        ).filter(
            active_bottles_filter  # Only include bottles that have not completed their journey
        ).exclude(
            serial_number=serial_number
        )

        # Get all bottles for this product
        all_bottles = list(product_executions)
        logger.info(f"Found {len(all_bottles)} other bottles for product {product.code}")

        # If there are no other bottles, no FIFO violation is possible
        if not all_bottles:
            logger.info("No other bottles found, no FIFO violation possible")
            return True, None

        # Get the timestamp when the current bottle entered the system (first stage)
        if not routing_execution.executed_sequence or len(routing_execution.executed_sequence) == 0:
            logger.warning("No executed sequence for current bottle, skipping FIFO order check")
            return True, None  # Can't validate without execution history

        current_first_step = routing_execution.executed_sequence[0]
        current_first_time_str = current_first_step.get('time')
        logger.debug(f"Current bottle first step: {current_first_step}")

        if not current_first_time_str:
            logger.warning("No timestamp in first step of current bottle, skipping FIFO order check")
            return True, None  # Can't validate without timestamp

        # Convert string time to datetime
        try:
            if isinstance(current_first_time_str, str):
                current_first_time = datetime.fromisoformat(current_first_time_str.replace('Z', '+00:00'))
                logger.debug(f"Converted current bottle first time to datetime: {current_first_time}")
            else:
                current_first_time = current_first_time_str
                logger.debug(f"Current bottle first time already datetime: {current_first_time}")
        except ValueError as e:
            logger.error(f"Invalid timestamp format in current bottle first step: {e}")
            return True, None  # Can't validate with invalid time format

        # Find bottles that entered the system before the current bottle
        earlier_bottles = []
        logger.info("Checking for bottles that entered the system before the current bottle")

        for execution in all_bottles:
            execution_sn = execution.serial_number
            logger.debug(f"Checking bottle: {execution_sn}")

            # Skip if no execution history
            if not execution.executed_sequence or len(execution.executed_sequence) == 0:
                logger.debug(f"Bottle {execution_sn} has no execution history, skipping")
                continue

            # Get the timestamp when this bottle entered the system
            first_step = execution.executed_sequence[0]
            first_time_str = first_step.get('time')
            logger.debug(f"Bottle {execution_sn} first step: {first_step}")

            if not first_time_str:
                logger.debug(f"Bottle {execution_sn} has no timestamp in first step, skipping")
                continue

            # Convert string time to datetime
            try:
                if isinstance(first_time_str, str):
                    first_time = datetime.fromisoformat(first_time_str.replace('Z', '+00:00'))
                    logger.debug(f"Converted bottle {execution_sn} first time to datetime: {first_time}")
                else:
                    first_time = first_time_str
                    logger.debug(f"Bottle {execution_sn} first time already datetime: {first_time}")
            except ValueError as e:
                logger.error(f"Invalid timestamp format in bottle {execution_sn} first step: {e}")
                continue

            # If this bottle entered the system before the current bottle
            if first_time < current_first_time:
                logger.info(f"Bottle {execution_sn} entered before current bottle ({first_time} < {current_first_time})")

                # Check if this bottle is at an earlier stage than the current bottle wants to be
                execution_stage = len(execution.executed_sequence) - 1
                logger.info(f"Bottle {execution_sn} is at stage {execution_stage+1}, current bottle wants to go to stage {current_stage+1}")

                if execution_stage < current_stage:
                    logger.warning(f"FIFO violation detected: Bottle {execution_sn} is at stage {execution_stage+1} but should be at stage {current_stage+1} before current bottle")
                    # This bottle entered earlier but is at an earlier stage,
                    # so the current bottle is trying to skip ahead - FIFO violation
                    earlier_bottles.append((execution, first_time, execution_stage))
                else:
                    logger.debug(f"Bottle {execution_sn} is at appropriate stage, no FIFO violation")
            else:
                logger.debug(f"Bottle {execution_sn} entered after current bottle, no FIFO violation")

        if earlier_bottles:
            logger.info(f"Found {len(earlier_bottles)} bottles that should be processed before the current bottle")
            # Sort by stage (lowest first) and then by time (oldest first)
            earlier_bottles.sort(key=lambda x: (x[2], x[1]))
            earliest_bottle, _, stage = earlier_bottles[0]
            violation_msg = f"FIFO violation: {earliest_bottle.serial_number} (at stage {stage+1}) should be processed to stage {current_stage+1} before {serial_number}"
            logger.warning(violation_msg)
            return False, violation_msg

        logger.info("FIFO order check passed successfully")
        return True, None

    def benchmark_fifo_order_checks(self, routing_execution, current_stage, product, iterations=10):
        """
        Benchmark both FIFO order check methods and compare their performance

        Args:
            routing_execution: The routing execution to check
            current_stage: The current stage to check
            product: The product to check
            iterations: Number of iterations to run for each method

        Returns:
            dict: Benchmark results with average execution times
        """
        import time

        # Benchmark in-memory approach
        in_memory_times = []
        in_memory_results = None

        for _ in range(iterations):
            start_time = time.time()
            result = self._check_fifo_order(routing_execution, current_stage, product)
            end_time = time.time()
            in_memory_times.append(end_time - start_time)
            if in_memory_results is None:
                in_memory_results = result

        # Benchmark DB approach
        db_times = []
        db_results = None

        for _ in range(iterations):
            start_time = time.time()
            result = self._check_fifo_order_db(routing_execution, current_stage, product)
            end_time = time.time()
            db_times.append(end_time - start_time)
            if db_results is None:
                db_results = result

        # Calculate averages
        avg_in_memory = sum(in_memory_times) / len(in_memory_times)
        avg_db = sum(db_times) / len(db_times)

        # Prepare results
        results = {
            "in_memory": {
                "average_time": avg_in_memory,
                "times": in_memory_times,
                "result": in_memory_results
            },
            "db": {
                "average_time": avg_db,
                "times": db_times,
                "result": db_results
            },
            "comparison": {
                "faster_method": "in_memory" if avg_in_memory < avg_db else "db",
                "speedup_factor": avg_in_memory / avg_db if avg_db < avg_in_memory else avg_db / avg_in_memory,
                "same_result": in_memory_results == db_results
            }
        }

        # Log results
        logger.info(f"Benchmark results:")
        logger.info(f"In-memory approach: {avg_in_memory:.6f} seconds average")
        logger.info(f"DB approach: {avg_db:.6f} seconds average")
        logger.info(f"Faster method: {results['comparison']['faster_method']}")
        logger.info(f"Speedup factor: {results['comparison']['speedup_factor']:.2f}x")
        logger.info(f"Same result: {results['comparison']['same_result']}")

        return results


class FIFOStrategyFactory:
    """Factory for creating FIFO strategies based on product type"""

    _strategies = {
        'solder_paste': SolderPasteFIFOStrategy(),
        'default': DefaultFIFOStrategy(),
    }

    @classmethod
    def get_strategy(cls, product):
        """
        Get the appropriate FIFO strategy for a product

        Args:
            product: The product to get a strategy for

        Returns:
            FIFOStrategy: The appropriate FIFO strategy
        """
        logger.info(f"FIFOStrategyFactory: Getting strategy for product: {product.code}")
        product_type = getattr(product, 'type_id', 'default')
        logger.info(f"FIFOStrategyFactory: Product type: {product_type}")

        strategy = cls._strategies.get(product_type, cls._strategies.get('default'))
        logger.info(f"FIFOStrategyFactory: Selected strategy: {strategy.__class__.__name__}")

        return strategy

    @classmethod
    def register_strategy(cls, product_type, strategy):
        """
        Register a new FIFO strategy

        Args:
            product_type: The product type to register the strategy for
            strategy: The strategy to register
        """
        logger.info(f"FIFOStrategyFactory: Registering new strategy for product type: {product_type}")
        logger.info(f"FIFOStrategyFactory: Strategy class: {strategy.__class__.__name__}")
        cls._strategies[product_type] = strategy
        logger.info(f"FIFOStrategyFactory: Strategy registered successfully")


class FIFOService:
    """
    Service for managing FIFO operations
    """

    @classmethod
    def validate_fifo(cls, serial_number, event_data):
        """
        Validate if an event complies with FIFO rules

        Args:
            serial_number: The serial number being processed
            event_data: The event data being validated

        Returns:
            (bool, str): (is_valid, violation_reason)
        """
        logger.info(f"FIFOService: Starting FIFO validation for serial number: {serial_number}")
        logger.debug(f"FIFOService: Event data: {event_data}")

        try:
            # Get product from serial number
            product_code = parse_serial_number(serial_number).get('part_code')
            logger.info(f"FIFOService: Product code from serial number: {product_code}")

            product = Product.objects.get(code=product_code)
            logger.info(f"FIFOService: Product found: {product.code} - {product.name}")
            logger.info(f"FIFOService: FIFO enabled: {product.fifo_enabled}, Strict enforcement: {product.fifo_strict_enforcement}")

            # Skip validation if FIFO not enabled for this product
            if not product.fifo_enabled:
                logger.info(f"FIFOService: FIFO not enabled for product {product.code}, skipping validation")
                return True, None

            # Get routing execution for this serial number
            routing_execution = RoutingExecution.objects.filter(serial_number=serial_number).first()
            logger.info(f"FIFOService: Routing execution found: {routing_execution is not None}")

            # Get the appropriate FIFO strategy for this product
            strategy = FIFOStrategyFactory.get_strategy(product)
            logger.info(f"FIFOService: Using strategy: {strategy.__class__.__name__}")

            # Validate FIFO rules
            is_valid, reason = strategy.validate_fifo(serial_number, event_data, routing_execution)
            logger.info(f"FIFOService: Validation result: {is_valid}, Reason: {reason}")

            return is_valid, reason

        except (Product.DoesNotExist, ValueError) as e:
            # If we can't determine the product, default to allowing the operation
            logger.error(f"FIFOService: Error determining product: {str(e)}")
            return True, f"Warning: Could not validate FIFO rules. {str(e)}"

    @classmethod
    def handle_fifo_violation(cls, event, serial_number, violation_reason):
        """
        Handle a FIFO violation based on product configuration

        Args:
            event: The event that caused the violation
            serial_number: The serial number being processed
            violation_reason: The reason for the violation

        Returns:
            (bool, str): (can_proceed, message)
        """
        logger.info(f"FIFOService: Handling FIFO violation for serial number: {serial_number}")
        logger.info(f"FIFOService: Violation reason: {violation_reason}")
        logger.debug(f"FIFOService: Event: {event}")

        try:
            # Get product from serial number
            product_code = parse_serial_number(serial_number).get('part_code')
            logger.info(f"FIFOService: Product code from serial number: {product_code}")

            product = Product.objects.get(code=product_code)
            logger.info(f"FIFOService: Product found: {product.code} - {product.name}")
            logger.info(f"FIFOService: FIFO strict enforcement: {product.fifo_strict_enforcement}")

            # Determine violation type
            if "Minimum time" in violation_reason:
                violation_type = "time_min"
                logger.info("FIFOService: Violation type: Minimum Time")
            elif "Maximum time" in violation_reason:
                violation_type = "time_max"
                logger.info("FIFOService: Violation type: Maximum Time")
            elif "FIFO violation" in violation_reason:
                violation_type = "fifo_order"
                logger.info("FIFOService: Violation type: FIFO Order")
            else:
                violation_type = "sequence"
                logger.info("FIFOService: Violation type: Sequence")

            # Log the violation
            violation_log = FIFOViolationLog.objects.create(
                event=event,
                serial_number=serial_number,
                product=product,
                violation_type=violation_type,
                violation_details={"reason": violation_reason}
            )
            logger.info(f"FIFOService: Created violation log entry with ID: {violation_log.id}")

            # Check if strict enforcement is enabled
            if product.fifo_strict_enforcement:
                message = f"FIFO violation: {violation_reason}. Operation blocked due to strict FIFO enforcement."
                logger.warning(f"FIFOService: {message}")
                return False, message

            # If not strict, allow with warning
            message = f"FIFO violation detected: {violation_reason}. Operation allowed to proceed."
            logger.warning(f"FIFOService: {message}")
            return True, message

        except (Product.DoesNotExist, ValueError) as e:
            # If we can't determine the product, default to allowing the operation
            logger.error(f"FIFOService: Error determining product: {str(e)}")
            return True, f"Warning: Could not validate FIFO rules. {str(e)}"

    @classmethod
    def benchmark_fifo_validation(cls, serial_number, event_data, iterations=1):
        """
        Benchmark both FIFO validation approaches (in-memory vs. database filtering)

        Args:
            serial_number: The serial number to validate
            event_data: The event data to validate
            iterations: Number of iterations to run for each method

        Returns:
            dict: Benchmark results with average execution times
        """
        logger.info(f"FIFOService: Starting FIFO validation benchmark for serial number: {serial_number}")

        try:
            # Get product from serial number
            product_code = parse_serial_number(serial_number).get('part_code')
            product = Product.objects.get(code=product_code)

            # Skip if FIFO not enabled
            if not product.fifo_enabled:
                return {"error": "FIFO not enabled for this product"}

            # Get routing execution
            routing_execution = RoutingExecution.objects.filter(serial_number=serial_number).first()
            if not routing_execution:
                return {"error": "No routing execution found for this serial number"}

            # Get current process block
            form_id = event_data.get('form')
            if not form_id:
                return {"error": "Form ID is required for FIFO validation"}

            from workflow_config.services.routing_validation_service import RoutingValidationService
            current_process_block, _ = RoutingValidationService.get_process_block_from_form(form_id)
            if not current_process_block:
                return {"error": f"Process block not found for form ID: {form_id}"}

            # Get routing
            routing = Routing.objects.filter(products=product).first()
            if not routing:
                return {"error": f"No routing found for product: {product.code}"}

            # Get strategy
            strategy = FIFOStrategyFactory.get_strategy(product)

            # Determine current stage
            current_stage = strategy._get_current_stage(routing_execution, current_process_block.code, routing)
            if current_stage < 0:
                return {"error": f"Process block {current_process_block.code} is not part of the routing for this product"}

            # Run benchmark
            return strategy.benchmark_fifo_order_checks(routing_execution, current_stage, product, iterations)

        except Exception as e:
            logger.error(f"FIFOService: Error in benchmark: {str(e)}", exc_info=True)
            return {"error": f"Error in benchmark: {str(e)}"}
