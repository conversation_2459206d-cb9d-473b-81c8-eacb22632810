from typing import Dict, List
from django.conf import settings
from workflow_config.models import FormConfig


class EventSchemaService:
    """Service for managing dynamic event schema configurations"""

    @staticmethod
    def _get_dynamic_fields_from_form(form_id: int = None) -> List[Dict]:
        """
        Extract fields marked with used_in_grid=true from form schema

        Args:
            form_id: Form configuration ID

        Returns:
            List of field configurations for grid display
        """
        if not form_id:
            return []

        try:
            form_config = FormConfig.objects.get(id=form_id)
            form_schema = form_config.form_schema
            dynamic_fields = []

            # Helper function to process nested fields
            def process_fields(fields, parent_key=''):
                field_configs = []
                for field in fields:
                    field_key = f"{parent_key}.{field['name']}" if parent_key else field['name']

                    # Check if field should be shown in grid
                    if field.get('used_in_grid', False):
                        field_config = {
                            'field': f"event_data.{field_key}",
                            'label': field.get('label', field['name']),
                            'type': field.get('type', 'string'),
                            'sortable': field.get('sortable', False),
                            'filterable': field.get('filterable', False),
                            'width': field.get('width', 120),
                            'unit': field.get('unit', '')
                        }
                        field_configs.append(field_config)

                    # Process nested fields if any
                    if 'fields' in field:
                        field_configs.extend(process_fields(field['fields'], field_key))

                return field_configs

            # Process form fields
            if 'form_fields' in form_schema:
                dynamic_fields = process_fields(form_schema['form_fields'])

            return dynamic_fields
        except FormConfig.DoesNotExist:
            return []
        except Exception as e:
            print(f"Error processing form schema: {str(e)}")
            return []

    @staticmethod
    def get_display_schema(form_id: int = None) -> Dict:
        """
        Get the display schema for events, optionally filtered by form_id.
        Schema includes field configurations for both fixed and dynamic fields.

        Args:
            form_id: Optional form ID to get specific schema

        Returns:
            Dict containing display configurations for event fields
        """
        # Fixed fields that are always shown
        base_fields = [
            {
                'field': 'id',
                'label': 'Event ID',
                'type': 'number',
                'sortable': True,
                'filterable': True,
                'width': 100
            },
            {
                'field': 'serial_number',
                'label': 'Serial Number',
                'type': 'string',
                'sortable': True,
                'filterable': True,
                'width': 200
            },
            {
                'field': 'inspection_status',
                'label': 'Status',
                'type': 'boolean',
                'sortable': True,
                'filterable': True,
                'width': 100
            },
            {
                'field': 'timestamp',
                'label': 'Submitted At',
                'type': 'datetime',
                'sortable': True,
                'filterable': True,
                'width': 150
            }
        ]

        # Get dynamic fields from form configuration
        dynamic_fields = EventSchemaService._get_dynamic_fields_from_form(form_id)

        return {
            'version': '1.0',
            'fields': base_fields + dynamic_fields,
            'defaultSort': {'field': 'created_at', 'direction': 'desc'},
            'grouping': [
                {
                    'field': 'form_id',
                    'label': 'Form'
                },
                {
                    'field': 'inspection_status',
                    'label': 'Status'
                }
            ],
            'actions': [
                {
                    'type': 'view',
                    'label': 'View Details',
                    'icon': 'eye'
                },
                {
                    'type': 'export',
                    'label': 'Export',
                    'icon': 'download'
                }
            ]
        }
