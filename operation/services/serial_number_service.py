import re
from typing import Dict, Any
from workflow_config.services.form_service import parse_serial_number as form_service_parse_serial_number

def parse_serial_number(serial_number: str) -> Dict[str, Any]:
    """
    Parse serial number in format: HE317171-35.12#D9241000010001
    Returns dict with:
    - part_code: everything before '#'
    - work_order: 7 digits before last 4 digits
    - board: last 4 digits as sequence number
    - validation_errors: list of validation errors if any
    
    This is a wrapper around the form_service.parse_serial_number function
    """
    return form_service_parse_serial_number(serial_number)
