from abc import ABC, abstractmethod
from rest_framework import status
from rest_framework.response import Response
from catalog.models import Product
from workflow_config.services.form_service import (
    parse_serial_number,
    FormConfig,
    FormService
)
from operation.services.event_transformers_plugin import get_plugin


class EventCreationStrategy(ABC):
    """Base strategy for event creation"""
    @abstractmethod
    def create_event(self, event_data: dict, serializer_class: type, perform_create_callback: callable) -> Response:
        pass


class StandardEventStrategy(EventCreationStrategy):
    """Strategy for creating standard events"""
    def create_event(self, event_data: dict, serializer_class: type, perform_create_callback: callable) -> Response:
        """Create a single event"""

        form_config = FormConfig.objects.get(id=event_data.get('form'))
        is_valid, validation_errors = FormService.validate_event_data(
            form_config, 
            event_data.get('event_data', {})
        )

        event_data['validation_status'] = 'valid' if is_valid else 'invalid'
        event_data['validation_errors'] = validation_errors

        serializer = serializer_class(data=event_data)
        serializer.is_valid(raise_exception=True)
        perform_create_callback(serializer)
        
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class CorrectedEventStrategy(EventCreationStrategy):
    """Strategy for creating corrected events (failed + success)"""
    def create_event(self, event_data: dict, serializer_class: type, perform_create_callback: callable) -> Response:
        """Create both failed and corrected events"""
        form_config = FormConfig.objects.get(id=event_data.get('form'))
        is_valid, validation_errors = FormService.validate_event_data(
            form_config, 
            event_data.get('event_data', {})
        )

        event_data['validation_status'] = 'valid' if is_valid else 'invalid'
        event_data['validation_errors'] = validation_errors

        failed_event = event_data.copy()
        failed_event['inspection_status'] = False
        failed_event['next_action'] = 'corrected'

        failed_serializer = serializer_class(data=failed_event)
        failed_serializer.is_valid(raise_exception=True)
        perform_create_callback(failed_serializer)

        corrected_event = event_data.copy()
        corrected_event['inspection_status'] = True
        corrected_event['next_action'] = 'main_forward'
        corrected_event['event_type'] = 'rework' # need to confirm whether it should be 'rework' or 'main' ?
        corrected_event_data = corrected_event['event_data'].copy()
        corrected_event_data['form_status'] = 'pass' # using the hardcoded value 'pass' for now
        corrected_event['event_data'] = corrected_event_data

        corrected_serializer = serializer_class(data=corrected_event)
        corrected_serializer.is_valid(raise_exception=True)
        perform_create_callback(corrected_serializer)

        return Response({
            'failed_event': failed_serializer.data,
            'corrected_event': corrected_serializer.data
        }, status=status.HTTP_201_CREATED)


class ManufacturingEventService:
    """Service layer for manufacturing event operations"""

    @staticmethod
    def get_event_strategy(event_data):
        """Factory method to get appropriate event creation strategy"""
        is_corrected = (
            event_data.get('inspection_status') is False and 
            event_data.get('next_action') == 'corrected'
        )
        return CorrectedEventStrategy() if is_corrected else StandardEventStrategy()

    @staticmethod
    def process_serial_number(serial_number):
        """Process and validate serial number"""
        if not serial_number:
            return None, ['Serial number is required']

        parsed_data = parse_serial_number(serial_number)
        validation_errors = parsed_data.pop('validation_errors', []) 

        try:
            product = Product.objects.get(code=parsed_data['part_code'])
        except Product.DoesNotExist:
            validation_errors.append(f'Product with code {parsed_data["part_code"]} not found')
            product = Product.objects.get(code='invalid_part_code')

        return {
            'product': product,
            'parsed_data': parsed_data,
            'validation_errors': validation_errors
        }

    @staticmethod
    def prepare_event_data(request_data, processed_data):
        """Prepare event data from request and processed serial number"""
        return {
            **request_data,
            'validation_errors': processed_data['validation_errors'],
            'product': processed_data['product'].id,
            'board': processed_data['parsed_data']['board'],
            'work_order': processed_data['parsed_data']['work_order']
        }

    @staticmethod
    def transform_event_data(raw_event_data: dict) -> list:
        """Transforms incoming event data using the dynamically loaded plugin(plugin architecture).

        Args:
            raw_event_data (dict): The raw event data from the request.

        Returns:
            list: The list of transformed events in canonical format.
        """
        form_id = raw_event_data.get('form')
        form_code = FormConfig.objects.get(id=form_id).code if form_id else 'default'
        transformer = get_plugin(form_code)
        return transformer.transform(raw_event_data)
