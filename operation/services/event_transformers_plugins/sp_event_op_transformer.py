from typing import List, <PERSON><PERSON>
from operation.services.event_transformers_plugin import BaseEventTransformerPlugin
from workflow_config.services.form_service import FormConfig, FormService


class SpEventTransformer(BaseEventTransformerPlugin):
    transformer_id = "sp_event_op_transformer"

    def create_serial_number(self, gr_number: str, board_number: int) -> str:
        return f'{gr_number}#{board_number}'
    
    def parse_serial_number(self, serial_number: str) -> Tuple[str, int]:
        gr_number, board_number = serial_number.split('#')
        return gr_number, int(board_number)

    def transform(self, event_data: dict) -> List[dict]:
        """Transforms incoming event data into a list of canonical event dictionaries."""
        form_config = FormConfig.objects.get(id=event_data['form'])
        is_valid, validation_errors = FormService.validate_event_data(
            form_config, event_data.get('event_data', {})
        )

        event_data['validation_status'] = 'valid' if is_valid else 'invalid'
        event_data['validation_errors'] = validation_errors

        if not event_data.get('serial_number'):
            event_data_obj = event_data['event_data']
            gr_number = event_data_obj.get('gr_number')
            box_number = int(event_data_obj.get('box_number', 1))
            serial_number = self.create_serial_number(gr_number, box_number)
            event_data['serial_number'] = serial_number
            event_data['board'] = box_number
        else:
            gr_number, box_number = self.parse_serial_number(event_data['serial_number'])
            event_data['board'] = box_number

        return [event_data]
