from typing import List, <PERSON><PERSON>
from operation.services.event_transformers_plugin import BaseEventTransformerPlugin
from workflow_config.services.form_service import FormConfig, FormService


class SpEventTransformer(BaseEventTransformerPlugin):
    transformer_id = "stencil_event_ip_transformer"

    def create_serial_number(self, stencil_part_number: str) -> str:
        """
        Serial number format: <stencil_part_number>#<board_number>
        each unique stencil_part_number has only one board so board_number is 1
        """
        board_number = 1
        return f'{stencil_part_number}#{board_number}'

    def parse_serial_number(self, serial_number: str) -> Tuple[str, int]:
        """return: (stencil_part_number, board_number)"""
        stencil_part_number, board_number = serial_number.split('#')
        return stencil_part_number, int(board_number)


    def transform(self, event_data: dict) -> List[dict]:
        """Transforms incoming event data into a list of canonical event dictionaries."""
        form_config = FormConfig.objects.get(id=event_data['form'])
        is_valid, validation_errors = FormService.validate_event_data(
            form_config, event_data.get('event_data', {})
        )

        event_data['validation_status'] = 'valid' if is_valid else 'invalid'
        event_data['validation_errors'] = validation_errors

        if not event_data.get('serial_number'):
            event_data_obj = event_data['event_data']
            stencil_part_number = event_data_obj.get('stencil_part_number')
            serial_number = self.create_serial_number(stencil_part_number,)
            _, board_number = self.parse_serial_number(serial_number)
            event_data['serial_number'] = serial_number
            event_data['board'] = board_number
        else:
            _, board_number = self.parse_serial_number(event_data['serial_number'])
            event_data['board'] = board_number

        return [event_data]
