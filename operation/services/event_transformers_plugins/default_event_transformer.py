from typing import List
from operation.services.event_service import ManufacturingEventService
from operation.services.event_transformers_plugin import BaseEventTransformerPlugin
from workflow_config.services.form_service import FormConfig, FormService
from workflow_config.services.routing_validation_service import RoutingValidationService


class DefaultTransformer(BaseEventTransformerPlugin):
    transformer_id = "default"

    def transform(self, event_data: dict) -> List[dict]:
        print("__inside default transform__")
        validation_errors = []
        # Process serial number
        processed_data = ManufacturingEventService.process_serial_number(
            event_data.get('serial_number')
        )
        print("__processed data:__", processed_data)
        if not processed_data:
            validation_errors.append({
                'field': 'event_data.serial_number',
                'error': 'Serial number processing failed',
                'value': event_data.get('serial_number')
            })

        print("__before prepare event data:__")  
        # Prepare event data
        event_data = ManufacturingEventService.prepare_event_data(
            event_data, 
            processed_data
        )
        print("__after prepare event data:__", event_data)
        # Form validation
        form_config = FormConfig.objects.get(id=event_data.get('form'))
        is_valid, errors = FormService.validate_event_data(
            form_config, 
            event_data.get('event_data', {})
        )

        if errors:
            validation_errors.extend(errors)
        
        print("__after form validation:__", event_data)
        # Routing validation
        routing_valid, routing_errors, next_executable = RoutingValidationService.validate_event(event_data)
        print("__after routing validation:__", routing_valid, routing_errors, next_executable)
        if routing_errors:
            validation_errors.extend(routing_errors)

        # Determine overall validation status
        is_valid = is_valid and routing_valid

        event_data['validation_status'] = 'valid' if is_valid else 'invalid'
        event_data['validation_errors'] = validation_errors

        return [event_data]
