from typing import List
from operation.services.event_transformers_plugin import BaseEventTransformerPlugin
from .default_event_transformer import DefaultTransformer


class CorrectedEventTransformer(BaseEventTransformerPlugin):
    transformer_id = "corrected_event_transformer"

    def transform(self, event_data: dict) -> List[dict]:
        processed_events = DefaultTransformer().transform(event_data)

        # considering DefaultTransformer returns only one event
        processed_event = processed_events[0]

        is_corrected = (
            processed_event.get('inspection_status') is False and 
            processed_event.get('next_action') == 'corrected'
        )
        if not is_corrected:
            return processed_events

        failed_event = processed_event.copy()
        failed_event['inspection_status'] = False
        failed_event['next_action'] = 'corrected'

        events = []
        events.append(failed_event)

        corrected_event = processed_event.copy()
        corrected_event['inspection_status'] = True
        corrected_event['next_action'] = 'main_forward'
        corrected_event['event_type'] = 'rework' # need to confirm whether it should be 'rework' or 'main' ?
        corrected_event_data = corrected_event['event_data'].copy()
        corrected_event_data['form_status'] = 'pass' # using the hardcoded value 'pass' for now
        corrected_event['event_data'] = corrected_event_data

        events.append(corrected_event)
        return events
