from typing import List
from operation.services.event_transformers_plugin import BaseEventTransformerPlugin
from workflow_config.services.form_service import FormConfig, FormService


class SpEventTransformer(BaseEventTransformerPlugin):
    transformer_id = "sp_event_ip_transformer"

    def create_serial_number(self, gr_number: str, board_number: int) -> str:
        return f'{gr_number}#{board_number}'

    def transform(self, event_data: dict) -> List[dict]:
        form_config = FormConfig.objects.get(id=event_data['form'])
        is_valid, validation_errors = FormService.validate_event_data(
            form_config, event_data.get('event_data', {})
        )

        event_data['validation_status'] = 'valid' if is_valid else 'invalid'
        event_data['validation_errors'] = validation_errors

        events = []
        if event_data.get('event_data'):
            event_obj = event_data['event_data']
            gr_number = event_obj.get('gr_number')
            qty = int(event_obj.get('box_numbers', 1))

            for i in range(1, qty + 1):
                new_event_obj = event_data.copy()
                new_event_obj['board'] = i
                new_event_obj['serial_number'] = self.create_serial_number(gr_number, i)
                events.append(new_event_obj)

        return events if events else [event_data]
