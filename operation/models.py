from django.db import models
from django.conf import settings
from catalog.models import Product, Scanner
from workflow_config.models import FormConfig
from django.utils import timezone


class ManufacturingEvent(models.Model):
    """
    Manufacturing event data collected through forms
    """
    EVENT_TYPE_CHOICES = [
        ('rework', 'Rework'),
        ('main', 'Main'),
        ('scrap', 'Scrap')
    ]

    NEXT_ACTION_CHOICES = [
        ('send_rework', 'Send Rework'),
        ('main_forward', 'Main Forward'),
        ('corrected', 'Corrected on Fail'),
    ]

    form = models.ForeignKey(FormConfig, on_delete=models.PROTECT, related_name='events')
    serial_number = models.CharField(max_length=100)
    event_type = models.CharField(
        max_length=100,
        choices=EVENT_TYPE_CHOICES,
        default='main'
    )
    next_action = models.CharField(
        max_length=100,
        choices=NEXT_ACTION_CHOICES,
        default='main_forward'
    )
    product = models.ForeignKey(Product, on_delete=models.PROTECT, null=True, blank=True, related_name='event_data')
    work_order = models.CharField(max_length=50, null=True, blank=True)
    board = models.IntegerField()
    event_data = models.JSONField(null=False, blank=False)
    inspection_status = models.BooleanField(default=True)
    validation_status = models.CharField(max_length=20, default='pending')
    validation_errors = models.JSONField(default=list, blank=True)
    timestamp = models.DateTimeField()
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='submitted_events'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    scanner = models.ForeignKey(
        Scanner,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='scanned_events',
    )

    class Meta:
        db_table = 'mes_manufacturing_events'
        ordering = ['-id']

    def __str__(self):
        return f'{self.serial_number} | {self.created_at}'


class FIFOViolationLog(models.Model):
    """
    Logs FIFO violations for auditing and reporting
    """
    event = models.ForeignKey(
        ManufacturingEvent,
        on_delete=models.CASCADE,
        related_name='fifo_violations',
        null=True
    )
    serial_number = models.CharField(max_length=100)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='fifo_violations')

    VIOLATION_TYPES = [
        ('sequence', 'Sequence Violation'),
        ('time_min', 'Minimum Time Violation'),
        ('time_max', 'Maximum Time Violation'),
        ('fifo_order', 'FIFO Order Violation'),
    ]
    violation_type = models.CharField(max_length=20, choices=VIOLATION_TYPES)
    violation_details = models.JSONField()
    violation_timestamp = models.DateTimeField(auto_now_add=True)

    # User who acknowledged/overrode the violation
    acknowledged_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='acknowledged_fifo_violations'
    )
    override_reason = models.TextField(null=True, blank=True)

    class Meta:
        db_table = 'mes_fifo_violation_logs'
        ordering = ['-violation_timestamp']

    def __str__(self):
        return f"{self.serial_number} - {self.get_violation_type_display()}"


class EventRequestLog(models.Model):
    """Model to store raw event creation requests for logging/debugging purposes"""
    request_data = models.JSONField(help_text="Raw request data as received")
    url = models.CharField(max_length=255, help_text="URL of the request")
    method = models.CharField(max_length=10, help_text="HTTP method used")
    created_at = models.DateTimeField(auto_now_add=True)
    response_code = models.IntegerField(null=True, blank=True, help_text="HTTP response status code")
    response_data = models.JSONField(null=True, blank=True, help_text="Response data if any")

    class Meta:
        db_table = 'mes_event_request_logs'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.method} at {self.created_at}"


class WorkOrder(models.Model):
    """
    Work Order model to track manufacturing orders
    """
    line = models.CharField(max_length=100, default='SMT', help_text="Production line")
    part_no = models.CharField(max_length=100, help_text="Part number")
    customer = models.CharField(max_length=100, help_text="Customer name")
    order_date = models.DateField(help_text="Date when the order was placed")
    cf = models.IntegerField(null=True, blank=True, help_text="Carry forward value")
    order_no = models.CharField(max_length=100, help_text="Order number")
    plan = models.IntegerField(null=True, blank=True, help_text="Planned production quantity")
    actual = models.IntegerField(null=True, blank=True, help_text="Actual production quantity")
    product = models.ForeignKey(
        'catalog.Product',
        on_delete=models.PROTECT,
        related_name='work_orders',
        null=True,
        blank=True,
        help_text="Product being manufactured"
    )
    remaining_quantity = models.IntegerField(
        null=True,
        blank=True,
        help_text="Remaining quantity to produce"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_work_orders'
    )

    class Meta:
        db_table = 'mes_work_orders'
        ordering = ['-order_date', 'line']

    def __str__(self):
        return f"{self.order_no} - {self.part_no} ({self.customer})"
