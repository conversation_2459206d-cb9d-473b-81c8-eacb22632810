from rest_framework import serializers
from django.contrib.auth import get_user_model
from ..models import WorkOrder

User = get_user_model()


class WorkOrderSerializer(serializers.ModelSerializer):
    """
    Serializer for the WorkOrder model
    """
    # created_by = serializers.PrimaryKeyRelatedField(
    #     read_only=True,
    #     default=serializers.CurrentUserDefault()
    # )
    created_by_name = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    updated_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    order_date = serializers.DateField(format='%Y-%m-%d')

    class Meta:
        model = WorkOrder
        fields = [
            'id', 'line', 'part_no', 'customer', 'order_date', 'cf', 'order_no',
            'plan', 'actual', 'created_by_name', 'created_at', 'updated_at',
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by_name',]

    def get_created_by_name(self, obj):
        """
        Get the name of the user who created the work order
        """
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None
