from rest_framework import serializers
from operation.models import FIFOViolationLog
from catalog.serializers import ProductSummarySerializer
from authentication.serializers.auth_serializers import UserSummarySerializer


class FIFOViolationLogSerializer(serializers.ModelSerializer):
    """
    Serializer for FIFO violation logs
    """
    product_details = ProductSummarySerializer(source='product', read_only=True)
    acknowledged_by_details = UserSummarySerializer(source='acknowledged_by', read_only=True)
    violation_type_display = serializers.CharField(source='get_violation_type_display', read_only=True)
    
    class Meta:
        model = FIFOViolationLog
        fields = [
            'id', 'event', 'serial_number', 'product', 'product_details',
            'violation_type', 'violation_type_display', 'violation_details', 
            'violation_timestamp', 'acknowledged_by', 'acknowledged_by_details',
            'override_reason'
        ]
        read_only_fields = [
            'id', 'event', 'serial_number', 'product', 'product_details',
            'violation_type', 'violation_type_display', 'violation_details', 
            'violation_timestamp'
        ]


class FIFOViolationOverrideSerializer(serializers.Serializer):
    """
    Serializer for overriding FIFO violations
    """
    violation_id = serializers.IntegerField(required=True)
    override_reason = serializers.CharField(required=True)
