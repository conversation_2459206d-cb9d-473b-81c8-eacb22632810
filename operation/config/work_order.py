from dataclasses import dataclass, field
from typing import Dict, List
from ..models import WorkOrder


@dataclass
class SheetConfig:
    name: str
    required_columns: List[str]
    model_class: object
    max_rows: int
    non_required_columns: List[str] = field(default_factory=list)


@dataclass
class WorkOrderImportConfig:
    MAX_FILE_SIZE_MB: int = 10
    ALLOWED_EXTENSIONS: tuple = ('.xlsx',)
    DATETIME_FORMAT: str = '%Y-%m-%d'  # Format for date

    @staticmethod
    def get_sheet_configs() -> Dict[str, SheetConfig]:
        return {
            'Work Orders': SheetConfig(
                name='Work Orders',
                required_columns=[
                    'Line', 'Part no', 'Customer', 'Date', 
                    'Order no'
                ],
                non_required_columns=[
                    'c/f', 'Plan', 'Actual'
                ],
                model_class=WorkOrder,
                max_rows=10_000
            )
        }

    SHEET_CONFIGS: Dict[str, SheetConfig] = field(default_factory=get_sheet_configs)
