# Mapping of form codes to their respective transformer IDs
FORM_TO_TRANSFORMER_MAPPING = {
    "default": "default", # if no dedicated transformer is found, use the default one
    "pre_wave": "corrected_event_transformer",
    "solder_paste_in": "default", # "sp_event_ip_transformer",
    "solder_paste_out": "default", # "sp_event_op_transformer",
    "solder_paste_mixer": "default", # "sp_event_op_transformer",
    "solder_paste_viscosity": "default", # "sp_event_op_transformer",
    "stencil_wash_in": "stencil_event_ip_transformer",
    "stencil_wash_out": "stencil_event_ip_transformer",
}
