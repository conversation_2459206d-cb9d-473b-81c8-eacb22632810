import json
from operation.models import EventRequestLog

class EventRequestLoggingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        log_entry = None
        # Only process if it's a POST request to the event creation endpoint
        if request.path == '/mes_trace/operation/api/events/' and request.method == 'POST':
            try:
                request_data = json.loads(request.body)
                log_entry = EventRequestLog.objects.create(
                    request_data=request_data,
                    url=request.path,
                    method=request.method
                )
            except Exception as e:
                print(f"Error logging request: {str(e)}")

        # Get response from the view
        response = self.get_response(request)

        # Update the log entry with response code and data if it exists
        if log_entry is not None:
            try:
                log_entry.response_code = response.status_code

                # Try to get response data based on content type
                if hasattr(response, 'content'):
                    try:
                        # Try to parse JSON response
                        response_data = json.loads(response.content.decode('utf-8'))
                        log_entry.response_data = response_data
                    except json.JSONDecodeError:
                        # If not JSON, store as text
                        log_entry.response_data = {
                            'content': response.content.decode('utf-8', errors='replace')
                        }
                    except UnicodeDecodeError:
                        # If can't decode, store basic response info
                        log_entry.response_data = {
                            'content_type': response.get('Content-Type', 'unknown'),
                            'content_length': len(response.content)
                        }

                log_entry.save()
            except Exception as e:
                print(f"Error updating response data: {str(e)}")

        return response
