# 🔧 PeerDB Setup & Configuration

This directory contains all files related to PeerDB setup, configuration, and maintenance for real-time PostgreSQL to ClickHouse data synchronization.

## 📁 **Directory Structure**

```
peerdb_setup/
├── README.md                           # This file
├── scripts/                           # Python configuration scripts
│   ├── configure_peerdb_programmatically.py  # Main setup script
│   ├── configure_peerdb_api.py               # API-based configuration
│   ├── fix_peerdb_upsert_issue.py           # UPSERT issue resolution
│   └── update_sync_interval.py              # Sync interval management
├── sql/                               # SQL configuration files
│   ├── setup_peerdb_sync.sql                # Direct SQL setup commands
│   └── update_sync_interval.sql             # SQL for interval changes
├── verification/                      # Testing & verification scripts
│   ├── test_sync_functionality.py           # Basic sync testing
│   ├── verify_update_sync.py               # UPDATE operation verification
│   ├── analyze_view_performance.py         # Performance analysis
│   └── analyze_mirror_recreation_behavior.py # Mirror behavior analysis
├── documentation/                     # Documentation & reports
│   ├── PEERDB_SETUP_COMPLETE.md            # Initial setup guide
│   ├── PEERDB_SUCCESS_REPORT.md            # Implementation success report
│   ├── PEERDB_UPSERT_SOLUTION_REPORT.md    # UPSERT issue solution
│   └── peerdb_setup_guide.md               # Step-by-step setup guide
└── shell/                            # Shell scripts
    └── verify_peerdb_setup.sh              # Infrastructure verification
```

## 🚀 **Quick Start**

### **1. Initial Setup**
```bash
# Run the main configuration script
cd peerdb_setup/scripts
python3 configure_peerdb_programmatically.py
```

### **2. Verify Setup**
```bash
# Check infrastructure
cd ../shell
./verify_peerdb_setup.sh

# Test sync functionality
cd ../verification
python3 test_sync_functionality.py
```

### **3. Monitor Performance**
```bash
# Analyze view performance
cd verification
python3 analyze_view_performance.py
```

## 📋 **File Descriptions**

### **Scripts (`scripts/`)**

#### **`configure_peerdb_programmatically.py`**
- **Purpose**: Main automated PeerDB configuration
- **Features**: Creates peers, mirrors, and optimized settings
- **Usage**: `python3 configure_peerdb_programmatically.py`
- **Output**: Complete PeerDB setup with 40 table sync

#### **`fix_peerdb_upsert_issue.py`**
- **Purpose**: Resolves UPDATE synchronization issues
- **Features**: Creates deduplication views for all entity tables
- **Usage**: `python3 fix_peerdb_upsert_issue.py`
- **Output**: 36 `*_current` views for clean analytics

#### **`update_sync_interval.py`**
- **Purpose**: Changes sync interval (e.g., 30s → 5s)
- **Features**: Recreates mirrors with new timing
- **Usage**: `python3 update_sync_interval.py`
- **Output**: Updated sync frequency

### **SQL (`sql/`)**

#### **`setup_peerdb_sync.sql`**
- **Purpose**: Direct SQL commands for PeerDB setup
- **Usage**: `psql "port=9900 host=localhost password=peerdb" -f setup_peerdb_sync.sql`
- **Features**: Peer creation, mirror configuration

#### **`update_sync_interval.sql`**
- **Purpose**: SQL commands to change sync interval
- **Usage**: Execute via PeerDB SQL interface
- **Features**: Mirror recreation with new timing

### **Verification (`verification/`)**

#### **`test_sync_functionality.py`**
- **Purpose**: Basic sync testing and verification
- **Features**: Tests INSERT/UPDATE operations
- **Usage**: `python3 test_sync_functionality.py`

#### **`verify_update_sync.py`**
- **Purpose**: Comprehensive UPDATE synchronization testing
- **Features**: Multi-table UPDATE verification
- **Usage**: `python3 verify_update_sync.py`

#### **`analyze_view_performance.py`**
- **Purpose**: Performance analysis of deduplication views
- **Features**: Storage overhead and query performance metrics
- **Usage**: `python3 analyze_view_performance.py`

### **Documentation (`documentation/`)**

#### **`PEERDB_SUCCESS_REPORT.md`**
- **Purpose**: Complete implementation success report
- **Content**: Configuration summary, test results, next steps

#### **`PEERDB_UPSERT_SOLUTION_REPORT.md`**
- **Purpose**: Detailed UPSERT issue resolution documentation
- **Content**: Root cause analysis, solution implementation, usage guide

### **Shell (`shell/`)**

#### **`verify_peerdb_setup.sh`**
- **Purpose**: Infrastructure verification script
- **Features**: Checks Docker containers, database connections
- **Usage**: `./verify_peerdb_setup.sh`

## 🔧 **Configuration Summary**

### **Current Setup**
- **PostgreSQL Source**: localhost:5432/mes_db
- **ClickHouse Target**: localhost:9000/default
- **PeerDB Server**: localhost:9900
- **PeerDB UI**: http://localhost:3000
- **Sync Interval**: 5 seconds
- **Tables Synced**: 40 MES tables

### **Sync Configuration**
- **Entity Tables (36)**: UPDATE mode with deduplication views
- **Event Tables (4)**: APPEND mode for high-volume data
- **Deduplication**: `*_current` views for analytics
- **Audit Trail**: Original tables preserve all versions

## 📊 **Usage Patterns**

### **For Analytics Queries**
```sql
-- ✅ CORRECT: Use deduplication views
SELECT * FROM mes_scanners_current;
SELECT * FROM mes_commodities_current;
```

### **For Audit/History**
```sql
-- ✅ CORRECT: Use original tables
SELECT * FROM mes_scanners ORDER BY _peerdb_version;
```

### **Performance Monitoring**
```bash
# Check sync status
echo "SELECT count(*) FROM default.mes_commodities_current" | curl --data-binary @- "http://localhost:8123/" -u default:password

# Monitor PeerDB dashboard
open http://localhost:3000
```

## 🛠️ **Maintenance**

### **Regular Tasks**
1. **Monitor sync lag**: Check PeerDB dashboard
2. **Verify data consistency**: Run verification scripts
3. **Performance analysis**: Use analyze_view_performance.py
4. **Update intervals**: Use update_sync_interval.py if needed

### **Troubleshooting**
1. **Sync issues**: Check PeerDB logs and dashboard
2. **Performance problems**: Run performance analysis
3. **Data inconsistencies**: Run verification scripts
4. **Configuration changes**: Use provided scripts

## 📚 **Additional Resources**

- **PeerDB Documentation**: https://docs.peerdb.io/
- **PeerDB Dashboard**: http://localhost:3000
- **ClickHouse Documentation**: https://clickhouse.com/docs/
- **PostgreSQL CDC**: https://www.postgresql.org/docs/current/logical-replication.html

---

**🎉 All PeerDB setup files are organized and ready for use!**
