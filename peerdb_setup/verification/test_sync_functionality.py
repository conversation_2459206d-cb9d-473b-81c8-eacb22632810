#!/usr/bin/env python3
"""
Test script to verify PeerDB sync functionality
This script tests data insertion and verifies sync between PostgreSQL and ClickHouse
"""

import psycopg2
import requests
import time
import json
from datetime import datetime

# Database connection parameters
PG_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'mes_db',
    'user': 'postgres',
    'password': 'postgres'
}

CH_CONFIG = {
    'host': 'localhost',
    'port': 8123,
    'user': 'default',
    'password': 'password'
}

def test_postgresql_connection():
    """Test PostgreSQL connection"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        print(f"✅ PostgreSQL connected: {version[:50]}...")
        return True
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False

def test_clickhouse_connection():
    """Test ClickHouse connection"""
    try:
        url = f"http://{CH_CONFIG['host']}:{CH_CONFIG['port']}/ping"
        response = requests.get(url, timeout=5)
        if response.text.strip() == "Ok.":
            print("✅ ClickHouse connected successfully")
            return True
        else:
            print(f"❌ ClickHouse ping failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ ClickHouse connection failed: {e}")
        return False

def insert_test_data():
    """Insert test data into PostgreSQL"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()

        # Insert test commodity (let PostgreSQL auto-generate ID)
        test_name = f"Test_Commodity_{int(time.time())}"
        cursor.execute("""
            INSERT INTO mes_commodities (name, description, is_active, created_at, updated_at)
            VALUES (%s, %s, %s, NOW(), NOW())
            RETURNING id;
        """, (test_name, "Test commodity for sync verification", True))

        commodity_id = cursor.fetchone()[0]
        conn.commit()
        cursor.close()
        conn.close()

        print(f"✅ Test data inserted: Commodity ID {commodity_id} - {test_name}")
        return commodity_id, test_name
    except Exception as e:
        print(f"❌ Failed to insert test data: {e}")
        return None, None

def check_clickhouse_sync(commodity_id, test_name, max_wait=60):
    """Check if data synced to ClickHouse"""
    print(f"🔍 Checking ClickHouse sync for commodity ID {commodity_id}...")

    for attempt in range(max_wait):
        try:
            query = f"SELECT count(*) FROM default.mes_commodities WHERE name = '{test_name}'"
            url = f"http://{CH_CONFIG['host']}:{CH_CONFIG['port']}/"

            response = requests.post(
                url,
                data=query,
                auth=(CH_CONFIG['user'], CH_CONFIG['password']),
                timeout=5
            )

            if response.status_code == 200:
                count = int(response.text.strip())
                if count > 0:
                    print(f"✅ Data synced to ClickHouse! Found {count} record(s)")
                    return True
                else:
                    if attempt % 10 == 0:  # Print every 10 seconds
                        print(f"⏳ Waiting for sync... ({attempt}s)")
                    time.sleep(1)
            else:
                print(f"❌ ClickHouse query failed: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            if attempt % 10 == 0:
                print(f"⏳ Waiting for sync... ({attempt}s) - {str(e)[:50]}")
            time.sleep(1)

    print(f"❌ Data not synced after {max_wait} seconds")
    return False

def cleanup_test_data(commodity_id):
    """Clean up test data"""
    try:
        # Clean PostgreSQL
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM mes_commodities WHERE id = %s", (commodity_id,))
        conn.commit()
        cursor.close()
        conn.close()
        print(f"✅ Test data cleaned from PostgreSQL")

        # Note: ClickHouse cleanup would happen automatically via CDC
        print("ℹ️  ClickHouse cleanup will happen via CDC sync")

    except Exception as e:
        print(f"⚠️  Failed to cleanup test data: {e}")

def main():
    print("🧪 PeerDB Sync Functionality Test")
    print("=================================")

    # Test connections
    print("\n📡 Testing database connections...")
    pg_ok = test_postgresql_connection()
    ch_ok = test_clickhouse_connection()

    if not (pg_ok and ch_ok):
        print("\n❌ Database connections failed. Please check your setup.")
        return

    print("\n📊 Testing data sync...")
    print("Note: This test requires PeerDB mirrors to be configured and running")

    # Insert test data
    commodity_id, test_name = insert_test_data()
    if not commodity_id:
        return

    # Check sync
    synced = check_clickhouse_sync(commodity_id, test_name)

    if synced:
        print("\n🎉 Sync test PASSED! PeerDB is working correctly.")
    else:
        print("\n⚠️  Sync test FAILED or TIMED OUT.")
        print("This could mean:")
        print("- PeerDB mirrors are not configured yet")
        print("- Mirrors are not running")
        print("- There's a configuration issue")
        print("- Sync is slower than expected")

    # Cleanup
    print("\n🧹 Cleaning up test data...")
    cleanup_test_data(commodity_id)

    print("\n📋 Next Steps:")
    print("1. Open PeerDB Dashboard: http://localhost:3000")
    print("2. Configure peers and mirrors if not done yet")
    print("3. Monitor sync status in the dashboard")
    print("4. Run this test again after configuration")

if __name__ == "__main__":
    main()
