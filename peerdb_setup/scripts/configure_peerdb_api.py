#!/usr/bin/env python3
"""
PeerDB API Configuration Script
Automatically configures PostgreSQL and ClickHouse peers and creates mirrors
"""

import requests
import json
import time
import sys

# PeerDB API Configuration
PEERDB_API_BASE = "http://localhost:8112"
PEERDB_UI_BASE = "http://localhost:3000"

def check_peerdb_ready():
    """Check if PeerDB API is ready"""
    try:
        response = requests.get(f"{PEERDB_API_BASE}/v1/peers", timeout=5)
        return response.status_code == 200
    except:
        return False

def create_postgres_peer():
    """Create PostgreSQL peer connection"""
    peer_config = {
        "name": "postgres_mes_source",
        "type": "POSTGRES",
        "config": {
            "host": "host.docker.internal",
            "port": 5432,
            "user": "postgres",
            "password": "postgres",
            "database": "mes_db"
        }
    }
    
    try:
        response = requests.post(
            f"{PEERDB_API_BASE}/v1/peers",
            json=peer_config,
            headers={"Content-Type": "application/json"}
        )
        return response.status_code in [200, 201], response.text
    except Exception as e:
        return False, str(e)

def create_clickhouse_peer():
    """Create ClickHouse peer connection"""
    peer_config = {
        "name": "clickhouse_mes_target", 
        "type": "CLICKHOUSE",
        "config": {
            "host": "host.docker.internal",
            "port": 9000,
            "user": "default",
            "password": "password",
            "database": "default"
        }
    }
    
    try:
        response = requests.post(
            f"{PEERDB_API_BASE}/v1/peers",
            json=peer_config,
            headers={"Content-Type": "application/json"}
        )
        return response.status_code in [200, 201], response.text
    except Exception as e:
        return False, str(e)

def create_entities_mirror():
    """Create mirror for tables with updated_at column (UPSERT mode)"""
    
    # Tables with updated_at column that support updates
    tables_with_updates = [
        "mes_commodities", "mes_components", "mes_products", "mes_product_parts",
        "mes_product_components", "mes_scanners", "mes_areas", "mes_assembly_lines", 
        "mes_factory", "mes_process_blocks", "mes_form_config", "mes_routing",
        "mes_routing_product", "mes_routing_execution", "mes_bom_header", "mes_bom_item",
        "mes_work_orders", "mes_users", "mes_modules", "mes_groups", "mes_access_scopes",
        "mes_user_mes_groups", "mes_group_module_permissions", "mes_group_object_permissions",
        "mes_user_module_permissions", "mes_user_object_permissions", "mes_reference_categories",
        "mes_reference_values", "mes_master_program", "mes_master_program_product_param",
        "mes_sop", "mes_analytics_dashboards", "mes_analytics_charts", 
        "mes_analytics_chart_groups", "mes_aoi_daily_yield", "mes_aoi_rejection"
    ]
    
    mirror_config = {
        "flow_job_name": "mes_entities_mirror",
        "source_peer": "postgres_mes_source",
        "destination_peer": "clickhouse_mes_target",
        "table_mappings": [
            {
                "source_table_identifier": table,
                "destination_table_identifier": table
            } for table in tables_with_updates
        ],
        "sync_mode": "CDC",
        "cdc_staging_path": "",
        "soft_delete": False
    }
    
    try:
        response = requests.post(
            f"{PEERDB_API_BASE}/v1/flows/cdc",
            json=mirror_config,
            headers={"Content-Type": "application/json"}
        )
        return response.status_code in [200, 201], response.text
    except Exception as e:
        return False, str(e)

def create_events_mirror():
    """Create mirror for append-only tables (INSERT mode)"""
    
    # Tables without updated_at column (append-only)
    append_only_tables = [
        "mes_manufacturing_events", "mes_event_request_logs", 
        "mes_fifo_violation_logs", "mes_cache"
    ]
    
    mirror_config = {
        "flow_job_name": "mes_events_mirror",
        "source_peer": "postgres_mes_source", 
        "destination_peer": "clickhouse_mes_target",
        "table_mappings": [
            {
                "source_table_identifier": table,
                "destination_table_identifier": table
            } for table in append_only_tables
        ],
        "sync_mode": "CDC",
        "cdc_staging_path": "",
        "soft_delete": False
    }
    
    try:
        response = requests.post(
            f"{PEERDB_API_BASE}/v1/flows/cdc",
            json=mirror_config,
            headers={"Content-Type": "application/json"}
        )
        return response.status_code in [200, 201], response.text
    except Exception as e:
        return False, str(e)

def main():
    print("🚀 PeerDB Automatic Configuration Script")
    print("========================================")
    
    # Check if PeerDB is ready
    print("\n📡 Checking PeerDB API availability...")
    if not check_peerdb_ready():
        print("❌ PeerDB API not ready. Please ensure PeerDB is running.")
        print("   Try: docker ps | grep peerdb")
        sys.exit(1)
    print("✅ PeerDB API is ready")
    
    # Create PostgreSQL peer
    print("\n🐘 Creating PostgreSQL peer connection...")
    success, message = create_postgres_peer()
    if success:
        print("✅ PostgreSQL peer created successfully")
    else:
        print(f"❌ Failed to create PostgreSQL peer: {message}")
    
    # Create ClickHouse peer  
    print("\n🏠 Creating ClickHouse peer connection...")
    success, message = create_clickhouse_peer()
    if success:
        print("✅ ClickHouse peer created successfully")
    else:
        print(f"❌ Failed to create ClickHouse peer: {message}")
    
    # Wait a moment for peers to be ready
    print("\n⏳ Waiting for peers to initialize...")
    time.sleep(3)
    
    # Create entities mirror (with updates)
    print("\n🔄 Creating entities mirror (UPDATE mode)...")
    success, message = create_entities_mirror()
    if success:
        print("✅ Entities mirror created successfully")
    else:
        print(f"❌ Failed to create entities mirror: {message}")
    
    # Create events mirror (append-only)
    print("\n📝 Creating events mirror (APPEND mode)...")
    success, message = create_events_mirror()
    if success:
        print("✅ Events mirror created successfully")
    else:
        print(f"❌ Failed to create events mirror: {message}")
    
    print("\n🎉 Configuration complete!")
    print(f"📊 Open PeerDB Dashboard: {PEERDB_UI_BASE}")
    print("📈 Monitor sync status in the Mirrors section")

if __name__ == "__main__":
    main()
