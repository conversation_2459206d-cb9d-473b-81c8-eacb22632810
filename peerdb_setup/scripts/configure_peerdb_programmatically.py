#!/usr/bin/env python3
"""
PeerDB Programmatic Configuration Script
Automatically creates peers and mirrors using PeerDB's SQL interface
"""

import psycopg2
import time
import sys
from typing import List, Tuple

# PeerDB connection parameters
PEERDB_CONFIG = {
    'host': 'localhost',
    'port': 9900,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'peerdb'
}

# Tables with updated_at column (supports UPDATE mode)
TABLES_WITH_UPDATES = [
    "mes_commodities", "mes_components", "mes_products", "mes_product_parts",
    "mes_product_components", "mes_scanners", "mes_areas", "mes_assembly_lines",
    "mes_factory", "mes_process_blocks", "mes_form_config", "mes_routing",
    "mes_routing_product", "mes_routing_execution", "mes_bom_header", "mes_bom_item",
    "mes_work_orders", "mes_users", "mes_modules", "mes_groups", "mes_access_scopes",
    "mes_user_mes_groups", "mes_group_module_permissions", "mes_group_object_permissions",
    "mes_user_module_permissions", "mes_user_object_permissions", "mes_reference_categories",
    "mes_reference_values", "mes_master_program", "mes_master_program_product_param",
    "mes_sop", "mes_analytics_dashboards", "mes_analytics_charts",
    "mes_analytics_chart_groups", "mes_aoi_daily_yield", "mes_aoi_rejection"
]

# Tables without updated_at column (INSERT-only mode)
APPEND_ONLY_TABLES = [
    "mes_manufacturing_events", "mes_event_request_logs",
    "mes_fifo_violation_logs", "mes_cache"
]

def connect_to_peerdb():
    """Connect to PeerDB"""
    try:
        conn = psycopg2.connect(**PEERDB_CONFIG)
        conn.autocommit = True
        print("✅ Connected to PeerDB successfully")
        return conn
    except Exception as e:
        print(f"❌ Failed to connect to PeerDB: {e}")
        return None

def execute_sql(conn, sql: str, description: str) -> bool:
    """Execute SQL command and handle errors"""
    try:
        cursor = conn.cursor()
        cursor.execute(sql)
        cursor.close()
        print(f"✅ {description}")
        return True
    except Exception as e:
        print(f"❌ Failed to {description.lower()}: {e}")
        return False

def create_postgresql_peer(conn) -> bool:
    """Create PostgreSQL peer connection"""
    sql = """
    CREATE PEER postgres_mes_source FROM POSTGRES WITH (
        host = 'host.docker.internal',
        port = 5432,
        user = 'postgres',
        password = 'postgres',
        database = 'mes_db'
    );
    """
    return execute_sql(conn, sql, "Create PostgreSQL peer 'postgres_mes_source'")

def create_clickhouse_peer(conn) -> bool:
    """Create ClickHouse peer connection"""
    sql = """
    CREATE PEER clickhouse_mes_target FROM CLICKHOUSE WITH (
        host = 'host.docker.internal',
        port = 9000,
        user = 'default',
        password = 'password',
        database = 'default',
        disable_tls = true
    );
    """
    return execute_sql(conn, sql, "Create ClickHouse peer 'clickhouse_mes_target'")

def create_entities_mirror(conn) -> bool:
    """Create mirror for tables with updated_at column (UPDATE mode)"""

    # Create table mappings for entities
    table_mappings = []
    for table in TABLES_WITH_UPDATES:
        table_mappings.append(f"public.{table}:{table}")

    mappings_str = ",\n      ".join(table_mappings)

    sql = f"""
    CREATE MIRROR IF NOT EXISTS mes_entities_mirror
    FROM postgres_mes_source TO clickhouse_mes_target
    WITH TABLE MAPPING (
      {mappings_str}
    )
    WITH (
      do_initial_copy = true,
      max_batch_size = 10000,
      sync_interval = 30,
      snapshot_num_rows_per_partition = 500000,
      snapshot_max_parallel_workers = 4,
      snapshot_num_tables_in_parallel = 4,
      soft_delete = true,
      synced_at_col_name = '_PEERDB_SYNCED_AT',
      soft_delete_col_name = '_PEERDB_IS_DELETED'
    );
    """
    return execute_sql(conn, sql, "Create entities mirror 'mes_entities_mirror' (UPDATE mode)")

def create_events_mirror(conn) -> bool:
    """Create mirror for append-only tables (INSERT mode)"""

    # Create table mappings for events
    table_mappings = []
    for table in APPEND_ONLY_TABLES:
        table_mappings.append(f"public.{table}:{table}")

    mappings_str = ",\n      ".join(table_mappings)

    sql = f"""
    CREATE MIRROR IF NOT EXISTS mes_events_mirror
    FROM postgres_mes_source TO clickhouse_mes_target
    WITH TABLE MAPPING (
      {mappings_str}
    )
    WITH (
      do_initial_copy = true,
      max_batch_size = 10000,
      sync_interval = 30,
      snapshot_num_rows_per_partition = 500000,
      snapshot_max_parallel_workers = 4,
      snapshot_num_tables_in_parallel = 4,
      soft_delete = false,
      synced_at_col_name = '_PEERDB_SYNCED_AT'
    );
    """
    return execute_sql(conn, sql, "Create events mirror 'mes_events_mirror' (APPEND mode)")

def verify_configuration(conn) -> bool:
    """Verify that peers and mirrors were created successfully"""
    try:
        cursor = conn.cursor()

        # Check peers
        cursor.execute("SELECT name, type FROM peers ORDER BY name;")
        peers = cursor.fetchall()
        print(f"\n📋 Created Peers ({len(peers)}):")
        for name, peer_type in peers:
            print(f"  - {name} ({peer_type})")

        # Check mirrors (try different possible table/column names)
        try:
            cursor.execute("SELECT name FROM flows ORDER BY name;")
            mirrors = cursor.fetchall()
        except:
            try:
                cursor.execute("SELECT flow_job_name FROM flows ORDER BY flow_job_name;")
                mirrors = cursor.fetchall()
            except:
                # If flows table doesn't exist or has different structure, check for mirrors table
                try:
                    cursor.execute("SELECT name FROM mirrors ORDER BY name;")
                    mirrors = cursor.fetchall()
                except:
                    mirrors = []

        print(f"\n📋 Created Mirrors ({len(mirrors)}):")
        for (name,) in mirrors:
            print(f"  - {name}")

        cursor.close()

        expected_peers = 2
        expected_mirrors = 2

        if len(peers) >= expected_peers and len(mirrors) >= expected_mirrors:
            print(f"\n✅ Configuration verification successful!")
            return True
        else:
            print(f"\n⚠️  Expected {expected_peers} peers and {expected_mirrors} mirrors, found {len(peers)} peers and {len(mirrors)} mirrors")
            return False

    except Exception as e:
        print(f"❌ Failed to verify configuration: {e}")
        return False

def main():
    print("🚀 PeerDB Programmatic Configuration")
    print("====================================")
    print(f"📊 Configuring sync for {len(TABLES_WITH_UPDATES)} entity tables and {len(APPEND_ONLY_TABLES)} event tables")

    # Connect to PeerDB
    print("\n📡 Connecting to PeerDB...")
    conn = connect_to_peerdb()
    if not conn:
        sys.exit(1)

    success_count = 0
    total_steps = 4

    # Step 1: Create PostgreSQL peer
    print("\n🐘 Step 1: Creating PostgreSQL peer...")
    if create_postgresql_peer(conn):
        success_count += 1

    # Step 2: Create ClickHouse peer
    print("\n🏠 Step 2: Creating ClickHouse peer...")
    if create_clickhouse_peer(conn):
        success_count += 1

    # Wait for peers to be ready
    print("\n⏳ Waiting for peers to initialize...")
    time.sleep(3)

    # Step 3: Create entities mirror
    print("\n🔄 Step 3: Creating entities mirror (UPDATE mode)...")
    if create_entities_mirror(conn):
        success_count += 1

    # Step 4: Create events mirror
    print("\n📝 Step 4: Creating events mirror (APPEND mode)...")
    if create_events_mirror(conn):
        success_count += 1

    # Verification
    print("\n🔍 Verifying configuration...")
    if verify_configuration(conn):
        success_count += 1

    # Close connection
    conn.close()

    # Summary
    print(f"\n{'='*50}")
    print(f"📊 Configuration Summary: {success_count}/{total_steps} steps completed")

    if success_count >= total_steps:
        print("🎉 SUCCESS: PeerDB configuration completed successfully!")
        print("\n📈 Next Steps:")
        print("1. Open PeerDB Dashboard: http://localhost:3000")
        print("2. Monitor sync status in the Mirrors section")
        print("3. Test data sync by inserting/updating records in PostgreSQL")
        print("4. Verify changes appear in ClickHouse within 30-60 seconds")
        print("5. Run: python3 test_sync_functionality.py")
    else:
        print("⚠️  PARTIAL SUCCESS: Some steps failed. Check the errors above.")
        print("💡 You can re-run this script to retry failed steps.")

    print(f"\n🔧 Table Configuration:")
    print(f"   - Entity tables (UPDATE mode): {len(TABLES_WITH_UPDATES)} tables")
    print(f"   - Event tables (APPEND mode): {len(APPEND_ONLY_TABLES)} tables")

if __name__ == "__main__":
    main()
