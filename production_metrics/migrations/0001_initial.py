# Generated by Django 5.1 on 2025-05-31 19:18

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('catalog', '0006_remove_time_constraint_fields'),
        ('operation', '0012_workorder_product_workorder_remaining_quantity'),
        ('workflow_config', '0012_alter_aoidailyyield_fpy_percentage_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Shift',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('start_time', models.TimeField(help_text='Shift start time')),
                ('end_time', models.TimeField(help_text='Shift end time')),
                ('duration_hours', models.DecimalField(decimal_places=2, help_text='Shift duration in hours', max_digits=4, validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(24.0)])),
            ],
            options={
                'db_table': 'mes_production_shifts',
                'ordering': ['start_time'],
            },
        ),
        migrations.CreateModel(
            name='LineCapacity',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uph', models.IntegerField(help_text='Units Per Hour', validators=[django.core.validators.MinValueValidator(1)])),
                ('manpower', models.IntegerField(help_text='Required manpower', validators=[django.core.validators.MinValueValidator(1)])),
                ('upph', models.DecimalField(decimal_places=2, help_text='Units Per Person Hour', max_digits=8, validators=[django.core.validators.MinValueValidator(0.01)])),
                ('standard_time_seconds', models.DecimalField(decimal_places=2, help_text='Standard time per unit in seconds', max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)])),
                ('bottleneck_time_seconds', models.DecimalField(decimal_places=2, help_text='Bottleneck time per unit in seconds', max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)])),
                ('is_active', models.BooleanField(default=True)),
                ('line', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='capacities', to='workflow_config.assemblyline')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='line_capacities', to='catalog.product')),
            ],
            options={
                'db_table': 'mes_line_capacity',
                'ordering': ['line__name', 'product__name'],
                'unique_together': {('line', 'product')},
            },
        ),
        migrations.CreateModel(
            name='ProductionSchedule',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('production_date', models.DateField(help_text='Production date')),
                ('start_time', models.TimeField(help_text='Hour start time')),
                ('end_time', models.TimeField(help_text='Hour end time')),
                ('planned_break_minutes', models.IntegerField(default=0, help_text='Planned break duration in minutes', validators=[django.core.validators.MinValueValidator(0)])),
                ('downtime_minutes', models.IntegerField(default=0, help_text='Unplanned downtime in minutes', validators=[django.core.validators.MinValueValidator(0)])),
                ('downtime_reason', models.CharField(blank=True, help_text='Reason for downtime', max_length=200)),
                ('manpower', models.IntegerField(help_text='Number of workers', validators=[django.core.validators.MinValueValidator(1)])),
                ('actual_quantity', models.IntegerField(blank=True, help_text='Actual production quantity', null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('rejection_quantity', models.IntegerField(default=0, help_text='Number of rejected units', validators=[django.core.validators.MinValueValidator(0)])),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_schedules', to=settings.AUTH_USER_MODEL)),
                ('line', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='schedules', to='workflow_config.assemblyline')),
                ('work_order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='schedules', to='operation.workorder')),
                ('shift', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='schedules', to='production_metrics.shift')),
            ],
            options={
                'db_table': 'mes_production_schedule',
                'ordering': ['-production_date', 'start_time', 'line__name'],
                'unique_together': {('production_date', 'start_time', 'line')},
            },
        ),
        migrations.CreateModel(
            name='ProductionMetrics',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('machine_runtime_hours', models.DecimalField(decimal_places=2, help_text='Actual machine runtime in hours', max_digits=6, validators=[django.core.validators.MinValueValidator(0)])),
                ('target_quantity', models.IntegerField(help_text='Target production based on capacity', validators=[django.core.validators.MinValueValidator(0)])),
                ('actual_ok_quantity', models.IntegerField(help_text='Actual quantity minus rejections', validators=[django.core.validators.MinValueValidator(0)])),
                ('efficiency_percentage', models.DecimalField(decimal_places=2, help_text='(Actual/Target) * 100', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(1000)])),
                ('quality_percentage', models.DecimalField(decimal_places=2, help_text='(OK Qty/Actual) * 100', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('upph_actual', models.DecimalField(decimal_places=2, help_text='Actual UPPH achieved', max_digits=8, validators=[django.core.validators.MinValueValidator(0)])),
                ('line_capacity_calculated', models.IntegerField(help_text='Calculated line capacity for the period', validators=[django.core.validators.MinValueValidator(0)])),
                ('line_capacity', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='metrics', to='production_metrics.linecapacity')),
                ('schedule', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='production_metrics.productionschedule')),
            ],
            options={
                'db_table': 'mes_production_metrics',
                'ordering': ['-schedule__production_date', 'schedule__start_time'],
            },
        ),
        migrations.CreateModel(
            name='DowntimeLog',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('downtime_type', models.CharField(choices=[('planned', 'Planned'), ('unplanned', 'Unplanned')], help_text='Type of downtime', max_length=20)),
                ('duration_minutes', models.IntegerField(help_text='Duration of downtime in minutes', validators=[django.core.validators.MinValueValidator(1)])),
                ('reason', models.CharField(help_text='Reason code for downtime', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the downtime')),
                ('start_time', models.DateTimeField(help_text='When the downtime started')),
                ('end_time', models.DateTimeField(blank=True, help_text='When the downtime ended', null=True)),
                ('logged_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='logged_downtimes', to=settings.AUTH_USER_MODEL)),
                ('schedule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='downtime_logs', to='production_metrics.productionschedule')),
            ],
            options={
                'db_table': 'mes_downtime_log',
                'ordering': ['-start_time'],
            },
        ),
    ]
