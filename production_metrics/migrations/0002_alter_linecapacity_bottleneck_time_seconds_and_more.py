# Generated by Django 5.1 on 2025-06-01 19:48

import django.core.validators
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('production_metrics', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='linecapacity',
            name='bottleneck_time_seconds',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Bottleneck time per unit in seconds', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.01)]),
        ),
        migrations.AlterField(
            model_name='linecapacity',
            name='manpower',
            field=models.IntegerField(blank=True, help_text='Required manpower', null=True, validators=[django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AlterField(
            model_name='linecapacity',
            name='standard_time_seconds',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Standard time per unit in seconds', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.01)]),
        ),
        migrations.AlterField(
            model_name='linecapacity',
            name='upph',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Units Per Person Hour', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(0.01)]),
        ),
        migrations.AlterField(
            model_name='productionmetrics',
            name='actual_ok_quantity',
            field=models.IntegerField(default=0, help_text='Actual quantity minus rejections', validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AlterField(
            model_name='productionmetrics',
            name='efficiency_percentage',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='(Actual/Target) * 100', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(1000)]),
        ),
        migrations.AlterField(
            model_name='productionmetrics',
            name='line_capacity_calculated',
            field=models.IntegerField(default=0, help_text='Calculated line capacity for the period', validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AlterField(
            model_name='productionmetrics',
            name='quality_percentage',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='(OK Qty/Actual) * 100', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
        migrations.AlterField(
            model_name='productionmetrics',
            name='target_quantity',
            field=models.IntegerField(default=0, help_text='Target production based on capacity', validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AlterField(
            model_name='productionmetrics',
            name='upph_actual',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Actual UPPH achieved', max_digits=8, validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AlterField(
            model_name='productionschedule',
            name='actual_quantity',
            field=models.IntegerField(default=0, help_text='Actual production quantity', validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AlterField(
            model_name='productionschedule',
            name='downtime_reason',
            field=models.CharField(blank=True, help_text='Reason for downtime', max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='productionschedule',
            name='manpower',
            field=models.IntegerField(default=0, help_text='Number of workers', validators=[django.core.validators.MinValueValidator(0)]),
        ),
    ]
