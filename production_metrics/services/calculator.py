"""
Production Metrics Calculator Service

This service implements the calculation formulas from the production metrics data dictionary.
"""
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional


class ProductionMetricsCalculator:
    """
    Service class for calculating production metrics based on data dictionary formulas
    """

    @staticmethod
    def calculate_machine_runtime_hours(
        total_time_minutes: int, 
        planned_break_minutes: int, 
        downtime_minutes: int
    ) -> Decimal:
        """
        Calculate actual machine runtime in hours
        Formula: (Total Time - Planned Breaks - Downtime) / 60
        """
        runtime_minutes = total_time_minutes - planned_break_minutes - downtime_minutes
        if runtime_minutes < 0:
            runtime_minutes = 0
        return Decimal(runtime_minutes / 60).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_target_quantity(total_time_minutes: int, planned_break_minutes: int, uph: int) -> int:
        """
        Calculate target production quantity
        Formula: ((Total Time - Planned Breaks) / 60) * UPH
        """
        runtime_minutes = total_time_minutes - planned_break_minutes
        if runtime_minutes < 0:
            runtime_minutes = 0
        return int((runtime_minutes / 60) * uph)

    @staticmethod
    def calculate_line_capacity(total_hours: Decimal, uph: int) -> int:
        """
        Calculate line capacity
        Formula: Total Hours * UPH
        """
        return int(total_hours * uph)

    @staticmethod
    def calculate_efficiency_percentage(actual_quantity: int, target_quantity: int) -> Decimal:
        """
        Calculate efficiency percentage
        Formula: (Actual / Target) * 100
        """
        if target_quantity <= 0:
            return Decimal('0.00')
        efficiency = (actual_quantity / target_quantity) * 100
        return Decimal(efficiency).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_quality_percentage(actual_ok_quantity: int, actual_quantity: int) -> Decimal:
        """
        Calculate quality percentage
        Formula: (Actual OK Qty / Actual) * 100
        """
        if actual_quantity <= 0:
            return Decimal('0.00')
        quality = (actual_ok_quantity / actual_quantity) * 100
        return Decimal(quality).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_upph_actual(actual_quantity: int, machine_runtime_hours: Decimal, manpower: int) -> Decimal:
        """
        Calculate actual Units Per Person Hour
        Formula: Actual Quantity / (Machine Runtime Hours * Manpower)
        """
        if machine_runtime_hours <= 0 or manpower <= 0:
            return Decimal('0.00')
        person_hours = machine_runtime_hours * manpower
        if person_hours <= 0:
            return Decimal('0.00')
        upph = actual_quantity / person_hours
        return Decimal(upph).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_weighted_average_time(
        time_values: list, 
        weight_values: list
    ) -> Decimal:
        """
        Calculate weighted average for standard time or bottleneck time
        Formula: Σ(Total Time * Time Value) / Σ(Total Time)
        
        Used for:
        - Standard Time = Σ(Total Time * Standard Time(s)) / Σ(Total Time)
        - Bottleneck Time = Σ(Total Time * Bottleneck Time(s)) / Σ(Total Time)
        """
        if not time_values or not weight_values or len(time_values) != len(weight_values):
            return Decimal('0.00')
        
        total_weighted = sum(t * w for t, w in zip(time_values, weight_values))
        total_weights = sum(weight_values)
        
        if total_weights <= 0:
            return Decimal('0.00')
        
        weighted_avg = total_weighted / total_weights
        return Decimal(weighted_avg).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_weighted_manpower(
        manpower_values: list, 
        total_time_values: list
    ) -> Decimal:
        """
        Calculate weighted average manpower
        Formula: Σ(Total Time * Manpower) / Σ(Total Time)
        """
        if not manpower_values or not total_time_values or len(manpower_values) != len(total_time_values):
            return Decimal('0.00')
        
        total_weighted = sum(m * t for m, t in zip(manpower_values, total_time_values))
        total_time = sum(total_time_values)
        
        if total_time <= 0:
            return Decimal('0.00')
        
        weighted_manpower = total_weighted / total_time
        return Decimal(weighted_manpower).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @classmethod
    def calculate_all_metrics(
        cls,
        total_time_minutes: int,
        planned_break_minutes: int,
        downtime_minutes: int,
        uph: int,
        manpower: int,
        actual_quantity: int,
        rejection_quantity: int,
        bottleneck_time_seconds: Decimal
    ) -> dict:
        """
        Calculate all production metrics in one call
        Returns a dictionary with all calculated values
        """
        # Basic calculations
        machine_runtime_hours = cls.calculate_machine_runtime_hours(
            total_time_minutes, planned_break_minutes, downtime_minutes
        )

        target_quantity = cls.calculate_target_quantity(total_time_minutes, planned_break_minutes, uph)

        line_capacity_calculated = cls.calculate_line_capacity(machine_runtime_hours, uph)

        actual_ok_quantity = max(0, actual_quantity - rejection_quantity)

        efficiency_percentage = cls.calculate_efficiency_percentage(actual_quantity, target_quantity)

        quality_percentage = cls.calculate_quality_percentage(actual_ok_quantity, actual_quantity)

        upph_actual = cls.calculate_upph_actual(actual_quantity, machine_runtime_hours, manpower)

        return {
            'machine_runtime_hours': machine_runtime_hours,
            'target_quantity': target_quantity,
            'actual_ok_quantity': actual_ok_quantity,
            'efficiency_percentage': efficiency_percentage,
            'quality_percentage': quality_percentage,
            'upph_actual': upph_actual,
            'line_capacity_calculated': line_capacity_calculated,
        }
