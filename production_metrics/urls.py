from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    ShiftViewSet, LineCapacityViewSet, ProductionScheduleViewSet,
    ProductionMetricsViewSet, DowntimeLogViewSet
)

# Create a router and register our viewsets
router = DefaultRouter()
router.register(r'shifts', ShiftViewSet, basename='shift')
router.register(r'line-capacities', LineCapacityViewSet, basename='line-capacity')
router.register(r'schedules', ProductionScheduleViewSet, basename='production-schedule')
router.register(r'metrics', ProductionMetricsViewSet, basename='production-metrics')
router.register(r'downtime-logs', DowntimeLogViewSet, basename='downtime-log')

urlpatterns = [
    path('', include(router.urls)),
]
