from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import Shift, LineCapacity, ProductionSchedule, ProductionMetrics, DowntimeLog


@admin.register(Shift)
class ShiftAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'start_time', 'end_time', 'duration_hours', 'is_active')
    list_filter = ('is_active', 'duration_hours')
    search_fields = ('name', 'code')
    ordering = ('start_time',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Time Configuration', {
            'fields': ('start_time', 'end_time', 'duration_hours')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    readonly_fields = ('created_at', 'updated_at')


@admin.register(LineCapacity)
class LineCapacityAdmin(admin.ModelAdmin):
    list_display = (
        'capacity_id', 'line', 'product', 'uph', 'manpower', 'upph', 
        'standard_time_seconds', 'bottleneck_time_seconds', 'is_active'
    )
    list_filter = ('is_active', 'line', 'product__type_id')
    search_fields = ('line__name', 'product__name', 'product__code')
    ordering = ('line__name', 'product__name')
    
    fieldsets = (
        ('Configuration', {
            'fields': ('line', 'product', 'is_active')
        }),
        ('Capacity Metrics', {
            'fields': ('uph', 'manpower', 'upph')
        }),
        ('Time Standards', {
            'fields': ('standard_time_seconds', 'bottleneck_time_seconds')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def capacity_id(self, obj):
        return obj.capacity_id
    capacity_id.short_description = 'Capacity ID'


@admin.register(ProductionSchedule)
class ProductionScheduleAdmin(admin.ModelAdmin):
    list_display = (
        'production_date', 'start_time', 'end_time', 'line', 'work_order', 
        'shift', 'manpower', 'actual_quantity', 'rejection_quantity', 
        'has_metrics', 'created_by'
    )
    list_filter = (
        'production_date', 'line', 'shift', 'created_by'
    )
    search_fields = (
        'work_order__order_no', 'work_order__part_no', 'work_order__customer',
        'line__name', 'downtime_reason'
    )
    ordering = ('-production_date', 'start_time', 'line__name')
    date_hierarchy = 'production_date'
    
    fieldsets = (
        ('Schedule Information', {
            'fields': ('production_date', 'start_time', 'end_time', 'line', 'work_order', 'shift')
        }),
        ('Production Data', {
            'fields': ('manpower', 'actual_quantity', 'rejection_quantity')
        }),
        ('Downtime Information', {
            'fields': ('planned_break_minutes', 'downtime_minutes', 'downtime_reason')
        }),
        ('Calculated Properties', {
            'fields': ('total_time_display', 'machine_runtime_display', 'actual_ok_quantity_display'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    readonly_fields = (
        'total_time_display', 'machine_runtime_display', 'actual_ok_quantity_display',
        'created_at', 'updated_at'
    )
    
    def has_metrics(self, obj):
        """Show if metrics have been calculated"""
        try:
            obj.metrics
            return format_html('<span style="color: green;">✓</span>')
        except ProductionMetrics.DoesNotExist:
            return format_html('<span style="color: red;">✗</span>')
    has_metrics.short_description = 'Metrics'
    has_metrics.admin_order_field = 'metrics'
    
    def total_time_display(self, obj):
        return f"{obj.total_time_minutes} minutes"
    total_time_display.short_description = 'Total Time'
    
    def machine_runtime_display(self, obj):
        return f"{obj.machine_runtime_minutes} minutes"
    machine_runtime_display.short_description = 'Machine Runtime'
    
    def actual_ok_quantity_display(self, obj):
        return obj.actual_ok_quantity
    actual_ok_quantity_display.short_description = 'Actual OK Qty'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'line', 'work_order', 'shift', 'created_by'
        ).prefetch_related('metrics')


@admin.register(ProductionMetrics)
class ProductionMetricsAdmin(admin.ModelAdmin):
    list_display = (
        'schedule_info', 'product_name', 'machine_runtime_hours', 'uph', 'target_quantity', 'line_capacity_calculated', 'actual_ok_quantity',
        'efficiency_percentage', 'quality_percentage', 'upph_actual'
    )
    list_filter = (
        'schedule__production_date', 'schedule__line', 'line_capacity__product'
    )
    search_fields = (
        'schedule__work_order__order_no', 'schedule__work_order__part_no',
        'schedule__line__name', 'line_capacity__product__name'
    )
    ordering = ('-schedule__production_date', 'schedule__start_time')
    date_hierarchy = 'schedule__production_date'
    
    fieldsets = (
        ('Schedule Reference', {
            'fields': ('schedule', 'line_capacity')
        }),
        ('Calculated Metrics', {
            'fields': (
                'machine_runtime_hours', 'target_quantity', 'actual_ok_quantity',
                'efficiency_percentage', 'quality_percentage', 'upph_actual',
                'line_capacity_calculated'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def schedule_info(self, obj):
        """Display schedule information"""
        schedule = obj.schedule
        return f"{schedule.line.name} - {schedule.production_date} {schedule.start_time}"
    schedule_info.short_description = 'Schedule'
    schedule_info.admin_order_field = 'schedule__production_date'

    def product_name(self, obj):
        """Display product name"""
        return obj.schedule.work_order.product.name
    product_name.short_description = 'Product'
    product_name.admin_order_field = 'schedule__work_order__product__name'

    def uph(self, obj):
        """Display UPH from line capacity"""
        return obj.line_capacity.uph
    uph.short_description = 'UPH'
    uph.admin_order_field = 'line_capacity__uph'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'schedule__line', 'schedule__work_order', 'line_capacity__product'
        )


@admin.register(DowntimeLog)
class DowntimeLogAdmin(admin.ModelAdmin):
    list_display = (
        'schedule_info', 'downtime_type', 'reason', 'duration_minutes',
        'start_time', 'end_time', 'logged_by'
    )
    list_filter = (
        'downtime_type', 'reason', 'schedule__production_date', 
        'schedule__line', 'logged_by'
    )
    search_fields = (
        'reason', 'description', 'schedule__work_order__order_no',
        'schedule__line__name'
    )
    ordering = ('-start_time',)
    date_hierarchy = 'start_time'
    
    fieldsets = (
        ('Downtime Information', {
            'fields': ('schedule', 'downtime_type', 'duration_minutes')
        }),
        ('Details', {
            'fields': ('reason', 'description')
        }),
        ('Timing', {
            'fields': ('start_time', 'end_time')
        }),
        ('Metadata', {
            'fields': ('logged_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def schedule_info(self, obj):
        """Display schedule information"""
        schedule = obj.schedule
        return f"{schedule.line.name} - {schedule.production_date} {schedule.start_time}"
    schedule_info.short_description = 'Schedule'
    schedule_info.admin_order_field = 'schedule__production_date'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'schedule__line', 'schedule__work_order', 'logged_by'
        )
