from rest_framework import viewsets, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from datetime import datetime

from .models import Shift, LineCapacity, ProductionSchedule, ProductionMetrics, DowntimeLog
from .serializers import (
    ShiftSerializer, LineCapacitySerializer, ProductionScheduleSerializer,
    ProductionMetricsSerializer, DowntimeLogSerializer
)


class ShiftViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Shift objects

    Provides CRUD operations for production shifts with filtering capabilities.
    """
    queryset = Shift.objects.all()
    serializer_class = ShiftSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'duration_hours']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'start_time', 'end_time', 'duration_hours', 'created_at']
    ordering = ['start_time']

    @action(detail=False, methods=['get'])
    def by_time_range(self, request):
        """
        Filter shifts that have ANY overlap with the query time range

        Query parameters:
        - start_time: Start time of the query range (HH:MM format)
        - end_time: End time of the query range (HH:MM format)

        Returns shifts where the query time range overlaps with any part of the shift's time range.
        Handles shifts that cross midnight (e.g., 22:00 - 06:00).
        """
        start_time_str = request.query_params.get('start_time')
        end_time_str = request.query_params.get('end_time')

        # Both start_time and end_time are required for overlap detection
        if not start_time_str or not end_time_str:
            return Response(
                {'error': 'Both start_time and end_time parameters are required for overlap detection.'},
                status=400
            )

        try:
            query_start = datetime.strptime(start_time_str, '%H:%M').time()
            query_end = datetime.strptime(end_time_str, '%H:%M').time()
        except ValueError:
            return Response(
                {'error': 'Invalid time format. Use HH:MM format for both start_time and end_time.'},
                status=400
            )

        # Note: We allow query_start >= query_end for queries that cross midnight
        # e.g., start_time=23:00, end_time=01:00 (23:00 to 01:00 next day)

        # Get all shifts and filter using Python logic for complex overlap detection
        all_shifts = self.get_queryset()
        overlapping_shifts = []

        for shift in all_shifts:
            if self._shifts_overlap(shift.start_time, shift.end_time, query_start, query_end):
                overlapping_shifts.append(shift)

        # Convert to queryset for consistent response format
        shift_ids = [shift.id for shift in overlapping_shifts]
        queryset = self.get_queryset().filter(id__in=shift_ids)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def _shifts_overlap(self, shift_start, shift_end, query_start, query_end):
        """
        Check if a shift's time range overlaps with the query time range.

        Handles both normal shifts (within same day) and shifts that cross midnight.
        Also handles queries that cross midnight.

        Args:
            shift_start: Shift start time
            shift_end: Shift end time
            query_start: Query start time
            query_end: Query end time

        Returns:
            bool: True if there is any overlap, False otherwise
        """
        # Convert times to minutes for easier calculation
        shift_start_min = shift_start.hour * 60 + shift_start.minute
        shift_end_min = shift_end.hour * 60 + shift_end.minute
        query_start_min = query_start.hour * 60 + query_start.minute
        query_end_min = query_end.hour * 60 + query_end.minute

        # Check if shift or query crosses midnight
        shift_crosses_midnight = shift_start_min >= shift_end_min
        query_crosses_midnight = query_start_min >= query_end_min

        if shift_crosses_midnight and query_crosses_midnight:
            # Both shift and query cross midnight
            # This is complex, so we'll check all combinations

            # Shift parts
            shift_evening = (shift_start_min, 24 * 60)
            shift_morning = (0, shift_end_min)

            # Query parts
            query_evening = (query_start_min, 24 * 60)
            query_morning = (0, query_end_min)

            # Check all combinations
            return (
                self._time_ranges_overlap(*shift_evening, *query_evening) or
                self._time_ranges_overlap(*shift_evening, *query_morning) or
                self._time_ranges_overlap(*shift_morning, *query_evening) or
                self._time_ranges_overlap(*shift_morning, *query_morning)
            )

        elif shift_crosses_midnight:
            # Only shift crosses midnight, query is normal
            # Split shift into two parts: evening part and morning part

            # Evening part: shift_start to 24:00 (1440 minutes)
            evening_start = shift_start_min
            evening_end = 24 * 60  # 1440 minutes (midnight)

            # Morning part: 00:00 to shift_end
            morning_start = 0
            morning_end = shift_end_min

            # Check overlap with evening part
            evening_overlap = self._time_ranges_overlap(
                evening_start, evening_end, query_start_min, query_end_min
            )

            # Check overlap with morning part
            morning_overlap = self._time_ranges_overlap(
                morning_start, morning_end, query_start_min, query_end_min
            )

            return evening_overlap or morning_overlap

        elif query_crosses_midnight:
            # Only query crosses midnight, shift is normal
            # Split query into two parts: evening part and morning part

            # Evening part: query_start to 24:00
            query_evening_start = query_start_min
            query_evening_end = 24 * 60

            # Morning part: 00:00 to query_end
            query_morning_start = 0
            query_morning_end = query_end_min

            # Check overlap with evening part of query
            evening_overlap = self._time_ranges_overlap(
                shift_start_min, shift_end_min, query_evening_start, query_evening_end
            )

            # Check overlap with morning part of query
            morning_overlap = self._time_ranges_overlap(
                shift_start_min, shift_end_min, query_morning_start, query_morning_end
            )

            return evening_overlap or morning_overlap
        else:
            # Neither shift nor query crosses midnight - normal case
            return self._time_ranges_overlap(
                shift_start_min, shift_end_min, query_start_min, query_end_min
            )

    def _time_ranges_overlap(self, range1_start, range1_end, range2_start, range2_end):
        """
        Check if two time ranges overlap.

        Two ranges overlap if: range1_start < range2_end AND range2_start < range1_end

        Args:
            range1_start: Start of first range (in minutes)
            range1_end: End of first range (in minutes)
            range2_start: Start of second range (in minutes)
            range2_end: End of second range (in minutes)

        Returns:
            bool: True if ranges overlap, False otherwise
        """
        return range1_start < range2_end and range2_start < range1_end


class LineCapacityViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing LineCapacity objects

    Provides CRUD operations for line capacity configurations.
    """
    queryset = LineCapacity.objects.select_related('line', 'product').all()
    serializer_class = LineCapacitySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['line', 'product', 'is_active', 'line__area']
    search_fields = ['line__name', 'product__name', 'product__code']
    ordering_fields = ['line__name', 'product__name', 'uph', 'manpower', 'created_at']
    ordering = ['line__name', 'product__name']

    def get_queryset(self):
        """Optimize queryset with select_related"""
        return super().get_queryset().select_related('line', 'product')


class ProductionScheduleViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing ProductionSchedule objects

    Provides CRUD operations for production schedules with comprehensive filtering.
    """
    queryset = ProductionSchedule.objects.select_related(
        'line', 'work_order', 'shift', 'created_by'
    ).prefetch_related('metrics').all()
    serializer_class = ProductionScheduleSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'production_date', 'line', 'work_order', 'shift', 'created_by'
    ]
    search_fields = [
        'work_order__order_no', 'work_order__part_no', 'work_order__customer',
        'line__name', 'downtime_reason'
    ]
    ordering_fields = [
        'production_date', 'start_time', 'line__name', 'actual_quantity', 'created_at'
    ]
    ordering = ['-production_date', 'start_time', 'line__name']

    def get_queryset(self):
        """Optimize queryset with select_related and prefetch_related"""
        return super().get_queryset().select_related(
            'line', 'work_order', 'shift', 'created_by'
        ).prefetch_related('metrics')

    def perform_create(self, serializer):
        """Set the created_by field when creating a new schedule"""
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def by_date_range(self, request):
        """
        Filter schedules by date range

        Query parameters:
        - start_date: Start date (YYYY-MM-DD format)
        - end_date: End date (YYYY-MM-DD format)
        """
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        queryset = self.get_queryset()

        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(production_date__gte=start_date)
            except ValueError:
                return Response(
                    {'error': 'Invalid start_date format. Use YYYY-MM-DD format.'},
                    status=400
                )

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(production_date__lte=end_date)
            except ValueError:
                return Response(
                    {'error': 'Invalid end_date format. Use YYYY-MM-DD format.'},
                    status=400
                )

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class ProductionMetricsViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing ProductionMetrics objects (read-only)

    Production metrics are auto-calculated and cannot be directly modified.
    """
    queryset = ProductionMetrics.objects.select_related(
        'schedule__line', 'schedule__work_order', 'line_capacity__product'
    ).all()
    serializer_class = ProductionMetricsSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'schedule__production_date', 'schedule__line', 'schedule__work_order',
        'line_capacity__product'
    ]
    search_fields = [
        'schedule__work_order__order_no', 'schedule__work_order__customer',
        'schedule__line__name', 'line_capacity__product__name'
    ]
    ordering_fields = [
        'schedule__production_date', 'efficiency_percentage', 'quality_percentage',
        'target_quantity', 'actual_ok_quantity', 'created_at'
    ]
    ordering = ['-schedule__production_date', 'schedule__start_time']

    def get_queryset(self):
        """Optimize queryset with select_related"""
        return super().get_queryset().select_related(
            'schedule__line', 'schedule__work_order', 'schedule__shift',
            'line_capacity__line', 'line_capacity__product'
        )

    @action(detail=False, methods=['get'])
    def efficiency_above(self, request):
        """Get metrics with efficiency above specified threshold"""
        threshold = request.query_params.get('threshold', 100)
        try:
            threshold = float(threshold)
            queryset = self.get_queryset().filter(efficiency_percentage__gte=threshold)

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except ValueError:
            return Response(
                {'error': 'Invalid threshold value. Must be a number.'},
                status=400
            )

    @action(detail=False, methods=['get'])
    def quality_below(self, request):
        """Get metrics with quality below specified threshold"""
        threshold = request.query_params.get('threshold', 95)
        try:
            threshold = float(threshold)
            queryset = self.get_queryset().filter(quality_percentage__lt=threshold)

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except ValueError:
            return Response(
                {'error': 'Invalid threshold value. Must be a number.'},
                status=400
            )


class DowntimeLogViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing DowntimeLog objects

    Provides CRUD operations for downtime tracking.
    """
    queryset = DowntimeLog.objects.select_related(
        'schedule__line', 'schedule__work_order', 'logged_by'
    ).all()
    serializer_class = DowntimeLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'downtime_type', 'reason', 'schedule__production_date',
        'schedule__line', 'logged_by'
    ]
    search_fields = [
        'reason', 'description', 'schedule__work_order__order_no',
        'schedule__line__name'
    ]
    ordering_fields = [
        'start_time', 'duration_minutes', 'schedule__production_date', 'created_at'
    ]
    ordering = ['-start_time']

    def get_queryset(self):
        """Optimize queryset with select_related"""
        return super().get_queryset().select_related(
            'schedule__line', 'schedule__work_order', 'logged_by'
        )

    def perform_create(self, serializer):
        """Set the logged_by field when creating a new downtime log"""
        serializer.save(logged_by=self.request.user)

    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """Filter downtime logs by type"""
        downtime_type = request.query_params.get('type')
        if not downtime_type:
            return Response(
                {'error': 'type parameter is required'},
                status=400
            )

        if downtime_type not in ['planned', 'unplanned']:
            return Response(
                {'error': 'type must be either "planned" or "unplanned"'},
                status=400
            )

        queryset = self.get_queryset().filter(downtime_type=downtime_type)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def duration_above(self, request):
        """Get downtime logs with duration above specified minutes"""
        threshold = request.query_params.get('threshold', 30)
        try:
            threshold = int(threshold)
            queryset = self.get_queryset().filter(duration_minutes__gte=threshold)

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except ValueError:
            return Response(
                {'error': 'Invalid threshold value. Must be an integer.'},
                status=400
            )
