"""
Management command to test the ProductionMetricsSerializer structure
"""
from django.core.management.base import BaseCommand
from production_metrics.serializers import ProductionMetricsSerializer


class Command(BaseCommand):
    help = 'Test the ProductionMetricsSerializer structure and configuration'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Testing ProductionMetricsSerializer Structure...')
        )

        try:
            # Create a serializer instance
            serializer = ProductionMetricsSerializer()
            
            # Check Meta configuration
            meta = serializer.Meta
            
            self.stdout.write("\nSerializer Configuration:")
            self.stdout.write(f"  Model: {meta.model.__name__}")
            self.stdout.write(f"  Fields count: {len(meta.fields)}")
            self.stdout.write(f"  Read-only fields count: {len(meta.read_only_fields)}")
            self.stdout.write(f"  Read-only fields type: {type(meta.read_only_fields)}")
            
            # Verify read_only_fields is a list/tuple
            if isinstance(meta.read_only_fields, (list, tuple)):
                self.stdout.write(
                    self.style.SUCCESS("  ✓ read_only_fields is correctly configured as list/tuple")
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f"  ✗ read_only_fields is {type(meta.read_only_fields)}, should be list/tuple")
                )
            
            # Check if all fields are marked as read-only
            if set(meta.fields) == set(meta.read_only_fields):
                self.stdout.write(
                    self.style.SUCCESS("  ✓ All fields are correctly marked as read-only")
                )
            else:
                writable_fields = set(meta.fields) - set(meta.read_only_fields)
                self.stdout.write(
                    self.style.WARNING(f"  ⚠ Some fields are writable: {writable_fields}")
                )
            
            # Test serializer instantiation
            self.stdout.write("\nTesting serializer instantiation...")
            
            # This should not raise any errors
            fields = serializer.get_fields()
            self.stdout.write(f"  Serializer fields: {len(fields)} fields loaded successfully")
            
            # Check specific field types
            important_fields = ['schedule_info', 'line_name', 'production_date', 'work_order_number']
            for field_name in important_fields:
                if field_name in fields:
                    field = fields[field_name]
                    self.stdout.write(f"  {field_name}: {type(field).__name__} (read_only: {field.read_only})")
                else:
                    self.stdout.write(f"  {field_name}: NOT FOUND")
            
            self.stdout.write(
                self.style.SUCCESS('\nProductionMetricsSerializer is correctly configured!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error testing serializer: {e}')
            )
            import traceback
            self.stdout.write(traceback.format_exc())
