"""
Management command to test the overlap detection logic in ShiftViewSet.by_time_range
"""
from django.core.management.base import BaseCommand
from datetime import time
from production_metrics.views import ShiftViewSet


class Command(BaseCommand):
    help = 'Test overlap detection logic in ShiftViewSet.by_time_range method'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed test steps',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Testing Overlap Detection Logic...')
        )

        # Create a ShiftViewSet instance for testing
        viewset = ShiftViewSet()

        # Test cases based on the requirements
        test_cases = [
            {
                'name': 'Example 1: Query within shift (should overlap)',
                'shift_start': time(6, 0),   # 06:00
                'shift_end': time(9, 0),     # 09:00
                'query_start': time(7, 0),   # 07:00
                'query_end': time(8, 0),     # 08:00
                'expected': True,
                'description': 'Query is completely within shift'
            },
            {
                'name': 'Example 2: Partial overlap at end (should overlap)',
                'shift_start': time(6, 0),   # 06:00
                'shift_end': time(9, 0),     # 09:00
                'query_start': time(8, 0),   # 08:00
                'query_end': time(10, 0),    # 10:00
                'expected': True,
                'description': 'Query overlaps with end of shift'
            },
            {
                'name': 'Example 3: No overlap (should NOT overlap)',
                'shift_start': time(6, 0),   # 06:00
                'shift_end': time(9, 0),     # 09:00
                'query_start': time(10, 0),  # 10:00
                'query_end': time(11, 0),    # 11:00
                'expected': False,
                'description': 'Query is completely after shift'
            },
            {
                'name': 'Example 4: Partial overlap at start (should overlap)',
                'shift_start': time(6, 0),   # 06:00
                'shift_end': time(9, 0),     # 09:00
                'query_start': time(5, 0),   # 05:00
                'query_end': time(7, 0),     # 07:00
                'expected': True,
                'description': 'Query overlaps with start of shift'
            },
            {
                'name': 'Query completely before shift (should NOT overlap)',
                'shift_start': time(6, 0),   # 06:00
                'shift_end': time(9, 0),     # 09:00
                'query_start': time(3, 0),   # 03:00
                'query_end': time(5, 0),     # 05:00
                'expected': False,
                'description': 'Query is completely before shift'
            },
            {
                'name': 'Query completely encompasses shift (should overlap)',
                'shift_start': time(6, 0),   # 06:00
                'shift_end': time(9, 0),     # 09:00
                'query_start': time(5, 0),   # 05:00
                'query_end': time(10, 0),    # 10:00
                'expected': True,
                'description': 'Query completely contains shift'
            },
            {
                'name': 'Exact match (should overlap)',
                'shift_start': time(6, 0),   # 06:00
                'shift_end': time(9, 0),     # 09:00
                'query_start': time(6, 0),   # 06:00
                'query_end': time(9, 0),     # 09:00
                'expected': True,
                'description': 'Query exactly matches shift'
            },
            # Midnight crossing shift tests
            {
                'name': 'Night shift - query in evening part (should overlap)',
                'shift_start': time(22, 0),  # 22:00
                'shift_end': time(6, 0),     # 06:00
                'query_start': time(23, 0),  # 23:00
                'query_end': time(23, 59),   # 23:59
                'expected': True,
                'description': 'Query in evening part of midnight-crossing shift'
            },
            {
                'name': 'Night shift - query in morning part (should overlap)',
                'shift_start': time(22, 0),  # 22:00
                'shift_end': time(6, 0),     # 06:00
                'query_start': time(1, 0),   # 01:00
                'query_end': time(2, 0),     # 02:00
                'expected': True,
                'description': 'Query in morning part of midnight-crossing shift'
            },
            {
                'name': 'Night shift - query spans across midnight (should overlap)',
                'shift_start': time(22, 0),  # 22:00
                'shift_end': time(6, 0),     # 06:00
                'query_start': time(23, 0),  # 23:00
                'query_end': time(1, 0),     # 01:00
                'expected': True,
                'description': 'Query spans across midnight within shift'
            },
            {
                'name': 'Night shift - query in middle of day (should NOT overlap)',
                'shift_start': time(22, 0),  # 22:00
                'shift_end': time(6, 0),     # 06:00
                'query_start': time(12, 0),  # 12:00
                'query_end': time(13, 0),    # 13:00
                'expected': False,
                'description': 'Query in middle of day, no overlap with night shift'
            },
            {
                'name': 'Night shift - query overlaps evening part only',
                'shift_start': time(22, 0),  # 22:00
                'shift_end': time(6, 0),     # 06:00
                'query_start': time(21, 0),  # 21:00
                'query_end': time(23, 0),    # 23:00
                'expected': True,
                'description': 'Query overlaps with evening part of night shift'
            },
            {
                'name': 'Night shift - query overlaps morning part only',
                'shift_start': time(22, 0),  # 22:00
                'shift_end': time(6, 0),     # 06:00
                'query_start': time(5, 0),   # 05:00
                'query_end': time(7, 0),     # 07:00
                'expected': True,
                'description': 'Query overlaps with morning part of night shift'
            }
        ]

        passed_tests = 0
        total_tests = len(test_cases)

        for test_case in test_cases:
            if options['verbose']:
                self.stdout.write(f"\nTesting: {test_case['name']}")
                self.stdout.write(f"  Shift: {test_case['shift_start']} - {test_case['shift_end']}")
                self.stdout.write(f"  Query: {test_case['query_start']} - {test_case['query_end']}")
                self.stdout.write(f"  Description: {test_case['description']}")

            try:
                # Test the overlap detection logic
                result = viewset._shifts_overlap(
                    test_case['shift_start'],
                    test_case['shift_end'],
                    test_case['query_start'],
                    test_case['query_end']
                )

                if result == test_case['expected']:
                    passed_tests += 1
                    if options['verbose']:
                        self.stdout.write(
                            self.style.SUCCESS(f"  ✓ PASSED (Expected: {test_case['expected']}, Got: {result})")
                        )
                else:
                    if options['verbose']:
                        self.stdout.write(
                            self.style.ERROR(f"  ✗ FAILED (Expected: {test_case['expected']}, Got: {result})")
                        )

            except Exception as e:
                if options['verbose']:
                    self.stdout.write(
                        self.style.ERROR(f"  ✗ ERROR (Exception: {e})")
                    )

        # Summary
        self.stdout.write(f"\nOverlap Detection Test Results: {passed_tests}/{total_tests} tests passed")

        if passed_tests == total_tests:
            self.stdout.write(
                self.style.SUCCESS('All overlap detection tests passed!')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'{total_tests - passed_tests} tests failed!')
            )

        # Additional validation info
        if options['verbose']:
            self.stdout.write("\nOverlap Detection Logic:")
            self.stdout.write("- Two ranges overlap if: range1_start < range2_end AND range2_start < range1_end")
            self.stdout.write("- Midnight-crossing shifts are split into evening and morning parts")
            self.stdout.write("- Each part is checked separately for overlap with the query range")
