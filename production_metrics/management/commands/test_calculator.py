"""
Management command to test the ProductionMetricsCalculator service
"""
from django.core.management.base import BaseCommand
from decimal import Decimal

from production_metrics.services.calculator import ProductionMetricsCalculator


class Command(BaseCommand):
    help = 'Test the ProductionMetricsCalculator service with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed calculation steps',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Testing ProductionMetricsCalculator...')
        )

        # Sample data from the data dictionary
        test_data = {
            'total_time_minutes': 60,  # 1 hour
            'planned_break_minutes': 10,
            'downtime_minutes': 5,
            'uph': 240,
            'manpower': 5,
            'actual_quantity': 200,
            'rejection_quantity': 5,
            'bottleneck_time_seconds': Decimal('15.0')
        }

        if options['verbose']:
            self.stdout.write('Input data:')
            for key, value in test_data.items():
                self.stdout.write(f'  {key}: {value}')
            self.stdout.write('')

        # Calculate all metrics
        calculator = ProductionMetricsCalculator()
        results = calculator.calculate_all_metrics(**test_data)

        self.stdout.write('Calculated metrics:')
        for key, value in results.items():
            self.stdout.write(f'  {key}: {value}')

        # Individual calculations for verification
        if options['verbose']:
            self.stdout.write('\nDetailed calculations:')
            
            runtime = calculator.calculate_machine_runtime_hours(
                test_data['total_time_minutes'],
                test_data['planned_break_minutes'],
                test_data['downtime_minutes']
            )
            self.stdout.write(f'  Machine runtime: {runtime} hours')
            
            target = calculator.calculate_target_quantity(runtime, test_data['uph'])
            self.stdout.write(f'  Target quantity: {target}')
            
            efficiency = calculator.calculate_efficiency_percentage(
                test_data['actual_quantity'], target
            )
            self.stdout.write(f'  Efficiency: {efficiency}%')
            
            quality = calculator.calculate_quality_percentage(
                test_data['actual_quantity'] - test_data['rejection_quantity'],
                test_data['actual_quantity']
            )
            self.stdout.write(f'  Quality: {quality}%')

        self.stdout.write(
            self.style.SUCCESS('\nCalculator test completed successfully!')
        )
