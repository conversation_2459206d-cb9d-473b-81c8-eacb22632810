"""
Management command to test shift validation in ProductionSchedule
"""
from django.core.management.base import BaseCommand
from django.core.exceptions import ValidationError
from datetime import time, date
from production_metrics.models import Shift, ProductionSchedule


class Command(BaseCommand):
    help = 'Test shift validation logic in ProductionSchedule model'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed validation steps',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Testing Shift Validation in ProductionSchedule...')
        )

        # Test cases for shift validation
        test_cases = [
            {
                'name': 'Normal Day Shift - Valid',
                'shift_start': time(8, 0),
                'shift_end': time(16, 0),
                'schedule_start': time(9, 0),
                'schedule_end': time(10, 0),
                'should_pass': True
            },
            {
                'name': 'Normal Day Shift - Invalid (outside range)',
                'shift_start': time(8, 0),
                'shift_end': time(16, 0),
                'schedule_start': time(17, 0),
                'schedule_end': time(18, 0),
                'should_pass': False
            },
            {
                'name': 'Normal Day Shift - Valid (with tolerance)',
                'shift_start': time(8, 0),
                'shift_end': time(16, 0),
                'schedule_start': time(7, 50),  # 10 minutes before shift start
                'schedule_end': time(8, 50),
                'should_pass': True
            },
            {
                'name': 'Night Shift Crossing Midnight - Valid (evening part)',
                'shift_start': time(22, 0),
                'shift_end': time(6, 0),
                'schedule_start': time(23, 0),
                'schedule_end': time(23, 59),
                'should_pass': True
            },
            {
                'name': 'Night Shift Crossing Midnight - Valid (morning part)',
                'shift_start': time(22, 0),
                'shift_end': time(6, 0),
                'schedule_start': time(1, 0),
                'schedule_end': time(2, 0),
                'should_pass': True
            },
            {
                'name': 'Night Shift Crossing Midnight - Invalid',
                'shift_start': time(22, 0),
                'shift_end': time(6, 0),
                'schedule_start': time(12, 0),  # Middle of day
                'schedule_end': time(13, 0),
                'should_pass': False
            },
        ]

        passed_tests = 0
        total_tests = len(test_cases)

        for test_case in test_cases:
            if options['verbose']:
                self.stdout.write(f"\nTesting: {test_case['name']}")
                self.stdout.write(
                    f"  Shift: {test_case['shift_start']} - {test_case['shift_end']}"
                )
                self.stdout.write(
                    f"  Schedule: {test_case['schedule_start']} - {test_case['schedule_end']}"
                )

            try:
                # Create a test shift (not saved to database)
                shift = Shift(
                    name=f"Test Shift {test_case['name']}",
                    code=f"TEST_{test_case['name'][:3]}",
                    start_time=test_case['shift_start'],
                    end_time=test_case['shift_end'],
                    duration_hours=8.0  # Placeholder
                )

                # Create a test schedule (not saved to database)
                schedule = ProductionSchedule(
                    production_date=date.today(),
                    start_time=test_case['schedule_start'],
                    end_time=test_case['schedule_end'],
                    shift=shift,
                    planned_break_minutes=0,
                    downtime_minutes=0,
                    manpower=1
                )

                # Test the validation
                schedule._validate_shift_alignment()
                
                if test_case['should_pass']:
                    passed_tests += 1
                    if options['verbose']:
                        self.stdout.write(
                            self.style.SUCCESS("  ✓ PASSED (validation succeeded as expected)")
                        )
                else:
                    if options['verbose']:
                        self.stdout.write(
                            self.style.ERROR("  ✗ FAILED (validation should have failed)")
                        )

            except ValidationError as e:
                if not test_case['should_pass']:
                    passed_tests += 1
                    if options['verbose']:
                        self.stdout.write(
                            self.style.SUCCESS(f"  ✓ PASSED (validation failed as expected: {e})")
                        )
                else:
                    if options['verbose']:
                        self.stdout.write(
                            self.style.ERROR(f"  ✗ FAILED (unexpected validation error: {e})")
                        )

            except Exception as e:
                if options['verbose']:
                    self.stdout.write(
                        self.style.ERROR(f"  ✗ ERROR (unexpected exception: {e})")
                    )

        # Summary
        self.stdout.write(f"\nTest Results: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            self.stdout.write(
                self.style.SUCCESS('All shift validation tests passed!')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'{total_tests - passed_tests} tests failed!')
            )
