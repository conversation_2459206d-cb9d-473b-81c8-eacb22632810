"""
Management command to test API endpoints for production metrics
"""
from django.core.management.base import BaseCommand
from django.urls import reverse
from django.test import Client
from django.contrib.auth import get_user_model
from rest_framework import status

User = get_user_model()


class Command(BaseCommand):
    help = 'Test API endpoints for production metrics'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed API responses',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Testing Production Metrics API Endpoints...')
        )

        # Create a test client
        client = Client()

        # Test endpoints (without authentication for now)
        endpoints = [
            {
                'name': 'Shifts List',
                'url': '/mes_trace/production-metrics/api/shifts/',
                'method': 'GET'
            },
            {
                'name': 'Shifts by Time Range',
                'url': '/mes_trace/production-metrics/api/shifts/by_time_range/?start_time=08:00&end_time=16:00',
                'method': 'GET'
            },
            {
                'name': 'Line Capacities List',
                'url': '/mes_trace/production-metrics/api/line-capacities/',
                'method': 'GET'
            },
            {
                'name': 'Production Schedules List',
                'url': '/mes_trace/production-metrics/api/schedules/',
                'method': 'GET'
            },
            {
                'name': 'Production Metrics List',
                'url': '/mes_trace/production-metrics/api/metrics/',
                'method': 'GET'
            },
            {
                'name': 'Downtime Logs List',
                'url': '/mes_trace/production-metrics/api/downtime-logs/',
                'method': 'GET'
            },
        ]

        passed_tests = 0
        total_tests = len(endpoints)

        for endpoint in endpoints:
            if options['verbose']:
                self.stdout.write(f"\nTesting: {endpoint['name']}")
                self.stdout.write(f"  URL: {endpoint['url']}")

            try:
                if endpoint['method'] == 'GET':
                    response = client.get(endpoint['url'])
                else:
                    response = client.post(endpoint['url'])

                # Check if endpoint is accessible (even if authentication is required)
                if response.status_code in [200, 401, 403]:  # 401/403 means endpoint exists but needs auth
                    passed_tests += 1
                    if options['verbose']:
                        status_msg = f"Status: {response.status_code}"
                        if response.status_code == 200:
                            self.stdout.write(
                                self.style.SUCCESS(f"  ✓ PASSED ({status_msg})")
                            )
                        else:
                            self.stdout.write(
                                self.style.WARNING(f"  ✓ PASSED ({status_msg} - needs authentication)")
                            )
                else:
                    if options['verbose']:
                        self.stdout.write(
                            self.style.ERROR(f"  ✗ FAILED (Status: {response.status_code})")
                        )

            except Exception as e:
                if options['verbose']:
                    self.stdout.write(
                        self.style.ERROR(f"  ✗ ERROR (Exception: {e})")
                    )

        # Summary
        self.stdout.write(f"\nAPI Test Results: {passed_tests}/{total_tests} endpoints accessible")
        
        if passed_tests == total_tests:
            self.stdout.write(
                self.style.SUCCESS('All API endpoints are accessible!')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'{total_tests - passed_tests} endpoints failed!')
            )

        # Additional info
        self.stdout.write(
            "\nNote: Endpoints returning 401/403 status codes are working correctly "
            "but require authentication. Use proper authentication headers in production."
        )
