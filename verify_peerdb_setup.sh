#!/bin/bash

# PeerDB Setup Verification Script
# This script verifies that PeerDB can connect to both PostgreSQL and ClickHouse

echo "🔍 PeerDB Setup Verification Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

echo ""
print_info "Step 1: Checking PeerDB Services"
echo "================================"

# Check if PeerDB containers are running
PEERDB_RUNNING=$(docker ps | grep peerdb-server | wc -l)
print_status $((1-$PEERDB_RUNNING)) "PeerDB Server Container"

PEERDB_UI_RUNNING=$(docker ps | grep peerdb-ui | wc -l)
print_status $((1-$PEERDB_UI_RUNNING)) "PeerDB UI Container"

CLICKHOUSE_RUNNING=$(docker ps | grep clickhouse-server | wc -l)
print_status $((1-$CLICKHOUSE_RUNNING)) "ClickHouse Container"

echo ""
print_info "Step 2: Testing Service Connectivity"
echo "===================================="

# Test PeerDB UI
curl -s http://localhost:3000 > /dev/null
print_status $? "PeerDB UI (http://localhost:3000)"

# Test ClickHouse HTTP
curl -s http://localhost:8123/ping > /dev/null
print_status $? "ClickHouse HTTP (localhost:8123)"

# Test PostgreSQL (requires psql)
if command -v psql &> /dev/null; then
    PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -d mes_db -c "SELECT 1;" > /dev/null 2>&1
    print_status $? "PostgreSQL (localhost:5432)"
else
    echo -e "${YELLOW}⚠️  psql not found - skipping PostgreSQL test${NC}"
fi

echo ""
print_info "Step 3: Database Content Verification"
echo "====================================="

# Count tables in PostgreSQL
if command -v psql &> /dev/null; then
    TABLE_COUNT=$(PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -d mes_db -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE 'mes_%';" 2>/dev/null | tr -d ' ')
    if [ ! -z "$TABLE_COUNT" ] && [ "$TABLE_COUNT" -gt 0 ]; then
        echo -e "${GREEN}✅ Found $TABLE_COUNT MES tables in PostgreSQL${NC}"
    else
        echo -e "${RED}❌ No MES tables found in PostgreSQL${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Cannot verify PostgreSQL tables - psql not available${NC}"
fi

# Test ClickHouse query
CLICKHOUSE_TEST=$(echo "SELECT 1" | curl -s --data-binary @- "http://localhost:8123/" -u default:password 2>/dev/null)
if [ "$CLICKHOUSE_TEST" = "1" ]; then
    echo -e "${GREEN}✅ ClickHouse query execution working${NC}"
else
    echo -e "${RED}❌ ClickHouse query execution failed${NC}"
fi

echo ""
print_info "Step 4: Network Connectivity for PeerDB"
echo "========================================"

# Test if PeerDB can reach PostgreSQL via host.docker.internal
docker exec peerdb-server ping -c 1 host.docker.internal > /dev/null 2>&1
print_status $? "PeerDB can reach host.docker.internal"

# Test if PeerDB can reach ClickHouse container
docker exec peerdb-server nc -z clickhouse-server 9000 > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ PeerDB can reach ClickHouse container${NC}"
else
    # Try via host.docker.internal
    docker exec peerdb-server nc -z host.docker.internal 9000 > /dev/null 2>&1
    print_status $? "PeerDB can reach ClickHouse via host.docker.internal"
fi

echo ""
print_info "Step 5: Configuration Summary"
echo "============================="

echo "📋 Connection Details for PeerDB Setup:"
echo ""
echo "PostgreSQL Peer Configuration:"
echo "  Name: postgres_mes_source"
echo "  Host: host.docker.internal"
echo "  Port: 5432"
echo "  Database: mes_db"
echo "  Username: postgres"
echo "  Password: postgres"
echo ""
echo "ClickHouse Peer Configuration:"
echo "  Name: clickhouse_mes_target"
echo "  Host: host.docker.internal"
echo "  Port: 9000"
echo "  Database: default"
echo "  Username: default"
echo "  Password: password"
echo ""

echo ""
print_info "Next Steps"
echo "=========="
echo "1. Open PeerDB Dashboard: http://localhost:3000"
echo "2. Create the peer connections using the details above"
echo "3. Set up mirrors for data synchronization"
echo "4. Refer to peerdb_setup_guide.md for detailed instructions"
echo ""

# Check if setup guide exists
if [ -f "peerdb_setup_guide.md" ]; then
    echo -e "${GREEN}✅ Setup guide available: peerdb_setup_guide.md${NC}"
else
    echo -e "${YELLOW}⚠️  Setup guide not found in current directory${NC}"
fi

# Check if SQL script exists
if [ -f "setup_peerdb_sync.sql" ]; then
    echo -e "${GREEN}✅ SQL setup script available: setup_peerdb_sync.sql${NC}"
else
    echo -e "${YELLOW}⚠️  SQL setup script not found in current directory${NC}"
fi

echo ""
echo "🎉 Verification complete! Check the status above and proceed with PeerDB configuration."
