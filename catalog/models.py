from django.db import models
from core.models import BaseEntity, BaseModel


class Commodity(BaseModel):
    """
    Commodity data model
    """
    name = models.CharField(max_length=255)
    description = models.TextField(null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_commodities'
        ordering = ['-id']

    def __str__(self):
        return self.name


class Component(BaseModel):
    """
    Model to store component information
    """
    code = models.Char<PERSON>ield(max_length=100, unique=True)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    specification = models.JSO<PERSON>ield(default=dict, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_components'
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class Product(BaseEntity):
    """
    Product data model - represents a product type/template
    """
    PRODUCT_TYPE_CHOICES = [
        ('part', 'Part Component'),
        ('consumable', 'Consumable'),
        ('solder_paste', 'Solder Paste'),
    ]
    commodity = models.ForeignKey(
        'Commodity',
        on_delete=models.PROTECT,
        related_name='products',
        null=True,
        blank=True
    )
    components = models.ManyToManyField(Component, through='ProductComponent')
    type_id = models.CharField(max_length=100, choices=PRODUCT_TYPE_CHOICES, default='part')

    # FIFO Configuration
    fifo_enabled = models.BooleanField(default=False, help_text="Enable FIFO tracking for this product")
    fifo_strict_enforcement = models.BooleanField(default=True, help_text="Strictly enforce FIFO rules")

    # FIFO time constraints are now defined in the routing schema's fifo_config

    # Custom FIFO configuration
    fifo_custom_config = models.JSONField(null=True, blank=True,
                                        help_text="Custom FIFO configuration parameters")

    class Meta:
        db_table = 'mes_products'
        ordering = ['-id']

    def __str__(self):
        return self.name


class ProductPart(BaseModel):
    """
    Model to store different part numbers for a product
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='parts')
    part_no = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=255, default='part name')
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_product_parts'
        ordering = ['part_no']

    def __str__(self):
        return f"{self.part_no} - {self.product.name}"


class ProductComponent(BaseModel):
    """
    Model to store product-component relationships
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='product_components')
    component = models.ForeignKey(Component, on_delete=models.CASCADE, related_name='products')
    quantity = models.IntegerField(default=1)
    position = models.CharField(max_length=100, blank=True)
    notes = models.TextField(blank=True)

    class Meta:
        db_table = 'mes_product_components'
        # unique_together = ('product', 'component', 'position')

    def __str__(self):
        return f"{self.product.code} - {self.component.name}"


class Scanner(BaseModel):
    """
    Model to store scanner information
    """
    code = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_scanners'
        ordering = ['-id']

    def __str__(self):
        return self.name
