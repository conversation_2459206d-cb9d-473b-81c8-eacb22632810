from django.contrib import admin
from django.core.exceptions import ValidationError
from django.forms import ModelForm
from .models import Product, Commodity, Component, ProductPart, ProductComponent, Scanner
from workflow_config.models import RoutingProduct


class ProductComponentInline(admin.TabularInline):
    model = ProductComponent
    extra = 1
    autocomplete_fields = ['component']


class ProductPartInline(admin.TabularInline):
    model = ProductPart
    extra = 1


class ProductRoutingInlineForm(ModelForm):
    """
    Custom form for ProductRoutingInline to provide better validation messages
    """
    class Meta:
        model = RoutingProduct
        fields = ['routing']

    def clean(self):
        cleaned_data = super().clean()

        # Get the routing from cleaned_data
        routing = cleaned_data.get('routing')
        if not routing:
            return cleaned_data

        # Get the product from the parent form
        if hasattr(self, 'instance') and hasattr(self.instance, 'product') and self.instance.product:
            product = self.instance.product
        else:
            # If we're adding to a new product that doesn't exist yet, we can't validate
            # This will be caught later when the instance is saved
            return cleaned_data

        # Check if product already has a different routing
        existing_routing = RoutingProduct.objects.filter(
            product=product
        ).exclude(routing=routing).first()

        if existing_routing:
            raise ValidationError({
                'routing': f"(catalog.admin)This product is already associated with routing '{existing_routing.routing.name}'. "
                          f"A product can only have one routing at this time."
            })

        return cleaned_data


class ProductRoutingInline(admin.TabularInline):
    """
    Inline admin for adding routings to a product
    """
    model = RoutingProduct
    # form = ProductRoutingInlineForm
    extra = 0
    autocomplete_fields = ['routing']
    verbose_name = "Routing"
    verbose_name_plural = "Routings"

    def get_max_num(self, request, obj=None, **kwargs):
        """
        Limit the number of routings that can be added to a product
        """
        if obj and obj.routings.exists():
            return obj.routings.count()  # If product already has routing(s), don't allow adding more
        return 1  # Otherwise allow adding one routing


@admin.register(Component)
class ComponentAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'description', 'is_active', 'specification', 'created_at')
    search_fields = ('name', 'code')
    list_filter = ('is_active', 'created_at')


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'get_routings_count', 'is_active', 'created_at', 'updated_at')
    search_fields = ('name', 'code')
    list_filter = ('is_active', 'created_at')
    inlines = [ProductComponentInline, ProductRoutingInline] # ProductPartInline

    def get_routings_count(self, obj):
        """Display the number of routings associated with this product"""
        return obj.routings.count()
    get_routings_count.short_description = 'Routings Count'


# @admin.register(ProductPart)
# class ProductPartAdmin(admin.ModelAdmin):
#     list_display = ('part_no', 'product', 'is_active', 'created_at')
#     search_fields = ('part_no', 'product__name')
#     list_filter = ('is_active', 'created_at')
#     autocomplete_fields = ['product']


@admin.register(Commodity)
class CommodityAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at', 'updated_at')
    search_fields = ('name',)
    list_filter = ('is_active', 'created_at')


@admin.register(Scanner)
class ScannerAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'is_active', 'created_at', 'updated_at')
    search_fields = ('name',)
    list_filter = ('is_active', 'created_at')
