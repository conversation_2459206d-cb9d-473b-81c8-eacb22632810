# Generated by Django 5.1 on 2025-02-02 10:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0002_scanner'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='productpart',
            name='components',
        ),
        migrations.RemoveField(
            model_name='productpart',
            name='metadata',
        ),
        migrations.AlterField(
            model_name='product',
            name='code',
            field=models.CharField(max_length=50, unique=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='name',
            field=models.CharField(max_length=100),
        ),
        migrations.CreateModel(
            name='ProductComponent',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.IntegerField(default=1)),
                ('position', models.CharField(blank=True, max_length=100)),
                ('notes', models.TextField(blank=True)),
                ('component', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='catalog.component')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_components', to='catalog.product')),
            ],
            options={
                'db_table': 'mes_product_components',
            },
        ),
        migrations.AddField(
            model_name='product',
            name='components',
            field=models.ManyToManyField(through='catalog.ProductComponent', to='catalog.component'),
        ),
        migrations.DeleteModel(
            name='ProductPartComponent',
        ),
    ]
