# Generated by Django 5.1 on 2024-11-30 19:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Commodity',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'mes_commodities',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Component',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=100, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('specification', models.JSONField(blank=True, default=dict)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'mes_components',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=100, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('commodity', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='products', to='catalog.commodity')),
            ],
            options={
                'db_table': 'mes_products',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='ProductPart',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('part_no', models.CharField(max_length=100, unique=True)),
                ('name', models.CharField(default='part name', max_length=255)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parts', to='catalog.product')),
            ],
            options={
                'db_table': 'mes_product_parts',
                'ordering': ['part_no'],
            },
        ),
        migrations.CreateModel(
            name='ProductPartComponent',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.IntegerField(default=1)),
                ('position', models.CharField(blank=True, max_length=100)),
                ('notes', models.TextField(blank=True)),
                ('component', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='part_components', to='catalog.component')),
                ('product_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='part_components', to='catalog.productpart')),
            ],
            options={
                'db_table': 'mes_product_part_components',
                'unique_together': {('product_part', 'component', 'position')},
            },
        ),
        migrations.AddField(
            model_name='productpart',
            name='components',
            field=models.ManyToManyField(through='catalog.ProductPartComponent', to='catalog.component'),
        ),
    ]
