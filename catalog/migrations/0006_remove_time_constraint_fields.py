# Generated manually

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0005_product_fifo_custom_config_product_fifo_enabled_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='stage1_stage2_min_time',
        ),
        migrations.RemoveField(
            model_name='product',
            name='stage1_stage2_max_time',
        ),
        migrations.RemoveField(
            model_name='product',
            name='stage2_stage3_min_time',
        ),
        migrations.RemoveField(
            model_name='product',
            name='stage2_stage3_max_time',
        ),
        migrations.RemoveField(
            model_name='product',
            name='stage3_stage4_min_time',
        ),
        migrations.RemoveField(
            model_name='product',
            name='stage3_stage4_max_time',
        ),
    ]
