# Generated by Django 5.1 on 2024-12-01 05:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Scanner',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=100, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'mes_scanners',
                'ordering': ['-id'],
            },
        ),
    ]
