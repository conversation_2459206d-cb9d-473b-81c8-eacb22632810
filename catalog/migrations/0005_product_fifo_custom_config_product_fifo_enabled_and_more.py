# Generated by Django 5.1 on 2025-04-06 16:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0004_product_type_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='fifo_custom_config',
            field=models.JSONField(blank=True, help_text='Custom FIFO configuration parameters', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='fifo_enabled',
            field=models.BooleanField(default=False, help_text='Enable FIFO tracking for this product'),
        ),
        migrations.AddField(
            model_name='product',
            name='fifo_strict_enforcement',
            field=models.BooleanField(default=True, help_text='Strictly enforce FIFO rules'),
        ),
        migrations.AddField(
            model_name='product',
            name='stage1_stage2_max_time',
            field=models.IntegerField(blank=True, help_text='Maximum time between stage 1 and 2 (minutes)', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='stage1_stage2_min_time',
            field=models.IntegerField(blank=True, help_text='Minimum time between stage 1 and 2 (minutes)', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='stage2_stage3_max_time',
            field=models.IntegerField(blank=True, help_text='Maximum time between stage 2 and 3 (minutes)', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='stage2_stage3_min_time',
            field=models.IntegerField(blank=True, help_text='Minimum time between stage 2 and 3 (minutes)', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='stage3_stage4_max_time',
            field=models.IntegerField(blank=True, help_text='Maximum time between stage 3 and 4 (minutes)', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='stage3_stage4_min_time',
            field=models.IntegerField(blank=True, help_text='Minimum time between stage 3 and 4 (minutes)', null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='type_id',
            field=models.CharField(choices=[('part', 'Part Component'), ('consumable', 'Consumable'), ('solder_paste', 'Solder Paste')], default='part', max_length=100),
        ),
    ]
