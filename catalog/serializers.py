from rest_framework import serializers
from django.core.exceptions import ValidationError
from operation.models import ManufacturingEvent
from workflow_config.models import Routing, RoutingProduct
from .models import Product, ProductPart, Component, ProductComponent
from bom.services import BOMService


class ComponentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Component
        fields = ['id', 'code', 'name', 'description']


class ProductComponentSerializer(serializers.ModelSerializer):
    component = ComponentSerializer()

    class Meta:
        model = ProductComponent
        fields = ['component']


class ProductDetailSerializer(serializers.ModelSerializer):
    components = ProductComponentSerializer(source='product_components', many=True, read_only=True)
    past_events = serializers.SerializerMethodField()
    last_failed_event = serializers.SerializerMethodField()
    bom_info = serializers.SerializerMethodField()

    # Add these fields for routing management
    routing_id = serializers.PrimaryKeyRelatedField(
        source='routings.first',  # Since a product can only have one routing
        queryset=Routing.objects.all(),
        required=False,
        allow_null=True,
        write_only=True
    )
    routing = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'code', 'name', 'description', 'is_active',
            'components', 'past_events', 'last_failed_event',
            'routing_id', 'routing',  # Add routing fields
            'fifo_enabled', 'fifo_strict_enforcement', 'fifo_custom_config',
            'bom_info'  # Add BOM information field
        ]

    def get_past_events(self, obj):
        from operation.serializers.event_serializers import ManufacturingEventSummarySerializer # to Break the circular dependency
        serial_number = self.context.get('serial_number')
        if not serial_number:
            return []

        events = obj.event_data.filter(
            serial_number=serial_number
        ).order_by('-timestamp')[:10]

        return ManufacturingEventSummarySerializer(events, many=True).data

    def get_last_failed_event(self, obj):
        from operation.serializers.event_serializers import ManufacturingEventSummarySerializer # to Break the circular dependency
        serial_number = self.context.get('serial_number')
        if not serial_number:
            return None

        event = obj.event_data.filter(
            serial_number=serial_number,
            inspection_status=False
        ).order_by('-timestamp').first()

        if not event:
            return None

        return ManufacturingEventSummarySerializer(event).data

    def get_routing(self, obj):
        """Get the routing details for this product"""
        from workflow_config.serializers.routing_serializers import RoutingSummarySerializer
        routing = obj.routings.first()  # Get the first (and only) routing
        if routing:
            return RoutingSummarySerializer(routing).data
        return None

    def get_bom_info(self, obj):
        """
        Get BOM information for this product if requested.

        This method checks if 'include_bom' is in the context and returns
        BOM information accordingly.
        """
        include_bom = self.context.get('include_bom', False)
        if not include_bom:
            return None

        # Get BOM status filter from context
        bom_status = self.context.get('bom_status', 'active')

        # Get BOM information
        return BOMService.get_product_bom_summary(obj.id, bom_status)

    def create(self, validated_data):
        """Create a product, including its routing association"""
        # Extract routing from validated data
        routing = None
        if 'routings' in validated_data:
            # This will be the first routing from the source='routings.first'
            routing = validated_data.pop('routings', {}).get('first')

        # Create the product
        product = Product.objects.create(**validated_data)

        # Handle routing association if provided
        if routing is not None:
            try:
                RoutingProduct.objects.create(product=product, routing=routing)
            except ValidationError as e:
                # If routing association fails, delete the created product and raise error
                product.delete()
                raise serializers.ValidationError({"routing_id": str(e)})

        return product

    def update(self, instance, validated_data):
        """Update a product, including its routing association"""
        # Extract routing from validated data
        routing = None
        if 'routings' in validated_data:
            # This will be the first routing from the source='routings.first'
            routing = validated_data.pop('routings', {}).get('first')

        # Update other product fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Handle routing association
        if routing is not None:
            # Check if product already has a routing
            current_routing = instance.routings.first()

            if current_routing and current_routing.id != routing.id:
                # Remove the current routing association
                RoutingProduct.objects.filter(product=instance).delete()

            if not current_routing or current_routing.id != routing.id:
                # Create new routing association
                try:
                    RoutingProduct.objects.create(product=instance, routing=routing)
                except ValidationError as e:
                    raise serializers.ValidationError({"routing_id": str(e)})
        elif routing is None and 'routings' in validated_data:
            # If routing is explicitly set to None, remove any existing routing
            RoutingProduct.objects.filter(product=instance).delete()

        return instance


class ProductSummarySerializer(serializers.ModelSerializer):
    """Simplified serializer for product references"""
    routing_id = serializers.SerializerMethodField()
    routing_name = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = ['id', 'name', 'code', 'description', 'routing_id', 'routing_name']
        read_only_fields = ['id', 'name', 'code', 'description', 'routing_id', 'routing_name']

    def get_routing_id(self, obj):
        """Get the routing ID for this product"""
        routing = obj.routings.first()  # Get the first (and only) routing
        return routing.id if routing else None

    def get_routing_name(self, obj):
        """Get the routing name for this product"""
        routing = obj.routings.first()  # Get the first (and only) routing
        return routing.name if routing else None
