# Catalog Module for MES Project

This module provides product and component catalog functionality for the MES project, allowing users to manage products, components, and their relationships.

## Features

- Product management (create, update, delete, list)
- Component management (create, update, delete, list)
- Product-component relationships
- Integration with routing and workflow configuration
- Serial number-based product lookup
- Bill of Materials (BOM) integration

## API Endpoints

### Products

- `GET /mes_trace/catalog/api/parts/` - List all products
- `POST /mes_trace/catalog/api/parts/` - Create a new product
- `GET /mes_trace/catalog/api/parts/{id}/` - Retrieve a specific product
- `PUT /mes_trace/catalog/api/parts/{id}/` - Update a specific product
- `DELETE /mes_trace/catalog/api/parts/{id}/` - Delete a specific product

#### Product Actions

- `GET /mes_trace/catalog/api/parts/by_serial_number/?serial_number={serial}` - Retrieve product details by serial number
- `GET /mes_trace/catalog/api/parts/last_failed_event/?serial_number={serial}` - Get the most recent failed event for a given serial number
- `DELETE /mes_trace/catalog/api/parts/{id}/routing/` - Remove the routing association for a product

### Product Details with BOM Information

The product detail endpoints now support retrieving BOM information along with product details:

#### Retrieve Product with BOM Information

```
GET /mes_trace/catalog/api/parts/{id}/?include_bom=true
```

**Query Parameters:**
- `include_bom` - Boolean flag to include BOM information (default: false)
- `bom_status` - Filter BOMs by status (default: 'active', options: 'active', 'draft', 'obsolete')

**Example:**
```
GET /mes_trace/catalog/api/parts/123/?include_bom=true
GET /mes_trace/catalog/api/parts/123/?include_bom=true&bom_status=draft
```

**Response:**
```json
{
  "id": 123,
  "code": "PROD-123",
  "name": "Product Name",
  "description": "Product Description",
  "is_active": true,
  "components": [...],
  "routing": {...},
  "bom_info": {
    "has_bom": true,
    "bom_count": 1,
    "active_bom": {
      "id": 1,
      "name": "BOM Name",
      "code": "BOM-001",
      "version": "1.0",
      "status": "active",
      "effective_date": "2025-05-01",
      "top_level_items_count": 3,
      "total_items_count": 10
    },
    "boms": [...]
  }
}
```

#### Retrieve Product by Serial Number with BOM Information

```
GET /mes_trace/catalog/api/parts/by_serial_number/?serial_number={serial}&include_bom=true
```

**Query Parameters:**
- `serial_number` - Serial number of the product (required)
- `include_bom` - Boolean flag to include BOM information (default: false)
- `bom_status` - Filter BOMs by status (default: 'active', options: 'active', 'draft', 'obsolete')

**Example:**
```
GET /mes_trace/catalog/api/parts/by_serial_number/?serial_number=HE317171-35.12#D92410529460638&include_bom=true
```

**Response:**
Same structure as the product detail endpoint, with additional past events information based on the serial number.

## Models

### Product

Represents a product type/template.

**Fields:**
- `id` - Primary key
- `code` - Unique code for the product
- `name` - Name of the product
- `description` - Description of the product
- `is_active` - Whether the product is active
- `commodity` - Foreign key to the Commodity model
- `components` - Many-to-many relationship with Component model through ProductComponent
- `type_id` - Type of the product (part, consumable, solder_paste)
- `fifo_enabled` - Whether FIFO tracking is enabled for this product
- `fifo_strict_enforcement` - Whether FIFO rules are strictly enforced
- `fifo_custom_config` - Custom FIFO configuration parameters

### Component

Represents a component that can be used in products.

**Fields:**
- `id` - Primary key
- `code` - Unique code for the component
- `name` - Name of the component
- `description` - Description of the component
- `specification` - JSON field for component specifications
- `is_active` - Whether the component is active

### ProductComponent

Represents a relationship between a product and a component.

**Fields:**
- `id` - Primary key
- `product` - Foreign key to the Product model
- `component` - Foreign key to the Component model
- `quantity` - Quantity of the component in the product
- `position` - Position or location identifier
- `notes` - Additional notes

## Integration with BOM Module

The Catalog module now integrates with the BOM module to provide BOM information for products. This integration allows users to:

1. Retrieve product details with complete BOM information
2. Filter BOMs by status (active, draft, obsolete)
3. Get summary information about BOMs associated with a product

For more detailed BOM information, use the dedicated BOM API endpoints in the BOM module.

## Usage Examples

### Retrieving a Product with BOM Information

```python
import requests

# Get product details with BOM information
response = requests.get(
    'http://localhost:8000/mes_trace/catalog/api/parts/123/?include_bom=true',
    headers={'Authorization': 'Bearer your_token_here'}
)

# Get product details by serial number with BOM information
response = requests.get(
    'http://localhost:8000/mes_trace/catalog/api/parts/by_serial_number/?serial_number=HE317171-35.12#D92410529460638&include_bom=true',
    headers={'Authorization': 'Bearer your_token_here'}
)
```
