"""
Tests for the Product API endpoints.

This module contains tests for the Product API endpoints, including the enhanced
functionality to include BOM information in product details.
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from catalog.models import Product
from bom.models import B<PERSON><PERSON>eader, BOMItem

User = get_user_model()


class ProductAPITests(APITestCase):
    """
    Tests for the Product API endpoints.
    """
    
    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create test products
        self.product = Product.objects.create(
            name='Test Product',
            code='TEST-PROD',
            description='Test product description'
        )
        
        self.component1 = Product.objects.create(
            name='Component 1',
            code='COMP-1',
            description='Test component 1'
        )
        
        self.component2 = Product.objects.create(
            name='Component 2',
            code='COMP-2',
            description='Test component 2'
        )
        
        # Create a BOM header
        self.bom_header = BOMHeader.objects.create(
            name='Test BOM',
            code='TEST-BOM',
            product=self.product,
            version='1.0',
            status='active',
            created_by=self.user
        )
        
        # Create BOM items
        self.item1 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component1,
            quantity=2,
            position='POS1',
            item_type='assembly'
        )
        
        self.item2 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component2,
            quantity=1,
            position='POS2',
            item_type='component'
        )
        
        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_product_detail_with_bom(self):
        """Test retrieving product details with BOM information"""
        # Test without BOM information
        url = reverse('product-detail', args=[self.product.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNone(response.data.get('bom_info'))
        
        # Test with BOM information
        response = self.client.get(f"{url}?include_bom=true")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.data.get('bom_info'))
        self.assertTrue(response.data['bom_info']['has_bom'])
        self.assertEqual(response.data['bom_info']['bom_count'], 1)
        self.assertIsNotNone(response.data['bom_info']['active_bom'])
        self.assertEqual(response.data['bom_info']['active_bom']['id'], self.bom_header.id)
        
        # Test with BOM status filter
        response = self.client.get(f"{url}?include_bom=true&bom_status=draft")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.data.get('bom_info'))
        self.assertFalse(response.data['bom_info']['has_bom'])  # No draft BOMs
        self.assertEqual(response.data['bom_info']['bom_count'], 0)
    
    def test_product_by_serial_number_with_bom(self):
        """Test retrieving product by serial number with BOM information"""
        # Mock a serial number that would resolve to our product
        serial_number = f"TEST-{self.product.code}-12345"
        
        # Patch the parse_serial_number function to return our product code
        from unittest.mock import patch
        with patch('workflow_config.services.form_service.parse_serial_number') as mock_parse:
            mock_parse.return_value = {'part_code': self.product.code}
            
            # Test without BOM information
            url = reverse('product-by-serial-number')
            response = self.client.get(f"{url}?serial_number={serial_number}")
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIsNone(response.data.get('bom_info'))
            
            # Test with BOM information
            response = self.client.get(f"{url}?serial_number={serial_number}&include_bom=true")
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIsNotNone(response.data.get('bom_info'))
            self.assertTrue(response.data['bom_info']['has_bom'])
            self.assertEqual(response.data['bom_info']['bom_count'], 1)
            
            # Test with BOM status filter
            response = self.client.get(f"{url}?serial_number={serial_number}&include_bom=true&bom_status=draft")
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIsNotNone(response.data.get('bom_info'))
            self.assertFalse(response.data['bom_info']['has_bom'])  # No draft BOMs
            self.assertEqual(response.data['bom_info']['bom_count'], 0)
