[{"model": "catalog.component", "fields": {"code": "IC-MCU-328P", "name": "ATmega328P Microcontroller", "description": "8-bit AVR RISC-based microcontroller with 32KB flash memory", "specification": {"package": "TQFP-32", "memory": "32KB Flash", "speed": "20MHz", "voltage": "1.8-5.5V"}, "is_active": true, "created_at": "2024-01-10T10:00:00Z", "updated_at": "2024-01-10T10:00:00Z"}}, {"model": "catalog.component", "fields": {"code": "CAP-CER-104", "name": "Ceramic Capacitor 0.1µF", "description": "Surface mount ceramic capacitor for power decoupling", "specification": {"value": "0.1µF", "voltage": "50V", "tolerance": "±10%", "package": "0805"}, "is_active": true, "created_at": "2024-01-10T10:01:00Z", "updated_at": "2024-01-10T10:01:00Z"}}, {"model": "catalog.component", "fields": {"code": "RES-0805-10K", "name": "Resistor 10kΩ", "description": "Surface mount chip resistor", "specification": {"value": "10kΩ", "power": "0.125W", "tolerance": "±1%", "package": "0805"}, "is_active": true, "created_at": "2024-01-10T10:02:00Z", "updated_at": "2024-01-10T10:02:00Z"}}, {"model": "catalog.component", "fields": {"code": "XTAL-16MHZ", "name": "Crystal Oscillator 16MHz", "description": "16MHz crystal oscillator for MCU clock", "specification": {"frequency": "16MHz", "stability": "±20ppm", "load_capacitance": "12pF", "package": "HC-49S"}, "is_active": true, "created_at": "2024-01-10T10:03:00Z", "updated_at": "2024-01-10T10:03:00Z"}}, {"model": "catalog.component", "fields": {"code": "LED-0805-GRN", "name": "Green LED", "description": "Surface mount green LED indicator", "specification": {"color": "Green", "forward_voltage": "2.0V", "current": "20mA", "package": "0805"}, "is_active": true, "created_at": "2024-01-10T10:04:00Z", "updated_at": "2024-01-10T10:04:00Z"}}, {"model": "catalog.component", "fields": {"code": "REG-LDO-3.3V", "name": "3.3V Voltage Regulator", "description": "Low dropout linear voltage regulator", "specification": {"output_voltage": "3.3V", "max_current": "800mA", "dropout_voltage": "340mV", "package": "SOT-223"}, "is_active": true, "created_at": "2024-01-10T10:05:00Z", "updated_at": "2024-01-10T10:05:00Z"}}]