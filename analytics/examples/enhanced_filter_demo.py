#!/usr/bin/env python3
"""
Demonstration script for enhanced filter configuration functionality
"""
import os
import sys
import django
from django.http import QueryDict

# Add the project root to the Python path
sys.path.append('/Users/<USER>/mes_bk')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mes_traceability.settings')
django.setup()

from analytics.services import FilterService


def demo_filter_configurations():
    """Demonstrate different filter configuration formats and their processing"""
    
    print("=" * 80)
    print("ENHANCED FILTER CONFIGURATION DEMONSTRATION")
    print("=" * 80)
    
    # Demo 1: Legacy Format
    print("\n1. LEGACY FORMAT DEMONSTRATION")
    print("-" * 40)
    
    legacy_config = {
        'user_id': 'int',
        'name': 'str',
        'is_active': 'bool'
    }
    
    query_params = QueryDict('user_id=123&name=John&is_active=1')
    
    print(f"Filter Config: {legacy_config}")
    print(f"Query Params: {dict(query_params)}")
    
    conditions = FilterService.build_filter_conditions(legacy_config, query_params)
    print(f"Generated Conditions: {conditions}")
    
    base_query = "SELECT * FROM users"
    modified_query = FilterService.apply_filters_to_query(base_query, conditions)
    print(f"Modified Query: {modified_query}")
    
    # Demo 2: Enhanced Format
    print("\n\n2. ENHANCED FORMAT DEMONSTRATION")
    print("-" * 40)
    
    enhanced_config = {
        'department': {
            'type': 'str',
            'clause': 'where'
        },
        'total_sales': {
            'type': 'int',
            'clause': 'having'
        },
        'avg_rating': {
            'type': 'int',
            'clause': 'having'
        }
    }
    
    query_params = QueryDict('department=Engineering&total_sales=50000&avg_rating=4')
    
    print(f"Filter Config: {enhanced_config}")
    print(f"Query Params: {dict(query_params)}")
    
    conditions = FilterService.build_filter_conditions(enhanced_config, query_params)
    print(f"Generated Conditions: {conditions}")
    
    base_query = "SELECT department, SUM(sales) as total_sales, AVG(rating) as avg_rating FROM employees GROUP BY department"
    modified_query = FilterService.apply_filters_to_query(base_query, conditions)
    print(f"Modified Query: {modified_query}")
    
    # Demo 3: Mixed Format
    print("\n\n3. MIXED FORMAT DEMONSTRATION")
    print("-" * 40)
    
    mixed_config = {
        'region': 'str',  # Legacy format
        'product_type': {  # Enhanced format
            'type': 'str',
            'clause': 'where'
        },
        'revenue': {  # Enhanced format
            'type': 'int',
            'clause': 'having'
        }
    }
    
    query_params = QueryDict('region=North&product_type=Electronics&revenue=100000')
    
    print(f"Filter Config: {mixed_config}")
    print(f"Query Params: {dict(query_params)}")
    
    conditions = FilterService.build_filter_conditions(mixed_config, query_params)
    print(f"Generated Conditions: {conditions}")
    
    base_query = "SELECT region, product_type, SUM(revenue) as revenue FROM sales GROUP BY region, product_type"
    modified_query = FilterService.apply_filters_to_query(base_query, conditions)
    print(f"Modified Query: {modified_query}")
    
    # Demo 4: Complex Query with Existing WHERE and HAVING
    print("\n\n4. COMPLEX QUERY DEMONSTRATION")
    print("-" * 40)
    
    complex_config = {
        'status': {
            'type': 'str',
            'clause': 'where'
        },
        'count': {
            'type': 'int',
            'clause': 'having'
        }
    }
    
    query_params = QueryDict('status=active&count=10')
    
    print(f"Filter Config: {complex_config}")
    print(f"Query Params: {dict(query_params)}")
    
    conditions = FilterService.build_filter_conditions(complex_config, query_params)
    print(f"Generated Conditions: {conditions}")
    
    # Query with existing WHERE and HAVING clauses
    base_query = """
    SELECT part_name, COUNT(*) as count 
    FROM defects 
    WHERE created_at > '2023-01-01' 
    GROUP BY part_name 
    HAVING count > 5 
    ORDER BY count DESC
    """.strip()
    
    print(f"Original Query: {base_query}")
    modified_query = FilterService.apply_filters_to_query(base_query, conditions)
    print(f"Modified Query: {modified_query}")
    
    # Demo 5: Normalization
    print("\n\n5. CONFIGURATION NORMALIZATION DEMONSTRATION")
    print("-" * 40)
    
    configs_to_normalize = [
        {'col1': 'str', 'col2': 'int'},  # Legacy
        {'col1': {'type': 'str', 'clause': 'where'}},  # Enhanced
        {'col1': 'str', 'col2': {'type': 'int', 'clause': 'having'}},  # Mixed
    ]
    
    for i, config in enumerate(configs_to_normalize, 1):
        print(f"\nConfig {i}: {config}")
        normalized = FilterService._normalize_filter_config(config)
        print(f"Normalized: {normalized}")


def demo_filter_types():
    """Demonstrate different filter data types"""
    
    print("\n\n" + "=" * 80)
    print("FILTER DATA TYPES DEMONSTRATION")
    print("=" * 80)
    
    # Integer filters
    print("\n1. INTEGER FILTERS")
    print("-" * 20)
    
    # Exact match
    query_params = QueryDict('age=25')
    condition = FilterService._build_single_condition('age', 'int', '25', query_params)
    print(f"Exact match: age=25 -> {condition}")
    
    # Range match
    query_params = QueryDict('age=20&age=30')
    condition = FilterService._build_single_condition('age', 'int', '20', query_params)
    print(f"Range match: age=20&age=30 -> {condition}")
    
    # String filters
    print("\n2. STRING FILTERS")
    print("-" * 20)
    
    # Exact match
    query_params = QueryDict('name=John')
    condition = FilterService._build_single_condition('name', 'str', 'John', query_params)
    print(f"Exact match: name=John -> {condition}")
    
    # Pattern match
    query_params = QueryDict('name=%John%')
    condition = FilterService._build_single_condition('name', 'str', '%John%', query_params)
    print(f"Pattern match: name=%John% -> {condition}")
    
    # Boolean filters
    print("\n3. BOOLEAN FILTERS")
    print("-" * 20)
    
    query_params = QueryDict('is_active=1')
    condition = FilterService._build_single_condition('is_active', 'bool', '1', query_params)
    print(f"Boolean true: is_active=1 -> {condition}")
    
    query_params = QueryDict('is_active=0')
    condition = FilterService._build_single_condition('is_active', 'bool', '0', query_params)
    print(f"Boolean false: is_active=0 -> {condition}")
    
    # Date filters
    print("\n4. DATE FILTERS")
    print("-" * 20)
    
    # Exact match
    query_params = QueryDict('created_at=2023-01-01')
    condition = FilterService._build_single_condition('created_at', 'date', '2023-01-01', query_params)
    print(f"Exact match: created_at=2023-01-01 -> {condition}")
    
    # Range match
    query_params = QueryDict('created_at=2023-01-01&created_at=2023-12-31')
    condition = FilterService._build_single_condition('created_at', 'date', '2023-01-01', query_params)
    print(f"Range match: created_at=2023-01-01&created_at=2023-12-31 -> {condition}")


if __name__ == "__main__":
    try:
        demo_filter_configurations()
        demo_filter_types()
        print("\n\n" + "=" * 80)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("=" * 80)
    except Exception as e:
        print(f"Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
