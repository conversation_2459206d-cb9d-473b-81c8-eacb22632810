# Filter Schema Documentation

## Overview

This document describes the filter schema API for the MES Analytics module. The filter schema API provides documentation about available filter types and their usage patterns, helping developers and users understand how to apply filters to chart queries.

## Filter Types

The following filter types are supported:

### Integer Filter (`int`)

- **Description**: Integer filter that supports exact match or range filtering
- **Usage**:
  - Exact match: `[123]` (single value in an array)
  - Range: `[100, 200]` (two values for min and max)
- **Example**: `{"user_id": [1]}` or `{"age": [18, 65]}`

### String Filter (`str`)

- **Description**: String filter that supports exact match or pattern matching with % wildcard
- **Usage**:
  - Exact match: `"example_value"`
  - Pattern match: `"%partial%"` (using % as wildcard)
- **Example**: `{"name": "John"}` or `{"name": "%Smith%"}`

### Date Filter (`date`)

- **Description**: Date filter that supports exact match or range filtering
- **Usage**:
  - Exact match: `["2023-01-01"]` (single date in an array)
  - Range: `["2023-01-01", "2023-12-31"]` (date range with start and end)
- **Example**: `{"created_at": ["2023-01-01"]}` or `{"created_at": ["2023-01-01", "2023-12-31"]}`

### Boolean Filter (`bool`)

- **Description**: Boolean filter that accepts 0 (false) or 1 (true)
- **Usage**: `"0"` or `"1"` (as strings)
- **Example**: `{"is_active": "1"}`

## API Endpoints

### Get Filter Schema Documentation

```
GET /mes_trace/analytics/api/charts/filter_schema/
```

Returns documentation about all filter types and their usage patterns.

#### Query Parameters

- `chart_id` (optional): If provided, also returns the specific filter configuration for that chart

#### Response Example

```json
{
  "filter_types": {
    "int": {
      "type": "int",
      "description": "Integer filter that supports exact match or range filtering",
      "example": {
        "exact_match": [123],
        "range": [100, 200]
      }
    },
    "str": {
      "type": "str",
      "description": "String filter that supports exact match or pattern matching with % wildcard",
      "example": {
        "exact_match": "example_value",
        "pattern_match": "%partial%"
      }
    },
    "date": {
      "type": "date",
      "description": "Date filter that supports exact match or range filtering",
      "example": {
        "exact_match": ["2023-01-01"],
        "range": ["2023-01-01", "2023-12-31"]
      }
    },
    "bool": {
      "type": "bool",
      "description": "Boolean filter that accepts 0 (false) or 1 (true)",
      "example": {
        "true_value": "1",
        "false_value": "0"
      }
    }
  }
}
```

### Get Filter Schema for Specific Chart

```
GET /mes_trace/analytics/api/charts/{chart_id}/filter_schema_for_chart/
```

Returns filter schema documentation and the specific filter configuration for the specified chart.

#### Response Example

```json
{
  "filter_types": {
    "int": { ... },
    "str": { ... },
    "date": { ... },
    "bool": { ... }
  },
  "chart_filters": {
    "user_id": "int",
    "created_at": "date",
    "is_active": "bool"
  }
}
```

## Swagger/OpenAPI Documentation

The API is documented using Swagger/OpenAPI. You can access the documentation at:

- Swagger UI: `/mes_trace/swagger/`
- ReDoc: `/mes_trace/redoc/`
- OpenAPI JSON: `/mes_trace/swagger.json`
- OpenAPI YAML: `/mes_trace/swagger.yaml`

## Applying Filters to Chart Queries

To apply filters to a chart query, include the filter parameters in the request query string. The filter parameters should match the keys defined in the chart's `filter_config`.

### Example

```
GET /mes_trace/analytics/api/charts/{chart_id}/data/?user_id=[1,100]&created_at=["2023-01-01","2023-12-31"]&is_active=1
```

This will apply the following filters to the chart query:
- `user_id`: Integer range filter from 1 to 100
- `created_at`: Date range filter from January 1, 2023 to December 31, 2023
- `is_active`: Boolean filter set to true (1)