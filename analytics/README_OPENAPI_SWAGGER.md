# OpenAPI/Swagger Documentation

## Overview

This document describes the OpenAPI/Swagger integration for the MES Analytics module. The OpenAPI/Swagger integration provides interactive API documentation, making it easier for developers to understand and use the API endpoints.

## Accessing the Documentation

The API documentation is available at the following URLs:

- **Swagger UI**: `/mes_trace/swagger/` - Interactive documentation with a user-friendly interface
- **ReDoc**: `/mes_trace/redoc/` - Alternative documentation viewer with a different layout
- **OpenAPI JSON**: `/mes_trace/swagger.json` - Raw OpenAPI specification in JSON format
- **OpenAPI YAML**: `/mes_trace/swagger.yaml` - Raw OpenAPI specification in YAML format

## Authentication

The Swagger UI supports authentication. To use authenticated endpoints:

1. Click the "Authorize" button in the Swagger UI
2. Enter your JWT token in the format: `Bearer <your-token>`
3. Click "Authorize" to apply the token to all API requests

## Filter Schema API Endpoints

The Filter Schema API provides documentation about available filter types and their usage patterns. These endpoints are documented in the Swagger UI:

### Get Filter Schema Documentation

```
GET /mes_trace/analytics/api/charts/filter_schema/
```

Returns documentation about all filter types and their usage patterns.

#### Query Parameters

- `chart_id` (optional): If provided, also returns the specific filter configuration for that chart

#### Response Example

```json
{
  "filter_types": {
    "int": {
      "type": "int",
      "description": "Integer filter that supports exact match or range filtering",
      "example": {
        "exact_match": [123],
        "range": [100, 200]
      }
    },
    "str": {
      "type": "str",
      "description": "String filter that supports exact match or pattern matching with % wildcard",
      "example": {
        "exact_match": "example_value",
        "pattern_match": "%partial%"
      }
    },
    "date": {
      "type": "date",
      "description": "Date filter that supports exact match or range filtering",
      "example": {
        "exact_match": ["2023-01-01"],
        "range": ["2023-01-01", "2023-12-31"]
      }
    },
    "bool": {
      "type": "bool",
      "description": "Boolean filter that accepts 0 (false) or 1 (true)",
      "example": {
        "true_value": "1",
        "false_value": "0"
      }
    }
  }
}
```

### Get Filter Schema for Specific Chart

```
GET /mes_trace/analytics/api/charts/{id}/filter_schema_for_chart/
```

Returns filter schema documentation and the specific filter configuration for the specified chart.

#### Response Example

```json
{
  "filter_types": {
    "int": { ... },
    "str": { ... },
    "date": { ... },
    "bool": { ... }
  },
  "chart_filters": {
    "user_id": "int",
    "created_at": "date",
    "is_active": "bool"
  }
}
```

## Configuration

The OpenAPI/Swagger integration is configured in the following files:

- `mes_traceability/settings.py` - Includes the `drf_yasg` app in `INSTALLED_APPS`
- `mes_traceability/urls.py` - Defines the Swagger UI endpoints
- `requirements.txt` - Includes the `drf-yasg` package dependency

## Customizing the Documentation

To customize the OpenAPI documentation:

1. Modify the `schema_view` configuration in `mes_traceability/urls.py`
2. Add or update docstrings in your API views
3. Use `@swagger_auto_schema` decorator to provide additional documentation for specific endpoints

Example:

```python
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class MyViewSet(viewsets.ModelViewSet):
    @swagger_auto_schema(
        operation_description="My custom description",
        manual_parameters=[
            openapi.Parameter(
                'param_name',
                openapi.IN_QUERY,
                description="Parameter description",
                type=openapi.TYPE_STRING
            )
        ],
        responses={
            200: "Success response description",
            400: "Error response description"
        }
    )
    def my_action(self, request):
        # Implementation
        pass
```

## Applying Filters to Chart Queries

To apply filters to a chart query, include the filter parameters in the request query string. The filter parameters should match the keys defined in the chart's `filter_config`.

### Example

```
GET /mes_trace/analytics/api/charts/{id}/data/?user_id=[1,100]&created_at=["2023-01-01","2023-12-31"]&is_active=1
```

This will apply the following filters to the chart query:
- `user_id`: Integer range filter from 1 to 100
- `created_at`: Date range filter from January 1, 2023 to December 31, 2023
- `is_active`: Boolean filter set to true (1)

### Filter Type Usage

#### Integer Filter (`int`)
- **Exact match**: `[123]` (single value in an array)
- **Range**: `[100, 200]` (two values for min and max)
- **Example**: `user_id=[1]` or `age=[18,65]`

#### String Filter (`str`)
- **Exact match**: `"example_value"`
- **Pattern match**: `"%partial%"` (using % as wildcard)
- **Example**: `name=John` or `name=%Smith%`

#### Date Filter (`date`)
- **Exact match**: `["2023-01-01"]` (single date in an array)
- **Range**: `["2023-01-01", "2023-12-31"]` (date range with start and end)
- **Example**: `created_at=["2023-01-01"]` or `created_at=["2023-01-01","2023-12-31"]`

#### Boolean Filter (`bool`)
- **Usage**: `0` (false) or `1` (true)
- **Example**: `is_active=1`

## Related Documentation

- [Filter Schema Documentation](README_FILTER_SCHEMA.md) - Documentation for the filter schema API
- [drf-yasg Documentation](https://drf-yasg.readthedocs.io/) - Official documentation for the drf-yasg package