from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import AnalyticsViewSet, DashboardViewSet, ChartViewSet, ChartGroupViewSet

# Create a router and register our viewsets
router = DefaultRouter()
router.register(r'reports', AnalyticsViewSet, basename='analytics')
router.register(r'dashboards', DashboardViewSet, basename='dashboards')
router.register(r'chart-groups', ChartGroupViewSet, basename='chart-groups')
router.register(r'charts', ChartViewSet, basename='charts')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
]
