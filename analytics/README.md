# Analytics Module for MES Project

This module provides analytics capabilities for the MES project, allowing users to create custom dashboards and charts that visualize data from ClickHouse database.

## Features

- Dashboard management (create, update, delete, list)
- Chart management (create, update, delete, list)
- Integration with ClickHouse database for data querying
- ECharts-based data visualization
- Permission-based access control
- Public and private dashboards

## Setup

1. Ensure the required dependencies are installed:

```bash
pip install -r requirements.txt
```

2. Configure ClickHouse connection in your environment variables or .env file:

```
CLICKHOUSE_HOST=your_clickhouse_host
CLICKHOUSE_PORT=9000
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=your_password
CLICKHOUSE_DB=your_database
```

3. Run migrations to create the necessary database tables:

```bash
python manage.py migrate analytics
```

## API Endpoints

### Dashboards

- `GET /mes_trace/analytics/api/dashboards/` - List all dashboards (owned by user or public)
- `POST /mes_trace/analytics/api/dashboards/` - Create a new dashboard
- `GET /mes_trace/analytics/api/dashboards/{id}/` - Get dashboard details
- `PUT /mes_trace/analytics/api/dashboards/{id}/` - Update dashboard
- `DELETE /mes_trace/analytics/api/dashboards/{id}/` - Delete dashboard

### Charts

- `GET /mes_trace/analytics/api/charts/` - List all charts (from dashboards owned by user or public)
- `POST /mes_trace/analytics/api/charts/` - Create a new chart
- `GET /mes_trace/analytics/api/charts/{id}/` - Get chart details
- `PUT /mes_trace/analytics/api/charts/{id}/` - Update chart
- `DELETE /mes_trace/analytics/api/charts/{id}/` - Delete chart
- `GET /mes_trace/analytics/api/charts/{id}/data/` - Get chart data

### Legacy Analytics

- `GET /mes_trace/analytics/api/reports/embed_url/` - Get embedded URL for legacy analytics dashboard

## Usage Examples

### Creating a Dashboard

```json
POST /mes_trace/analytics/api/dashboards/
{
  "name": "Production Overview",
  "code": "prod-overview",
  "description": "Dashboard showing production metrics",
  "layout": {
    "cols": 12,
    "rowHeight": 30,
    "margin": [10, 10],
    "containerPadding": [10, 10],
    "verticalCompact": true
  },
  "is_public": false
}
```

### Creating a Chart

```json
POST /mes_trace/analytics/api/charts/
{
  "name": "Daily Production",
  "code": "daily-prod",
  "description": "Daily production count",
  "dashboard": 1,
  "chart_type": "bar",
  "query": "SELECT date, count(*) as count FROM production_events GROUP BY date ORDER BY date",
  "config": {
    "title": {
      "text": "Daily Production"
    },
    "tooltip": {
      "trigger": "axis"
    }
  },
  "refresh_interval": 300,
  "position": {
    "x": 0,
    "y": 0,
    "w": 6,
    "h": 4
  }
}
```

## Permissions

The analytics module uses the existing MES permission system. To access analytics features, users need the following permissions:

- `analytics.get` - View dashboards and charts
- `analytics.create` - Create dashboards and charts
- `analytics.update` - Update dashboards and charts
- `analytics.delete` - Delete dashboards and charts

These permissions can be assigned at the module level through the admin interface.