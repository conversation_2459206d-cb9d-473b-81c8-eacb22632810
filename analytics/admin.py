from django.contrib import admin
from .models import Dashboard, Chart, ChartGroup


@admin.register(Dashboard)
class DashboardAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_public', 'owner', 'sort_order', 'is_active', 'created_at')
    list_filter = ('is_public', 'is_active')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Dashboard Settings', {
            'fields': ('layout', 'is_public', 'owner', 'sort_order')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ChartGroup)
class ChartGroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'dashboard', 'sort_order', 'is_active', 'created_at')
    list_filter = ('is_active', 'dashboard')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Group Settings', {
            'fields': ('dashboard', 'sort_order')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Chart)
class ChartAdmin(admin.ModelAdmin):
    list_display = ('name', 'chart_type', 'dashboard', 'chart_group', 'sort_order', 'refresh_interval', 'is_active')
    list_filter = ('chart_type', 'is_active', 'dashboard', 'chart_group')
    search_fields = ('name', 'code', 'description', 'query')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Chart Settings', {
            'fields': ('dashboard', 'chart_group', 'chart_type', 'query', 'config', 'filter_config', 'refresh_interval', 'position', 'sort_order')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )