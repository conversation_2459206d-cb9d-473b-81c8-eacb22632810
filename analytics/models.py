from django.db import models
from core.models import BaseModel, BaseEntity
from authentication.models import User


class Dashboard(BaseEntity):
    """
    Dashboard model to store dashboard configurations
    """
    layout = models.JSONField(default=dict, help_text="Dashboard layout configuration")
    is_public = models.BooleanField(default=False, help_text="Whether this dashboard is public or private")
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='dashboards')
    sort_order = models.IntegerField(default=0, help_text="Order in which the dashboard appears (lower numbers appear first)")
    
    class Meta:
        db_table = 'mes_analytics_dashboards'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class ChartGroup(BaseEntity):
    """
    ChartGroup model to organize charts within a dashboard
    """
    dashboard = models.ForeignKey(Dashboard, on_delete=models.CASCADE, related_name='chart_groups')
    description = models.TextField(blank=True, null=True, help_text="Optional description of the chart group")
    sort_order = models.Integer<PERSON>ield(default=0, help_text="Order in which the group appears within a dashboard (lower numbers appear first)")

    class Meta:
        db_table = 'mes_analytics_chart_groups'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class Chart(BaseEntity):
    """
    Chart model to store chart configurations
    """
    CHART_TYPES = [
        ('line', 'Line Chart'),
        ('bar', 'Bar Chart'),
        ('pie', 'Pie Chart'),
        ('scatter', 'Scatter Plot'),
        ('table', 'Table'),
        ('gauge', 'Gauge'),
        ('heatmap', 'Heatmap'),
        ('custom', 'Custom'),
    ]

    dashboard = models.ForeignKey(Dashboard, on_delete=models.CASCADE, related_name='charts', null=True, blank=True)
    chart_group = models.ForeignKey(ChartGroup, on_delete=models.SET_NULL, related_name='charts', null=True, blank=True,
                                   help_text="Optional group this chart belongs to")
    chart_type = models.CharField(max_length=20, choices=CHART_TYPES)
    query = models.TextField(help_text="SQL query for the chart data")
    config = models.JSONField(default=dict, help_text="Chart configuration in ECharts format")
    filter_config = models.JSONField(default=dict, help_text="Filter configuration/schema that can be applied to the chart query")
    refresh_interval = models.IntegerField(default=0, help_text="Refresh interval in seconds, 0 means no auto refresh")
    position = models.JSONField(default=dict, help_text="Position in the dashboard grid")
    sort_order = models.IntegerField(default=0, help_text="Order in which the chart appears within a dashboard (lower numbers appear first)")

    class Meta:
        db_table = 'mes_analytics_charts'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

    def clean(self):
        """
        Validate the chart data before saving
        """
        from django.core.exceptions import ValidationError
        from analytics.validators import SQLQueryValidator
        
        # Validate SQL query to prevent malicious operations
        if self.query:
            is_valid, error_message = SQLQueryValidator.validate_query(self.query)
            if not is_valid:
                raise ValidationError({'query': f'Invalid SQL query: {error_message}'})
        
        # Validate filter_config JSON schema
        if self.filter_config:
            self._validate_filter_config()
        
        super().clean()
        
    def _validate_filter_config(self):
        """
        Validate the filter_config JSON schema to ensure it follows the required format
        where each key can only have values from the set: "int", "str", "date", "bool"
        """
        from django.core.exceptions import ValidationError
        
        ALLOWED_VALUES = ["int", "str", "date", "bool"]
        
        if not isinstance(self.filter_config, dict):
            raise ValidationError({'filter_config': 'Filter configuration must be a JSON object'})
            
        for key, value in self.filter_config.items():
            if not isinstance(value, str):
                raise ValidationError({'filter_config': f'Value for key "{key}" must be a string'})
                
            if value not in ALLOWED_VALUES:
                raise ValidationError({'filter_config': f'Value for key "{key}" must be one of: {", ".join(ALLOWED_VALUES)}'})


    def save(self, *args, **kwargs):
        """
        Override save method to ensure validation is performed
        """
        self.clean()
        super().save(*args, **kwargs)