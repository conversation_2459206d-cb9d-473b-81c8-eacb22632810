import re
from typing import List, Tuple, Optional


class SQLQueryValidator:
    """
    Validator for SQL queries to prevent malicious operations
    """
    # Dangerous SQL operations that should be blocked
    DANGEROUS_OPERATIONS = [
        r'\bDROP\b',
        r'\bDELETE\b',
        r'\bTRUNCATE\b',
        r'\bALTER\b',
        r'\bCREATE\b',
        r'\bINSERT\b',
        r'\bUPDATE\b',
        r'\bREPLACE\b',
        r'\bMERGE\b',
        r'\bGRANT\b',
        r'\bREVOKE\b',
        r'\bSET\b',
        r'\bRENAME\b',
    ]
    
    # Allowed operations
    ALLOWED_OPERATIONS = [
        r'\bSELECT\b',
        r'\bWITH\b',
        r'\bFROM\b',
        r'\bWHERE\b',
        r'\bGROUP BY\b',
        r'\bHAVING\b',
        r'\bORDER BY\b',
        r'\bLIMIT\b',
        r'\bOFFSET\b',
        r'\bJOIN\b',
    ]
    
    # Optional: Whitelist of allowed tables
    # This should be configured based on your specific requirements
    ALLOWED_TABLES: List[str] = []
    
    @classmethod
    def validate_query(cls, query: str) -> Tuple[bool, Optional[str]]:
        """
        Validate a SQL query to ensure it doesn't contain dangerous operations
        
        Args:
            query: The SQL query to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Convert query to uppercase for case-insensitive matching
        query_upper = query.upper()
        
        # Check for dangerous operations
        for pattern in cls.DANGEROUS_OPERATIONS:
            if re.search(pattern, query_upper, re.IGNORECASE):
                operation = re.search(pattern, query_upper, re.IGNORECASE).group(0)
                return False, f"Query contains dangerous operation: {operation}"
        
        # Check if query starts with SELECT or WITH (for CTEs)
        if not (query_upper.strip().startswith('SELECT') or query_upper.strip().startswith('WITH')):
            return False, "Query must start with SELECT or WITH"
        
        # Optional: Check for allowed tables if whitelist is configured
        if cls.ALLOWED_TABLES:
            tables_in_query = cls._extract_tables_from_query(query)
            for table in tables_in_query:
                if table not in cls.ALLOWED_TABLES:
                    return False, f"Table '{table}' is not in the allowed tables list"
        
        return True, None
    
    @classmethod
    def _extract_tables_from_query(cls, query: str) -> List[str]:
        """
        Extract table names from a SQL query
        This is a simplified implementation and might not cover all SQL syntax variations
        
        Args:
            query: The SQL query
            
        Returns:
            List of table names found in the query
        """
        # This is a simplified implementation
        # In a production environment, consider using a proper SQL parser
        tables = []
        # Look for tables after FROM and JOIN keywords
        from_clauses = re.findall(r'\bFROM\s+([\w\.]+)', query, re.IGNORECASE)
        join_clauses = re.findall(r'\bJOIN\s+([\w\.]+)', query, re.IGNORECASE)
        
        tables.extend(from_clauses)
        tables.extend(join_clauses)
        
        # Remove any schema prefixes (e.g., 'schema.table' -> 'table')
        tables = [t.split('.')[-1] for t in tables]
        
        return tables