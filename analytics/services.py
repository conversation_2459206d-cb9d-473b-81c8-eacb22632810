import json
import requests
import pandas as pd
from django.conf import settings
from django.db import connections
from typing import Dict, List, Any, Optional


class ClickHouseService:
    """
    Service for interacting with ClickHouse database using Django's database connections
    """
    def __init__(self, using='clickhouse'):
        # Use Django's database connection pool
        self.using = using

    def execute_query(self, query: str) -> pd.DataFrame:
        """
        Execute a SQL query and return the results as a pandas DataFrame

        The query is first validated to ensure it doesn't contain dangerous operations
        """
        from analytics.validators import SQLQueryValidator

        # Validate the query before execution
        is_valid, error_message = SQLQueryValidator.validate_query(query)
        if not is_valid:
            print(f"Query validation failed: {error_message}")
            return pd.DataFrame({
                'error': [f"Query validation failed: {error_message}"],
                'query': [query]
            })

        try:
            # Use Django's database connection
            with connections[self.using].cursor() as cursor:
                cursor.execute(query)
                # Get column names from cursor description
                columns = [col[0] for col in cursor.description] if cursor.description else []
                # Fetch all rows
                rows = cursor.fetchall()
                # Create DataFrame
                df = pd.DataFrame(rows, columns=columns)
                return df
        except Exception as e:
            print(f"Error executing query: {str(e)}")
            return pd.DataFrame()


class ChartTransformerBase:
    """
    Base class for chart transformers using Strategy Pattern
    """
    @staticmethod
    def handle_error_or_empty(df: pd.DataFrame) -> Optional[Dict]:
        """
        Handle error or empty dataframes
        
        Returns None if no error, otherwise returns error config
        """
        if 'error' in df.columns:
            return {
                "title": {"text": "Query Error"},
                "tooltip": {"formatter": df['error'].iloc[0] if not df.empty else "Unknown error"},
                "series": []
            }
            
        if df.empty:
            return {
                "title": {"text": "No Data Available"},
                "series": []
            }
        
        return None
    
    @staticmethod
    def create_dataset(df: pd.DataFrame) -> Dict:
        """
        Create a dataset configuration from a DataFrame
        
        Returns a dataset configuration that can be used in ECharts
        """
        if df.empty:
            return {}
            
        # Convert DataFrame to dataset source format
        # First row is column names, subsequent rows are data
        source = [df.columns.tolist()]  # First row is column names
        for _, row in df.iterrows():
            source.append(row.tolist())  # Add data rows
            
        return {
            "dimensions": df.columns.tolist(),
            "source": source
        }

    @staticmethod
    def transform(df: pd.DataFrame, config: Dict) -> Dict:
        """
        Transform dataframe to chart format
        """
        raise NotImplementedError("Subclasses must implement transform method")


class LineBarTransformer(ChartTransformerBase):
    """
    Transformer for line and bar charts using dataset approach
    """
    @staticmethod
    def transform(df: pd.DataFrame, config: Dict, chart_type: str) -> Dict:
        result = config.copy()
        
        # Create dataset from DataFrame if not empty
        if not df.empty:
            # Create dataset configuration
            dataset_config = ChartTransformerBase.create_dataset(df)
            
            # Add or update dataset in result
            if "dataset" not in result:
                result["dataset"] = dataset_config
            else:
                # If dataset already exists in config, update it
                result["dataset"].update(dataset_config)
        
        # Update or create xAxis configuration
        if "xAxis" not in result:
            result["xAxis"] = {"type": "category"}
        elif "type" not in result["xAxis"]:
            result["xAxis"]["type"] = "category"
            
        # Remove data from xAxis if it exists (now using dataset)
        if "data" in result.get("xAxis", {}):
            del result["xAxis"]["data"]

        # Update or create yAxis configuration
        if "yAxis" not in result:
            result["yAxis"] = {"type": "value"}

        # Create or update series without data (data comes from dataset)
        if "series" not in result or not isinstance(result["series"], list) or not result["series"]:
            # No existing series, create new ones based on columns (excluding first column which is for x-axis)
            result["series"] = []
            if not df.empty:
                for col in df.columns[1:]:
                    result["series"].append({
                        "name": col,
                        "type": chart_type,
                        # No data here, it comes from dataset
                        "encode": {
                            "x": df.columns[0],  # First column is x-axis
                            "y": col             # Current column is y-axis
                        }
                    })
        else:
            # Update existing series to use dataset
            existing_series_count = len(result["series"])
            
            # First update existing series
            for i, series_item in enumerate(result["series"]):
                # Remove data from series if it exists
                if "data" in series_item:
                    del series_item["data"]
                    
                # Ensure type is set correctly
                series_item["type"] = chart_type
                
                # Add encode if not empty dataframe and we have enough columns
                if not df.empty and i < len(df.columns) - 1:
                    series_item["encode"] = {
                        "x": df.columns[0],      # First column is x-axis
                        "y": df.columns[i + 1]   # Column for this series
                    }
            
            # Add new series for any additional columns in the dataframe
            if not df.empty and len(df.columns) > existing_series_count + 1:
                for i in range(existing_series_count, len(df.columns) - 1):
                    col = df.columns[i + 1]
                    result["series"].append({
                        "name": col,
                        "type": chart_type,
                        "encode": {
                            "x": df.columns[0],  # First column is x-axis
                            "y": col             # Current column is y-axis
                        }
                    })

        return result


class PieTransformer(ChartTransformerBase):
    """
    Transformer for pie charts using dataset approach
    """
    @staticmethod
    def transform(df: pd.DataFrame, config: Dict) -> Dict:
        result = config.copy()

        # Create dataset from DataFrame if not empty
        if not df.empty:
            # Create dataset configuration
            dataset_config = ChartTransformerBase.create_dataset(df)
            
            # Add or update dataset in result
            if "dataset" not in result:
                result["dataset"] = dataset_config
            else:
                # If dataset already exists in config, update it
                result["dataset"].update(dataset_config)

        # Create or update series without data (data comes from dataset)
        if "series" not in result or not isinstance(result["series"], list) or not result["series"]:
            # No existing series, create new one
            result["series"] = [{
                "type": "pie",
                # No data here, it comes from dataset
                "encode": {
                    "itemName": df.columns[0] if not df.empty else "name",  # First column for item names
                    "value": df.columns[1] if not df.empty and len(df.columns) > 1 else "value"  # Second column for values
                }
            }]
        else:
            # Update existing series to use dataset
            pie_series_found = False
            for series_item in result["series"]:
                if series_item.get("type") == "pie":
                    # Remove data from series if it exists
                    if "data" in series_item:
                        del series_item["data"]
                    
                    # Add encode if not already present
                    if "encode" not in series_item and not df.empty:
                        series_item["encode"] = {
                            "itemName": df.columns[0],  # First column for item names
                            "value": df.columns[1] if len(df.columns) > 1 else df.columns[0]  # Second column for values
                        }
                    
                    pie_series_found = True
                    break
            
            # If no pie series found, add a new one
            if not pie_series_found:
                result["series"].append({
                    "type": "pie",
                    "encode": {
                        "itemName": df.columns[0] if not df.empty else "name",
                        "value": df.columns[1] if not df.empty and len(df.columns) > 1 else "value"
                    }
                })

        return result


class TableTransformer(ChartTransformerBase):
    """
    Transformer for table format
    """
    @staticmethod
    def transform(df: pd.DataFrame, config: Dict) -> Dict:
        result = config.copy()
        
        # For tables, we still need to return in a specific format
        # but we'll preserve any existing config and add dataset if available
        if not df.empty:
            # Add dataset for reference
            dataset_config = ChartTransformerBase.create_dataset(df)
            if "dataset" not in result:
                result["dataset"] = dataset_config
            else:
                result["dataset"].update(dataset_config)
        
        # Always include columns and data for tables
        result["columns"] = df.columns.tolist() if not df.empty else result.get("columns", [])
        result["data"] = df.values.tolist() if not df.empty else result.get("data", [])
        
        return result


class FilterService:
    """
    Service for handling SQL query filters
    """
    @staticmethod
    def build_filter_conditions(filter_config: Dict, query_params: Dict) -> List[str]:
        """
        Build filter conditions based on filter configuration and query parameters
        
        Args:
            filter_config: Dictionary mapping filter keys to their types
            query_params: Request query parameters
            
        Returns:
            List of SQL filter conditions
        """
        filter_conditions = []
        print("__filter_config__", filter_config)
        print("__query_params__", query_params)
        
        if not filter_config or not query_params:
            return filter_conditions
            
        for filter_key, filter_type in filter_config.items():
            if filter_key in query_params:
                filter_value = query_params.get(filter_key)
                print("__filter_value__", filter_key, filter_value)
                # Process filter based on its type
                if filter_type in ['date', 'int']:
                    try:
                        values = query_params.getlist(filter_key) # Get in list format, either single value or range
                        if not isinstance(values, list):
                            continue
                        if len(values) == 1:
                            # Exact match
                            if filter_type == 'int':
                                # Convert to integer if it's a string
                                if isinstance(values[0], str):
                                    values[0] = int(values[0])
                                filter_conditions.append(f"{filter_key} = {values[0]}")
                            else:  # date type
                                filter_conditions.append(f"{filter_key} = '{values[0]}'")
                        elif len(values) == 2:
                            # Range match
                            if filter_type == 'int':
                                # Convert to integers if they're strings
                                if isinstance(values[0], str):
                                    values[0] = int(values[0])
                                if isinstance(values[1], str):
                                    values[1] = int(values[1])
                                filter_conditions.append(f"{filter_key} BETWEEN {values[0]} AND {values[1]}")
                            else:  # date type
                                filter_conditions.append(f"{filter_key} BETWEEN '{values[0]}' AND '{values[1]}'")
                    except (json.JSONDecodeError, ValueError, TypeError) as e:
                        print(f"Error processing filter {filter_key}: {str(e)}")
                        continue

                elif filter_type == 'bool':
                    # Boolean filter (0 or 1)
                    if filter_value in ['0', '1']:
                        filter_conditions.append(f"{filter_key} = {filter_value}")
                        
                elif filter_type == 'str':
                    # String filter
                    if '%' in filter_value:
                        # LIKE operator for partial matches
                        filter_conditions.append(f"{filter_key} LIKE '{filter_value}'")
                    else:
                        # Exact match
                        filter_conditions.append(f"{filter_key} = '{filter_value}'")

        print("__filter_conditions__", filter_conditions)
        return filter_conditions
    
    @staticmethod
    def apply_filters_to_query(query: str, filter_conditions: List[str]) -> str:
        """
        Apply filter conditions to a SQL query
        
        Args:
            query: Original SQL query
            filter_conditions: List of SQL filter conditions
            
        Returns:
            Modified SQL query with filters applied
        """
        if not filter_conditions:
            return query
            
        # Check if the query already has a WHERE clause
        if 'WHERE' in query.upper():
            # Add filters with AND condition
            query = query.replace('WHERE', 'WHERE (', 1) + ') AND ' + ' AND '.join(filter_conditions)
        else:
            # Add WHERE clause with filters
            # Find appropriate position to add WHERE clause (after FROM clause and before GROUP BY, ORDER BY, etc.)
            clauses = ['GROUP BY', 'ORDER BY', 'LIMIT', 'HAVING']
            position = len(query)
            
            for clause in clauses:
                clause_pos = query.upper().find(clause)
                if clause_pos != -1 and clause_pos < position:
                    position = clause_pos
            
            # Insert WHERE clause at the determined position
            query = query[:position] + ' WHERE ' + ' AND '.join(filter_conditions) + ' ' + query[position:]
        
        return query


class ChartService:
    """
    Service for transforming data into chart format
    """
    @staticmethod
    def get_chart_data(chart) -> pd.DataFrame:
        """
        Get data for a chart by executing its query

        Args:
            chart: Chart model instance

        Returns:
            DataFrame with chart data or error information
        """
        clickhouse_service = ClickHouseService()
        return clickhouse_service.execute_query(chart.query)
        
    @staticmethod
    def get_charts_by_dashboard(dashboard_id):
        """
        Get all charts for a dashboard, including those in chart groups
        
        Args:
            dashboard_id: ID of the dashboard
            
        Returns:
            List of chart objects ordered by sort_order
        """
        from analytics.models import Chart, ChartGroup
        
        # Get charts directly attached to dashboard
        direct_charts = Chart.objects.filter(dashboard_id=dashboard_id).order_by('sort_order')
        
        # Get charts from chart groups in this dashboard
        chart_groups = ChartGroup.objects.filter(dashboard_id=dashboard_id).order_by('sort_order')
        group_charts = Chart.objects.filter(chart_group__in=chart_groups).order_by('chart_group__sort_order', 'sort_order')
        
        # Combine both querysets
        return list(direct_charts) + list(group_charts)
        
    @staticmethod
    def get_charts_by_chart_group(chart_group_id):
        """
        Get all charts for a specific chart group
        
        Args:
            chart_group_id: ID of the chart group
            
        Returns:
            List of chart objects ordered by sort_order
        """
        from analytics.models import Chart
        
        return Chart.objects.filter(chart_group_id=chart_group_id).order_by('sort_order')

    @staticmethod
    def transform_to_echarts(df: pd.DataFrame, chart_type: str, config: Dict) -> Dict:
        """
        Transform pandas DataFrame to ECharts format based on chart type
        
        If data is not available from the datasource but sample data exists in config,
        the config with sample data will be returned without modification.
        """
        # Check if we have a dataset in the config already (sample data)
        has_sample_data = False
        if "dataset" in config and config["dataset"] and (
            ("source" in config["dataset"] and config["dataset"]["source"]) or
            ("dimensions" in config["dataset"] and config["dataset"]["dimensions"])
        ):
            has_sample_data = True
            
        # Check for errors in the dataframe
        if 'error' in df.columns:
            error_result = ChartTransformerBase.handle_error_or_empty(df)
            if error_result:
                return error_result
        
        # If dataframe is empty but we have sample data in config, return config as is
        if df.empty and has_sample_data:
            return config.copy()
            
        # If dataframe is empty and no sample data, show empty message
        if df.empty and not has_sample_data:
            return {
                "title": {"text": "No Data Available"},
                "series": []
            }

        # Use the appropriate transformer based on chart type
        if chart_type == 'line' or chart_type == 'bar':
            return LineBarTransformer.transform(df, config, chart_type)
        elif chart_type == 'pie':
            return PieTransformer.transform(df, config)
        elif chart_type == 'table':
            return TableTransformer.transform(df, config)
        else:
            # Default case for unsupported chart types
            # Add dataset to config if data is available
            result = config.copy()
            if not df.empty:
                dataset_config = ChartTransformerBase.create_dataset(df)
                if "dataset" not in result:
                    result["dataset"] = dataset_config
                else:
                    result["dataset"].update(dataset_config)
            return result
