from rest_framework import serializers
from .models import Dashboard, Chart, ChartGroup


class ChartSerializer(serializers.ModelSerializer):
    """Serializer for Chart model"""
    class Meta:
        model = Chart
        fields = ['id', 'name', 'code', 'description', 'is_active', 
                  'chart_type', 'filter_config', 'config', 'position',
                  'dashboard', 'chart_group', 'sort_order', 'query', 'refresh_interval',
                  'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class ChartGroupSerializer(serializers.ModelSerializer):
    """Serializer for ChartGroup model"""
    charts = ChartSerializer(many=True, read_only=True)

    class Meta:
        model = ChartGroup
        fields = ['id', 'name', 'code', 'description', 'is_active',
                  'dashboard', 'charts', 'sort_order', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class DashboardSerializer(serializers.ModelSerializer):
    """Serializer for Dashboard model"""
    charts = ChartSerializer(many=True, read_only=True)
    chart_groups = ChartGroupSerializer(many=True, read_only=True)
    
    class Meta:
        model = Dashboard
        fields = ['id', 'name', 'code', 'description', 'is_active', 
                  'layout', 'is_public', 'owner', 'charts', 'chart_groups',
                  'sort_order', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        # Set the owner to the current user if not provided
        if 'owner' not in validated_data:
            validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)