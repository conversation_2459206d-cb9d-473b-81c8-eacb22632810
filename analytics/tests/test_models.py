import unittest
from unittest.mock import patch
from django.core.exceptions import ValidationError
from django.test import TestCase
from analytics.models import Chart, Dashboard
from authentication.models import User


class TestChartModel(TestCase):
    """Test cases for the Chart model"""
    
    def setUp(self):
        """Set up test environment"""
        # Create a test user
        self.user = User.objects.create(
            email='<EMAIL>',
            username='testuser',
            password='password123'
        )
        
        # Create a test dashboard
        self.dashboard = Dashboard.objects.create(
            name='Test Dashboard',
            code='test-dashboard',
            owner=self.user
        )
    
    @patch('analytics.validators.SQLQueryValidator.validate_query')
    def test_save_with_valid_query(self, mock_validate):
        """Test that a chart with a valid query can be saved"""
        # Mock the validator to return valid
        mock_validate.return_value = (True, None)
        
        # Create a chart with a valid query
        chart = Chart(
            name='Test Chart',
            code='test-chart',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM events'
        )
        
        # This should not raise an exception
        chart.save()
        
        # Verify the validator was called
        mock_validate.assert_called_once_with('SELECT * FROM events')
        
        # Verify the chart was saved
        self.assertIsNotNone(chart.id)
    
    @patch('analytics.validators.SQLQueryValidator.validate_query')
    def test_save_with_invalid_query(self, mock_validate):
        """Test that a chart with an invalid query cannot be saved"""
        # Mock the validator to return invalid
        mock_validate.return_value = (False, "Query contains dangerous operation: DROP")
        
        # Create a chart with an invalid query
        chart = Chart(
            name='Test Chart',
            code='test-chart',
            dashboard=self.dashboard,
            chart_type='bar',
            query='DROP TABLE events'
        )
        
        # This should raise a ValidationError
        with self.assertRaises(ValidationError):
            chart.save()
        
        # Verify the validator was called
        mock_validate.assert_called_once_with('DROP TABLE events')
    
    @patch('analytics.validators.SQLQueryValidator.validate_query')
    def test_clean_with_invalid_query(self, mock_validate):
        """Test that clean() raises ValidationError for invalid queries"""
        # Mock the validator to return invalid
        mock_validate.return_value = (False, "Query contains dangerous operation: DELETE")
        
        # Create a chart with an invalid query
        chart = Chart(
            name='Test Chart',
            code='test-chart',
            dashboard=self.dashboard,
            chart_type='bar',
            query='DELETE FROM events'
        )
        
        # This should raise a ValidationError
        with self.assertRaises(ValidationError):
            chart.clean()
        
        # Verify the validator was called
        mock_validate.assert_called_once_with('DELETE FROM events')


if __name__ == '__main__':
    unittest.main()