import unittest
from unittest.mock import patch, MagicMock
import pandas as pd
from analytics.services import ClickHouseService


class TestClickHouseService(unittest.TestCase):
    """Test cases for the ClickHouseService"""
    
    def setUp(self):
        """Set up test environment"""
        self.service = ClickHouseService()
    
    @patch('analytics.validators.SQLQueryValidator.validate_query')
    @patch('django.db.connections')
    def test_execute_query_with_valid_query(self, mock_connections, mock_validate):
        """Test that valid queries are executed"""
        # Mock the validator to return valid
        mock_validate.return_value = (True, None)
        
        # Mock the cursor and its methods
        mock_cursor = MagicMock()
        mock_cursor.description = [('col1', 'type1'), ('col2', 'type2')]
        mock_cursor.fetchall.return_value = [['value1', 'value2']]
        
        # Mock the connection and its cursor method
        mock_connection = MagicMock()
        mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
        mock_connections.__getitem__.return_value = mock_connection
        
        # Execute a query
        result = self.service.execute_query("SELECT * FROM table")
        
        # Verify the validator was called
        mock_validate.assert_called_once_with("SELECT * FROM table")
        
        # Verify the connection was accessed with the correct key
        mock_connections.__getitem__.assert_called_with('clickhouse')
        
        # Verify the result is a DataFrame with the expected columns
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(list(result.columns), ['col1', 'col2'])
    
    @patch('analytics.validators.SQLQueryValidator.validate_query')
    @patch('django.db.connections')
    def test_execute_query_with_invalid_query(self, mock_connections, mock_validate):
        """Test that invalid queries are not executed"""
        # Mock the validator to return invalid
        mock_validate.return_value = (False, "Query contains dangerous operation: DROP")
        
        # Mock connection (should not be used)
        mock_connection = MagicMock()
        mock_connections.__getitem__.return_value = mock_connection
        
        # Execute a query
        result = self.service.execute_query("DROP TABLE users")
        
        # Verify the validator was called
        mock_validate.assert_called_once_with("DROP TABLE users")
        
        # Verify the connection was not accessed
        mock_connections.__getitem__.assert_not_called()
        
        # Verify the result is a DataFrame with error information
        self.assertIsInstance(result, pd.DataFrame)
        self.assertTrue('error' in result.columns)
        self.assertTrue('query' in result.columns)
        self.assertIn("Query validation failed", result['error'].iloc[0])
    
    @patch('analytics.validators.SQLQueryValidator.validate_query')
    @patch('django.db.connections')
    def test_execute_query_with_db_error(self, mock_connections, mock_validate):
        """Test handling of database errors"""
        # Mock the validator to return valid
        mock_validate.return_value = (True, None)
        
        # Mock the connection to raise an exception
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_cursor.execute.side_effect = Exception("Database error")
        mock_connection.cursor.return_value.__enter__.return_value = mock_cursor
        mock_connections.__getitem__.return_value = mock_connection
        
        # Execute a query
        result = self.service.execute_query("SELECT * FROM table")
        
        # Verify the validator was called
        mock_validate.assert_called_once_with("SELECT * FROM table")
        
        # Verify the connection was accessed with the correct key
        mock_connections.__getitem__.assert_called_with('clickhouse')
        
        # Verify the result is an empty DataFrame
        self.assertIsInstance(result, pd.DataFrame)
        self.assertTrue(result.empty)


if __name__ == '__main__':
    unittest.main()