"""
Test cases for edge cases in chart configuration handling
"""
import unittest
import pandas as pd
from django.test import TestCase
from analytics.services import ChartService, ChartTransformerBase


class TestChartConfigEdgeCases(TestCase):
    """Test cases for chart configuration edge cases that could cause AttributeError"""
    
    def setUp(self):
        """Set up test data"""
        # Sample DataFrame similar to the one in the error logs
        self.df = pd.DataFrame({
            'part_name': ['HE317100 NAme', 'Invalid Product Part', 'Prog.PCA Cont. 1C1RS0SD M1000 V22.10', 'Solder Paste Make (OM-340)'],
            'count': [300, 160, 730, 420]
        })
    
    def test_config_with_list_dataset(self):
        """Test that config with list as dataset doesn't cause AttributeError"""
        # This is the problematic case that was causing the error
        config_with_list_dataset = {
            "title": {"text": "Test Chart"},
            "dataset": [  # This is a list, not a dict - this was causing the error
                ["part_name", "count"],
                ["A", 10],
                ["B", 20]
            ],
            "series": [{"type": "bar"}]
        }
        
        # This should not raise an AttributeError
        try:
            result = ChartService.transform_to_echarts(self.df, 'bar', config_with_list_dataset)
            self.assertIsInstance(result, dict)
            self.assertIn('dataset', result)
            # The dataset should now be a proper dict, not a list
            self.assertIsInstance(result['dataset'], dict)
            self.assertIn('dimensions', result['dataset'])
            self.assertIn('source', result['dataset'])
        except AttributeError as e:
            self.fail(f"transform_to_echarts raised AttributeError with list dataset: {e}")
    
    def test_config_as_list(self):
        """Test that config as list doesn't cause AttributeError"""
        config_as_list = ["invalid", "config", "format"]
        
        try:
            result = ChartService.transform_to_echarts(self.df, 'bar', config_as_list)
            self.assertIsInstance(result, dict)
            self.assertIn('dataset', result)
        except AttributeError as e:
            self.fail(f"transform_to_echarts raised AttributeError with list config: {e}")
    
    def test_config_as_none(self):
        """Test that None config doesn't cause AttributeError"""
        try:
            result = ChartService.transform_to_echarts(self.df, 'bar', None)
            self.assertIsInstance(result, dict)
            self.assertIn('dataset', result)
        except AttributeError as e:
            self.fail(f"transform_to_echarts raised AttributeError with None config: {e}")
    
    def test_config_with_nested_list_structures(self):
        """Test config with various nested list structures"""
        config_with_nested_lists = {
            "title": {"text": "Test Chart"},
            "dataset": [1, 2, 3],  # Simple list
            "series": [
                {
                    "type": "bar",
                    "data": [10, 20, 30]  # This is fine as it's data, not dataset
                }
            ],
            "xAxis": ["category1", "category2"]  # List instead of dict
        }
        
        try:
            result = ChartService.transform_to_echarts(self.df, 'bar', config_with_nested_lists)
            self.assertIsInstance(result, dict)
            self.assertIn('dataset', result)
            # The dataset should be replaced with proper dict structure
            self.assertIsInstance(result['dataset'], dict)
        except AttributeError as e:
            self.fail(f"transform_to_echarts raised AttributeError with nested lists: {e}")
    
    def test_ensure_config_is_dict_method(self):
        """Test the ensure_config_is_dict helper method directly"""
        test_cases = [
            ({"valid": "dict"}, dict),
            ([], dict),
            (["list", "items"], dict),
            (None, dict),
            ("string", dict),
            (123, dict),
            (True, dict),
        ]
        
        for input_config, expected_type in test_cases:
            with self.subTest(input_config=input_config):
                result = ChartTransformerBase.ensure_config_is_dict(input_config)
                self.assertIsInstance(result, expected_type)
                # Should always return a dict
                self.assertIsInstance(result, dict)
    
    def test_all_chart_types_with_problematic_config(self):
        """Test all chart types with the problematic config that was causing the error"""
        problematic_config = {
            "dataset": ["not", "a", "dict"],  # This was the root cause
            "title": {"text": "Test"}
        }
        
        chart_types = ['line', 'bar', 'pie', 'table', 'scatter', 'gauge', 'heatmap', 'custom']
        
        for chart_type in chart_types:
            with self.subTest(chart_type=chart_type):
                try:
                    result = ChartService.transform_to_echarts(self.df, chart_type, problematic_config)
                    self.assertIsInstance(result, dict)
                    # Should have a proper dataset now
                    if 'dataset' in result:
                        self.assertIsInstance(result['dataset'], dict)
                except AttributeError as e:
                    self.fail(f"Chart type '{chart_type}' raised AttributeError: {e}")
    
    def test_empty_dataframe_with_problematic_config(self):
        """Test empty DataFrame with problematic config"""
        empty_df = pd.DataFrame()
        problematic_config = {
            "dataset": ["sample", "data"],  # List instead of dict
            "title": {"text": "Empty Data Test"}
        }
        
        try:
            result = ChartService.transform_to_echarts(empty_df, 'bar', problematic_config)
            self.assertIsInstance(result, dict)
            # Should show "No Data Available" message
            self.assertIn('title', result)
        except AttributeError as e:
            self.fail(f"Empty DataFrame with problematic config raised AttributeError: {e}")


if __name__ == '__main__':
    unittest.main()
