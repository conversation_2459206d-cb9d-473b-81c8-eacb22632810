from django.test import TestCase
from django.core.exceptions import ValidationError
from analytics.models import Chart, Dashboard
from authentication.models import User


class FilterConfigValidationTest(TestCase):
    """Test cases for filter_config validation in Chart model"""
    
    def setUp(self):
        # Create a test user
        self.user = User.objects.create(username='testuser')
        
        # Create a test dashboard
        self.dashboard = Dashboard.objects.create(
            name='Test Dashboard',
            code='test-dashboard',
            owner=self.user
        )
    
    def test_valid_filter_config(self):
        """Test that valid filter_config values are accepted"""
        # Valid filter config with all allowed types
        valid_config = {
            'user_id': 'int',
            'name': 'str',
            'created_at': 'date',
            'is_active': 'bool'
        }
        
        chart = Chart(
            name='Test Chart',
            code='test-chart',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            filter_config=valid_config
        )
        
        # This should not raise any validation errors
        chart.full_clean()
        chart.save()
        
        # Verify the filter_config was saved correctly
        saved_chart = Chart.objects.get(id=chart.id)
        self.assertEqual(saved_chart.filter_config, valid_config)
    
    def test_invalid_filter_config_type(self):
        """Test that non-dict filter_config values are rejected"""
        # Invalid filter config - not a dict
        invalid_config = ['user_id', 'name']
        
        chart = Chart(
            name='Test Chart',
            code='test-chart-invalid-type',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            filter_config=invalid_config
        )
        
        # This should raise a validation error
        with self.assertRaises(ValidationError) as context:
            chart.full_clean()
        
        self.assertIn('filter_config', str(context.exception))
    
    def test_invalid_filter_config_value_type(self):
        """Test that non-string values in filter_config are rejected"""
        # Invalid filter config - value is not a string
        invalid_config = {
            'user_id': 123  # Should be 'int', not an actual integer
        }
        
        chart = Chart(
            name='Test Chart',
            code='test-chart-invalid-value-type',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            filter_config=invalid_config
        )
        
        # This should raise a validation error
        with self.assertRaises(ValidationError) as context:
            chart.full_clean()
        
        self.assertIn('filter_config', str(context.exception))
    
    def test_invalid_filter_config_value(self):
        """Test that invalid string values in filter_config are rejected"""
        # Invalid filter config - value is not one of the allowed values
        invalid_config = {
            'user_id': 'integer'  # Should be 'int', not 'integer'
        }
        
        chart = Chart(
            name='Test Chart',
            code='test-chart-invalid-value',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            filter_config=invalid_config
        )
        
        # This should raise a validation error
        with self.assertRaises(ValidationError) as context:
            chart.full_clean()
        
        self.assertIn('filter_config', str(context.exception))