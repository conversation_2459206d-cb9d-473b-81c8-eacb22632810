"""
Test cases for enhanced filter configuration with SQL clause specification
"""
import unittest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from analytics.models import Dashboard, Chart
from analytics.services import FilterService

User = get_user_model()


class TestEnhancedFilterConfig(TestCase):
    """Test cases for enhanced filter configuration functionality"""

    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.dashboard = Dashboard.objects.create(
            name='Test Dashboard',
            code='test-dashboard',
            owner=self.user
        )

    def test_legacy_filter_config_validation(self):
        """Test that legacy filter configuration format still works"""
        legacy_config = {
            'user_id': 'int',
            'name': 'str',
            'created_at': 'date',
            'is_active': 'bool'
        }

        chart = Chart(
            name='Test Chart',
            code='test-chart-legacy',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            config={'title': {'text': 'Test Chart'}},  # Required field
            position={'x': 0, 'y': 0, 'w': 6, 'h': 4},  # Required field
            filter_config=legacy_config
        )

        # This should not raise any validation errors
        chart.full_clean()
        chart.save()

        # Verify the filter_config was saved correctly
        saved_chart = Chart.objects.get(id=chart.id)
        self.assertEqual(saved_chart.filter_config, legacy_config)

    def test_enhanced_filter_config_validation(self):
        """Test that enhanced filter configuration format works"""
        enhanced_config = {
            'cl1': {
                'type': 'int',
                'clause': 'having'
            },
            'cl2': {
                'type': 'str',
                'clause': 'where'
            },
            'cl3': {
                'type': 'date',
                'clause': 'where'
            }
        }

        chart = Chart(
            name='Test Chart',
            code='test-chart-enhanced',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            config={'title': {'text': 'Test Chart'}},
            position={'x': 0, 'y': 0, 'w': 6, 'h': 4},
            filter_config=enhanced_config
        )

        # This should not raise any validation errors
        chart.full_clean()
        chart.save()

        # Verify the filter_config was saved correctly
        saved_chart = Chart.objects.get(id=chart.id)
        self.assertEqual(saved_chart.filter_config, enhanced_config)

    def test_mixed_filter_config_validation(self):
        """Test that mixed legacy and enhanced format works"""
        mixed_config = {
            'legacy_column': 'str',  # Legacy format
            'enhanced_column': {     # Enhanced format
                'type': 'int',
                'clause': 'having'
            }
        }

        chart = Chart(
            name='Test Chart',
            code='test-chart-mixed',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            config={'title': {'text': 'Test Chart'}},
            position={'x': 0, 'y': 0, 'w': 6, 'h': 4},
            filter_config=mixed_config
        )

        # This should not raise any validation errors
        chart.full_clean()
        chart.save()

        # Verify the filter_config was saved correctly
        saved_chart = Chart.objects.get(id=chart.id)
        self.assertEqual(saved_chart.filter_config, mixed_config)

    def test_enhanced_config_missing_type(self):
        """Test that enhanced format without 'type' field is rejected"""
        invalid_config = {
            'cl1': {
                'clause': 'having'  # Missing 'type' field
            }
        }

        chart = Chart(
            name='Test Chart',
            code='test-chart-invalid-missing-type',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            config={'title': {'text': 'Test Chart'}},
            position={'x': 0, 'y': 0, 'w': 6, 'h': 4},
            filter_config=invalid_config
        )

        # This should raise a validation error
        with self.assertRaises(ValidationError) as context:
            chart.full_clean()

        self.assertIn('filter_config', str(context.exception))
        self.assertIn('must include "type" field', str(context.exception))

    def test_enhanced_config_invalid_type(self):
        """Test that enhanced format with invalid 'type' is rejected"""
        invalid_config = {
            'cl1': {
                'type': 'invalid_type',
                'clause': 'where'
            }
        }

        chart = Chart(
            name='Test Chart',
            code='test-chart-invalid-type',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            config={'title': {'text': 'Test Chart'}},
            position={'x': 0, 'y': 0, 'w': 6, 'h': 4},
            filter_config=invalid_config
        )

        # This should raise a validation error
        with self.assertRaises(ValidationError) as context:
            chart.full_clean()

        self.assertIn('filter_config', str(context.exception))
        self.assertIn('must be one of: int, str, date, bool', str(context.exception))

    def test_enhanced_config_invalid_clause(self):
        """Test that enhanced format with invalid 'clause' is rejected"""
        invalid_config = {
            'cl1': {
                'type': 'int',
                'clause': 'invalid_clause'
            }
        }

        chart = Chart(
            name='Test Chart',
            code='test-chart-invalid-clause',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            config={'title': {'text': 'Test Chart'}},
            position={'x': 0, 'y': 0, 'w': 6, 'h': 4},
            filter_config=invalid_config
        )

        # This should raise a validation error
        with self.assertRaises(ValidationError) as context:
            chart.full_clean()

        self.assertIn('filter_config', str(context.exception))
        self.assertIn('must be one of: where, having', str(context.exception))

    def test_enhanced_config_unexpected_fields(self):
        """Test that enhanced format with unexpected fields is rejected"""
        invalid_config = {
            'cl1': {
                'type': 'int',
                'clause': 'where',
                'unexpected_field': 'value'
            }
        }

        chart = Chart(
            name='Test Chart',
            code='test-chart-unexpected-fields',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            config={'title': {'text': 'Test Chart'}},
            position={'x': 0, 'y': 0, 'w': 6, 'h': 4},
            filter_config=invalid_config
        )

        # This should raise a validation error
        with self.assertRaises(ValidationError) as context:
            chart.full_clean()

        self.assertIn('filter_config', str(context.exception))
        self.assertIn('Unexpected fields', str(context.exception))

    def test_get_normalized_filter_config_legacy(self):
        """Test normalization of legacy filter configuration"""
        legacy_config = {
            'user_id': 'int',
            'name': 'str'
        }

        chart = Chart(
            name='Test Chart',
            code='test-chart-normalize-legacy',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            config={'title': {'text': 'Test Chart'}},
            position={'x': 0, 'y': 0, 'w': 6, 'h': 4},
            filter_config=legacy_config
        )
        chart.save()

        normalized = chart.get_normalized_filter_config()

        expected = {
            'user_id': {
                'type': 'int',
                'clause': 'where'
            },
            'name': {
                'type': 'str',
                'clause': 'where'
            }
        }

        self.assertEqual(normalized, expected)

    def test_get_normalized_filter_config_enhanced(self):
        """Test normalization of enhanced filter configuration"""
        enhanced_config = {
            'cl1': {
                'type': 'int',
                'clause': 'having'
            },
            'cl2': {
                'type': 'str'  # Missing clause - should default to 'where'
            }
        }

        chart = Chart(
            name='Test Chart',
            code='test-chart-normalize-enhanced',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            config={'title': {'text': 'Test Chart'}},
            position={'x': 0, 'y': 0, 'w': 6, 'h': 4},
            filter_config=enhanced_config
        )
        chart.save()

        normalized = chart.get_normalized_filter_config()

        expected = {
            'cl1': {
                'type': 'int',
                'clause': 'having'
            },
            'cl2': {
                'type': 'str',
                'clause': 'where'  # Default clause
            }
        }

        self.assertEqual(normalized, expected)


if __name__ == '__main__':
    unittest.main()
