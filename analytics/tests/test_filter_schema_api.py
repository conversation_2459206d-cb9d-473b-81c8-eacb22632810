import json
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from analytics.models import Dashboard, Chart
from authentication.models import User


class FilterSchemaAPITest(TestCase):
    """Test cases for the Filter Schema API"""
    
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )
        
        # Create a test dashboard
        self.dashboard = Dashboard.objects.create(
            name='Test Dashboard',
            code='test-dashboard',
            owner=self.user
        )
        
        # Create a test chart with filter_config
        self.chart = Chart.objects.create(
            name='Test Chart',
            code='test-chart',
            dashboard=self.dashboard,
            chart_type='bar',
            query='SELECT * FROM test',
            filter_config={
                'user_id': 'int',
                'created_at': 'date',
                'is_active': 'bool'
            }
        )
        
        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_filter_schema_endpoint(self):
        """Test the filter schema endpoint"""
        url = reverse('charts-filter-schema')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('filter_types', response.data)
        
        # Check that all filter types are included
        filter_types = response.data['filter_types']
        self.assertIn('int', filter_types)
        self.assertIn('str', filter_types)
        self.assertIn('date', filter_types)
        self.assertIn('bool', filter_types)
    
    def test_filter_schema_with_chart_id(self):
        """Test the filter schema endpoint with chart_id parameter"""
        url = f"{reverse('charts-filter-schema')}?chart_id={self.chart.id}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('filter_types', response.data)
        self.assertIn('chart_filters', response.data)
        
        # Check that the chart's filter_config is included
        chart_filters = response.data['chart_filters']
        self.assertEqual(chart_filters, self.chart.filter_config)
    
    def test_filter_schema_for_chart_endpoint(self):
        """Test the filter schema for chart endpoint"""
        url = reverse('charts-filter-schema-for-chart', args=[self.chart.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('filter_types', response.data)
        self.assertIn('chart_filters', response.data)
        
        # Check that the chart's filter_config is included
        chart_filters = response.data['chart_filters']
        self.assertEqual(chart_filters, self.chart.filter_config)