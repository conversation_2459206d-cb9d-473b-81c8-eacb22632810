"""
Test cases for enhanced FilterService with clause-aware filtering
"""
import unittest
from django.test import TestCase
from django.http import QueryDict
from analytics.services import FilterService


class TestFilterServiceEnhanced(TestCase):
    """Test cases for enhanced FilterService functionality"""

    def test_build_filter_conditions_legacy_format(self):
        """Test building filter conditions with legacy format"""
        filter_config = {
            'user_id': 'int',
            'name': 'str',
            'is_active': 'bool'
        }

        query_params = QueryDict('user_id=123&name=John&is_active=1')

        conditions = FilterService.build_filter_conditions(filter_config, query_params)

        # All conditions should go to WHERE clause in legacy format
        self.assertIn('where', conditions)
        self.assertIn('having', conditions)
        self.assertEqual(len(conditions['having']), 0)
        self.assertEqual(len(conditions['where']), 3)

        where_conditions = conditions['where']
        self.assertIn('user_id = 123', where_conditions)
        self.assertIn("name = 'John'", where_conditions)
        self.assertIn('is_active = 1', where_conditions)

    def test_build_filter_conditions_enhanced_format(self):
        """Test building filter conditions with enhanced format"""
        filter_config = {
            'cl1': {
                'type': 'int',
                'clause': 'having'
            },
            'cl2': {
                'type': 'str',
                'clause': 'where'
            },
            'cl3': {
                'type': 'bool',
                'clause': 'having'
            }
        }

        query_params = QueryDict('cl1=100&cl2=test&cl3=1')

        conditions = FilterService.build_filter_conditions(filter_config, query_params)

        # Check WHERE conditions
        where_conditions = conditions['where']
        self.assertEqual(len(where_conditions), 1)
        self.assertIn("cl2 = 'test'", where_conditions)

        # Check HAVING conditions
        having_conditions = conditions['having']
        self.assertEqual(len(having_conditions), 2)
        self.assertIn('cl1 = 100', having_conditions)
        self.assertIn('cl3 = 1', having_conditions)

    def test_build_filter_conditions_mixed_format(self):
        """Test building filter conditions with mixed legacy and enhanced format"""
        filter_config = {
            'legacy_col': 'str',  # Legacy format - should go to WHERE
            'enhanced_col': {     # Enhanced format - explicit HAVING
                'type': 'int',
                'clause': 'having'
            }
        }

        query_params = QueryDict('legacy_col=value&enhanced_col=200')

        conditions = FilterService.build_filter_conditions(filter_config, query_params)

        # Check WHERE conditions (legacy format defaults to WHERE)
        where_conditions = conditions['where']
        self.assertEqual(len(where_conditions), 1)
        self.assertIn("legacy_col = 'value'", where_conditions)

        # Check HAVING conditions
        having_conditions = conditions['having']
        self.assertEqual(len(having_conditions), 1)
        self.assertIn('enhanced_col = 200', having_conditions)

    def test_normalize_filter_config(self):
        """Test the _normalize_filter_config method"""
        mixed_config = {
            'legacy_col': 'str',
            'enhanced_col': {
                'type': 'int',
                'clause': 'having'
            },
            'enhanced_no_clause': {
                'type': 'date'  # No clause specified
            }
        }

        normalized = FilterService._normalize_filter_config(mixed_config)

        expected = {
            'legacy_col': {
                'type': 'str',
                'clause': 'where'
            },
            'enhanced_col': {
                'type': 'int',
                'clause': 'having'
            },
            'enhanced_no_clause': {
                'type': 'date',
                'clause': 'where'  # Default clause
            }
        }

        self.assertEqual(normalized, expected)

    def test_apply_filters_to_query_where_only(self):
        """Test applying WHERE filters to a query"""
        query = "SELECT part_name, count(*) FROM test_table GROUP BY part_name"
        filter_conditions = {
            'where': ['user_id = 123', "name = 'John'"],
            'having': []
        }

        result = FilterService.apply_filters_to_query(query, filter_conditions)

        expected = "SELECT part_name, count(*) FROM test_table WHERE user_id = 123 AND name = 'John' GROUP BY part_name"
        self.assertEqual(result, expected)

    def test_apply_filters_to_query_having_only(self):
        """Test applying HAVING filters to a query"""
        query = "SELECT part_name, count(*) as cnt FROM test_table GROUP BY part_name"
        filter_conditions = {
            'where': [],
            'having': ['cnt > 100', 'cnt < 1000']
        }

        result = FilterService.apply_filters_to_query(query, filter_conditions)

        expected = "SELECT part_name, count(*) as cnt FROM test_table GROUP BY part_name HAVING cnt > 100 AND cnt < 1000"
        self.assertEqual(result, expected)

    def test_apply_filters_to_query_both_clauses(self):
        """Test applying both WHERE and HAVING filters to a query"""
        query = "SELECT part_name, count(*) as cnt FROM test_table GROUP BY part_name"
        filter_conditions = {
            'where': ['user_id = 123'],
            'having': ['cnt > 100']
        }

        result = FilterService.apply_filters_to_query(query, filter_conditions)

        expected = "SELECT part_name, count(*) as cnt FROM test_table WHERE user_id = 123 GROUP BY part_name HAVING cnt > 100"
        self.assertEqual(result, expected)

    def test_apply_filters_to_query_existing_where(self):
        """Test applying filters to a query that already has WHERE clause"""
        query = "SELECT * FROM test_table WHERE status = 'active' GROUP BY part_name"
        filter_conditions = {
            'where': ['user_id = 123'],
            'having': ['cnt > 100']
        }

        result = FilterService.apply_filters_to_query(query, filter_conditions)

        # Should wrap existing WHERE condition and add new ones
        self.assertIn("WHERE (status = 'active') AND user_id = 123", result)
        self.assertIn("HAVING cnt > 100", result)

    def test_apply_filters_to_query_existing_having(self):
        """Test applying filters to a query that already has HAVING clause"""
        query = "SELECT part_name, count(*) as cnt FROM test_table GROUP BY part_name HAVING cnt > 0"
        filter_conditions = {
            'where': ['user_id = 123'],
            'having': ['cnt < 1000']
        }

        result = FilterService.apply_filters_to_query(query, filter_conditions)

        # Should wrap existing HAVING condition and add new ones
        self.assertIn("WHERE user_id = 123", result)
        self.assertIn("HAVING (cnt > 0) AND cnt < 1000", result)

    def test_backward_compatibility_list_conditions(self):
        """Test backward compatibility when filter_conditions is a list"""
        query = "SELECT * FROM test_table"
        filter_conditions = ['user_id = 123', "name = 'John'"]  # Old format (list)

        result = FilterService.apply_filters_to_query(query, filter_conditions)

        expected = "SELECT * FROM test_table WHERE user_id = 123 AND name = 'John'"
        self.assertEqual(result, expected)

    def test_build_single_condition_int_exact(self):
        """Test building single condition for integer exact match"""
        query_params = QueryDict('test_col=123')
        condition = FilterService._build_single_condition('test_col', 'int', '123', query_params)
        self.assertEqual(condition, 'test_col = 123')

    def test_build_single_condition_int_range(self):
        """Test building single condition for integer range"""
        query_params = QueryDict('test_col=100&test_col=200')
        condition = FilterService._build_single_condition('test_col', 'int', '100', query_params)
        self.assertEqual(condition, 'test_col BETWEEN 100 AND 200')

    def test_build_single_condition_str_exact(self):
        """Test building single condition for string exact match"""
        query_params = QueryDict('test_col=value')
        condition = FilterService._build_single_condition('test_col', 'str', 'value', query_params)
        self.assertEqual(condition, "test_col = 'value'")

    def test_build_single_condition_str_like(self):
        """Test building single condition for string LIKE match"""
        query_params = QueryDict('test_col=%partial%')
        condition = FilterService._build_single_condition('test_col', 'str', '%partial%', query_params)
        self.assertEqual(condition, "test_col LIKE '%partial%'")

    def test_build_single_condition_bool(self):
        """Test building single condition for boolean"""
        query_params = QueryDict('test_col=1')
        condition = FilterService._build_single_condition('test_col', 'bool', '1', query_params)
        self.assertEqual(condition, 'test_col = 1')

    def test_build_single_condition_date_exact(self):
        """Test building single condition for date exact match"""
        query_params = QueryDict('test_col=2023-01-01')
        condition = FilterService._build_single_condition('test_col', 'date', '2023-01-01', query_params)
        self.assertEqual(condition, "test_col = '2023-01-01'")

    def test_build_single_condition_date_range(self):
        """Test building single condition for date range"""
        query_params = QueryDict('test_col=2023-01-01&test_col=2023-12-31')
        condition = FilterService._build_single_condition('test_col', 'date', '2023-01-01', query_params)
        self.assertEqual(condition, "test_col BETWEEN '2023-01-01' AND '2023-12-31'")

    def test_build_single_condition_int_range_reversed(self):
        """Test building single condition for integer range with reversed values"""
        query_params = QueryDict('test_col=730&test_col=160')
        condition = FilterService._build_single_condition('test_col', 'int', '730', query_params)
        # Should automatically sort to put smaller value first
        self.assertEqual(condition, 'test_col BETWEEN 160 AND 730')

    def test_build_single_condition_int_range_correct_order(self):
        """Test building single condition for integer range with correct order"""
        query_params = QueryDict('test_col=160&test_col=730')
        condition = FilterService._build_single_condition('test_col', 'int', '160', query_params)
        # Should maintain correct order
        self.assertEqual(condition, 'test_col BETWEEN 160 AND 730')

    def test_build_single_condition_date_range_reversed(self):
        """Test building single condition for date range with reversed values"""
        query_params = QueryDict('test_col=2023-12-01&test_col=2023-01-01')
        condition = FilterService._build_single_condition('test_col', 'date', '2023-12-01', query_params)
        # Should automatically sort to put earlier date first
        self.assertEqual(condition, "test_col BETWEEN '2023-01-01' AND '2023-12-01'")

    def test_build_single_condition_date_range_correct_order(self):
        """Test building single condition for date range with correct order"""
        query_params = QueryDict('test_col=2023-01-01&test_col=2023-12-01')
        condition = FilterService._build_single_condition('test_col', 'date', '2023-01-01', query_params)
        # Should maintain correct order
        self.assertEqual(condition, "test_col BETWEEN '2023-01-01' AND '2023-12-01'")

    def test_build_single_condition_int_range_equal_values(self):
        """Test building single condition for integer range with equal values"""
        query_params = QueryDict('test_col=100&test_col=100')
        condition = FilterService._build_single_condition('test_col', 'int', '100', query_params)
        # Should handle equal values correctly
        self.assertEqual(condition, 'test_col BETWEEN 100 AND 100')

    def test_build_single_condition_date_range_equal_values(self):
        """Test building single condition for date range with equal values"""
        query_params = QueryDict('test_col=2023-01-01&test_col=2023-01-01')
        condition = FilterService._build_single_condition('test_col', 'date', '2023-01-01', query_params)
        # Should handle equal values correctly
        self.assertEqual(condition, "test_col BETWEEN '2023-01-01' AND '2023-01-01'")

    def test_build_single_condition_int_range_negative_values(self):
        """Test building single condition for integer range with negative values"""
        query_params = QueryDict('test_col=50&test_col=-100')
        condition = FilterService._build_single_condition('test_col', 'int', '50', query_params)
        # Should sort negative values correctly
        self.assertEqual(condition, 'test_col BETWEEN -100 AND 50')

    def test_range_sorting_integration_where_clause(self):
        """Test that range sorting works correctly in WHERE clause"""
        filter_config = {
            'count': {
                'type': 'int',
                'clause': 'where'
            }
        }

        query_params = QueryDict('count=730&count=160')

        conditions = FilterService.build_filter_conditions(filter_config, query_params)

        # Check that the condition is properly sorted
        self.assertEqual(len(conditions['where']), 1)
        self.assertEqual(conditions['where'][0], 'count BETWEEN 160 AND 730')
        self.assertEqual(len(conditions['having']), 0)

    def test_range_sorting_integration_having_clause(self):
        """Test that range sorting works correctly in HAVING clause"""
        filter_config = {
            'total_sales': {
                'type': 'int',
                'clause': 'having'
            }
        }

        query_params = QueryDict('total_sales=100000&total_sales=50000')

        conditions = FilterService.build_filter_conditions(filter_config, query_params)

        # Check that the condition is properly sorted
        self.assertEqual(len(conditions['having']), 1)
        self.assertEqual(conditions['having'][0], 'total_sales BETWEEN 50000 AND 100000')
        self.assertEqual(len(conditions['where']), 0)

    def test_range_sorting_integration_date_where_clause(self):
        """Test that date range sorting works correctly in WHERE clause"""
        filter_config = {
            'created_at': {
                'type': 'date',
                'clause': 'where'
            }
        }

        query_params = QueryDict('created_at=2023-12-31&created_at=2023-01-01')

        conditions = FilterService.build_filter_conditions(filter_config, query_params)

        # Check that the condition is properly sorted
        self.assertEqual(len(conditions['where']), 1)
        self.assertEqual(conditions['where'][0], "created_at BETWEEN '2023-01-01' AND '2023-12-31'")
        self.assertEqual(len(conditions['having']), 0)


if __name__ == '__main__':
    unittest.main()
