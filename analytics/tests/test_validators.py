import unittest
from analytics.validators import SQLQueryValidator


class TestSQLQueryValidator(unittest.TestCase):
    """Test cases for the SQLQueryValidator"""
    
    def test_valid_queries(self):
        """Test that valid queries pass validation"""
        valid_queries = [
            "SELECT * FROM table",
            "SELECT id, name FROM users WHERE age > 18",
            "WITH cte AS (SELECT * FROM table) SELECT * FROM cte",
            "SELECT * FROM table1 JOIN table2 ON table1.id = table2.id",
            "SELECT COUNT(*) FROM events GROUP BY event_type HAVING COUNT(*) > 10",
            "SELECT * FROM table ORDER BY created_at DESC LIMIT 100",
        ]
        
        for query in valid_queries:
            is_valid, error = SQLQueryValidator.validate_query(query)
            self.assertTrue(is_valid, f"Query should be valid: {query}, but got error: {error}")
    
    def test_invalid_queries(self):
        """Test that invalid queries fail validation"""
        invalid_queries = [
            "DROP TABLE users",
            "DELETE FROM events",
            "UPDATE users SET active = 1",
            "INSERT INTO logs VALUES (1, 'test')",
            "CREATE TABLE new_table (id INT)",
            "ALTER TABLE users ADD COLUMN email VARCHAR(255)",
            "TRUNCATE TABLE logs",
            "GRANT SELECT ON users TO user",
            "REVOKE ALL PRIVILEGES FROM user",
            "SET global_var = 1",
            "RENAME TABLE old_name TO new_name",
        ]
        
        for query in invalid_queries:
            is_valid, error = SQLQueryValidator.validate_query(query)
            self.assertFalse(is_valid, f"Query should be invalid: {query}")
    
    def test_mixed_queries(self):
        """Test queries that mix valid and invalid operations"""
        mixed_queries = [
            "SELECT * FROM users; DROP TABLE users;",
            "WITH cte AS (SELECT * FROM table) DELETE FROM users",
            "SELECT * FROM table WHERE id IN (SELECT id FROM users); UPDATE users SET active = 1",
        ]
        
        for query in mixed_queries:
            is_valid, error = SQLQueryValidator.validate_query(query)
            self.assertFalse(is_valid, f"Query should be invalid: {query}")
    
    def test_case_insensitivity(self):
        """Test that validation is case-insensitive"""
        invalid_queries = [
            "drop TABLE users",
            "Delete FROM events",
            "update USERS set active = 1",
            "Insert INTO logs VALUES (1, 'test')",
        ]
        
        for query in invalid_queries:
            is_valid, error = SQLQueryValidator.validate_query(query)
            self.assertFalse(is_valid, f"Query should be invalid regardless of case: {query}")


if __name__ == '__main__':
    unittest.main()