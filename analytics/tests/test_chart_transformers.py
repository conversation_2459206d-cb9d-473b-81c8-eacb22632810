import unittest
import pandas as pd
from analytics.services import (
    ChartTransformerBase, LineBarTransformer, 
    PieTransformer, TableTransformer, ChartService
)


class TestChartTransformers(unittest.TestCase):
    """Test cases for the Chart Transformer classes"""
    
    def setUp(self):
        """Set up test data"""
        # Sample DataFrame for testing
        self.df = pd.DataFrame({
            'category': ['A', 'B', 'C'],
            'value1': [10, 20, 30],
            'value2': [5, 15, 25]
        })
        
        # Sample config with styling options
        self.config = {
            "title": {"text": "Test Chart"},
            "series": [{
                "name": "value1",
                "type": "line",
                "data": [],
                "showBackground": True,
                "backgroundStyle": {
                    "color": 'rgba(180, 180, 180, 0.2)'
                }
            }]
        }
    
    def test_base_transformer_error_handling(self):
        """Test error handling in base transformer"""
        # Test with error DataFrame
        error_df = pd.DataFrame({
            'error': ['Query validation failed: Test error'],
            'query': ['SELECT * FROM test']
        })
        
        result = ChartTransformerBase.handle_error_or_empty(error_df)
        self.assertIsNotNone(result)
        self.assertEqual(result['title']['text'], 'Query Error')
        
        # Test with empty DataFrame
        empty_df = pd.DataFrame()
        result = ChartTransformerBase.handle_error_or_empty(empty_df)
        self.assertIsNotNone(result)
        self.assertEqual(result['title']['text'], 'No Data Available')
        
        # Test with valid DataFrame
        result = ChartTransformerBase.handle_error_or_empty(self.df)
        self.assertIsNone(result)
        
    def test_create_dataset(self):
        """Test dataset creation from DataFrame"""
        # Test with valid DataFrame
        dataset = ChartTransformerBase.create_dataset(self.df)
        
        # Verify dataset structure
        self.assertIn('dimensions', dataset)
        self.assertIn('source', dataset)
        self.assertEqual(dataset['dimensions'], ['category', 'value1', 'value2'])
        self.assertEqual(len(dataset['source']), 4)  # Header row + 3 data rows
        self.assertEqual(dataset['source'][0], ['category', 'value1', 'value2'])
        
        # Test with empty DataFrame
        empty_dataset = ChartTransformerBase.create_dataset(pd.DataFrame())
        self.assertEqual(empty_dataset, {})
    
    def test_line_bar_transformer(self):
        """Test line/bar chart transformer with dataset approach"""
        # Test with existing config that has styling
        result = LineBarTransformer.transform(self.df, self.config, 'line')
        
        # Verify dataset was created
        self.assertIn('dataset', result)
        self.assertEqual(result['dataset']['dimensions'], ['category', 'value1', 'value2'])
        self.assertEqual(len(result['dataset']['source']), 4)  # Header row + 3 data rows
        
        # Verify xAxis no longer has data property
        self.assertIn('xAxis', result)
        self.assertNotIn('data', result['xAxis'])
        self.assertEqual(result['xAxis']['type'], 'category')
        
        # Verify series structure
        self.assertEqual(len(result['series']), 2)  # Should have two series
        
        # Check that first series has encode property instead of data
        self.assertNotIn('data', result['series'][0])
        self.assertIn('encode', result['series'][0])
        self.assertEqual(result['series'][0]['encode']['x'], 'category')
        self.assertEqual(result['series'][0]['encode']['y'], 'value1')
        self.assertEqual(result['series'][0]['name'], 'value1')
        self.assertEqual(result['series'][0]['type'], 'line')
        self.assertTrue(result['series'][0]['showBackground'])
        self.assertEqual(
            result['series'][0]['backgroundStyle']['color'], 
            'rgba(180, 180, 180, 0.2)'
        )
        
        # Check second series
        self.assertNotIn('data', result['series'][1])
        self.assertIn('encode', result['series'][1])
        self.assertEqual(result['series'][1]['encode']['x'], 'category')
        self.assertEqual(result['series'][1]['encode']['y'], 'value2')
        self.assertEqual(result['series'][1]['name'], 'value2')
        self.assertEqual(result['series'][1]['type'], 'line')
        
        # Test with empty config
        empty_config = {}
        result = LineBarTransformer.transform(self.df, empty_config, 'bar')
        
        # Verify default config was created
        self.assertIn('dataset', result)
        self.assertIn('xAxis', result)
        self.assertIn('yAxis', result)
        self.assertIn('series', result)
        self.assertEqual(len(result['series']), 2)
        self.assertEqual(result['series'][0]['type'], 'bar')
        self.assertIn('encode', result['series'][0])
    
    def test_pie_transformer(self):
        """Test pie chart transformer with dataset approach"""
        # Create a pie chart DataFrame
        pie_df = pd.DataFrame({
            'category': ['A', 'B', 'C'],
            'value': [10, 20, 30]
        })
        
        # Test with config that has existing pie series
        pie_config = {
            "series": [{
                "type": "pie",
                "data": [],
                "radius": '70%',
                "label": {"show": False}
            }]
        }
        
        result = PieTransformer.transform(pie_df, pie_config)
        
        # Verify dataset was created
        self.assertIn('dataset', result)
        self.assertEqual(result['dataset']['dimensions'], ['category', 'value'])
        self.assertEqual(len(result['dataset']['source']), 4)  # Header row + 3 data rows
        
        # Verify series structure
        self.assertEqual(len(result['series']), 1)
        self.assertEqual(result['series'][0]['type'], 'pie')
        self.assertNotIn('data', result['series'][0])
        self.assertIn('encode', result['series'][0])
        self.assertEqual(result['series'][0]['encode']['itemName'], 'category')
        self.assertEqual(result['series'][0]['encode']['value'], 'value')
        self.assertEqual(result['series'][0]['radius'], '70%')
        self.assertFalse(result['series'][0]['label']['show'])
        
        # Test with empty config
        empty_config = {}
        result = PieTransformer.transform(pie_df, empty_config)
        
        # Verify default config was created
        self.assertIn('dataset', result)
        self.assertIn('series', result)
        self.assertEqual(len(result['series']), 1)
        self.assertEqual(result['series'][0]['type'], 'pie')
        self.assertIn('encode', result['series'][0])
    
    def test_table_transformer(self):
        """Test table transformer with dataset approach"""
        result = TableTransformer.transform(self.df, self.config)
        
        # Table format should preserve config and add dataset
        self.assertIn('dataset', result)
        self.assertEqual(result['dataset']['dimensions'], ['category', 'value1', 'value2'])
        
        # Table still needs columns and data for direct rendering
        self.assertIn('columns', result)
        self.assertIn('data', result)
        self.assertEqual(result['columns'], ['category', 'value1', 'value2'])
        self.assertEqual(len(result['data']), 3)
        
        # Test with empty DataFrame
        empty_result = TableTransformer.transform(pd.DataFrame(), self.config)
        self.assertEqual(empty_result['columns'], [])
        self.assertEqual(empty_result['data'], [])
    
    def test_chart_service_integration(self):
        """Test ChartService integration with transformers using dataset approach"""
        # Test line chart
        result = ChartService.transform_to_echarts(self.df, 'line', self.config)
        self.assertIn('dataset', result)
        self.assertIn('xAxis', result)
        self.assertIn('series', result)
        self.assertEqual(len(result['series']), 2)
        
        # Test pie chart
        pie_df = pd.DataFrame({
            'category': ['A', 'B', 'C'],
            'value': [10, 20, 30]
        })
        result = ChartService.transform_to_echarts(pie_df, 'pie', {})
        self.assertIn('dataset', result)
        self.assertIn('series', result)
        self.assertEqual(result['series'][0]['type'], 'pie')
        
        # Test table
        result = ChartService.transform_to_echarts(self.df, 'table', {})
        self.assertIn('dataset', result)
        self.assertIn('columns', result)
        self.assertIn('data', result)
        
        # Test unsupported type
        custom_config = {"custom": "value"}
        result = ChartService.transform_to_echarts(self.df, 'unknown', custom_config)
        self.assertIn('dataset', result)
        self.assertEqual(result['custom'], 'value')
        self.assertNotEqual(id(result), id(custom_config))  # Should be a copy
        
        # Test error handling
        error_df = pd.DataFrame({
            'error': ['Query validation failed: Test error'],
            'query': ['SELECT * FROM test']
        })
        result = ChartService.transform_to_echarts(error_df, 'line', {})
        self.assertEqual(result['title']['text'], 'Query Error')
        
        # Test with empty DataFrame but sample data in config
        sample_config = {
            "dataset": {
                "dimensions": ["category", "value"],
                "source": [["category", "value"], ["A", 10], ["B", 20]]
            },
            "series": [{
                "type": "pie",
                "encode": {
                    "itemName": "category",
                    "value": "value"
                }
            }]
        }
        empty_df = pd.DataFrame()
        result = ChartService.transform_to_echarts(empty_df, 'pie', sample_config)
        # Should return config as is with sample data
        self.assertEqual(result, sample_config)
        self.assertNotEqual(id(result), id(sample_config))  # Should be a copy


if __name__ == '__main__':
    unittest.main()