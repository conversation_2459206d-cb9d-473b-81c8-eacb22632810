from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Dashboard',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('layout', models.JSONField(default=dict, help_text='Dashboard layout configuration')),
                ('is_public', models.BooleanField(default=False, help_text='Whether this dashboard is public or private')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dashboards', to='authentication.user')),
            ],
            options={
                'db_table': 'mes_analytics_dashboards',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Chart',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('chart_type', models.CharField(choices=[('line', 'Line Chart'), ('bar', 'Bar Chart'), ('pie', 'Pie Chart'), ('scatter', 'Scatter Plot'), ('table', 'Table'), ('gauge', 'Gauge'), ('heatmap', 'Heatmap')], max_length=20)),
                ('query', models.TextField(help_text='SQL query for the chart data')),
                ('config', models.JSONField(default=dict, help_text='Chart configuration in ECharts format')),
                ('refresh_interval', models.IntegerField(default=0, help_text='Refresh interval in seconds, 0 means no auto refresh')),
                ('position', models.JSONField(default=dict, help_text='Position in the dashboard grid')),
                ('dashboard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='charts', to='analytics.dashboard')),
            ],
            options={
                'db_table': 'mes_analytics_charts',
                'ordering': ['name'],
            },
        ),
    ]