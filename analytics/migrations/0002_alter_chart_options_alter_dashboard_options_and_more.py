# Generated by Django 5.1 on 2025-05-03 18:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analytics', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='chart',
            options={'ordering': ['sort_order', 'name']},
        ),
        migrations.AlterModelOptions(
            name='dashboard',
            options={'ordering': ['sort_order', 'name']},
        ),
        migrations.AddField(
            model_name='chart',
            name='filter_config',
            field=models.JSONField(default=dict, help_text='Filter configuration/schema that can be applied to the chart query'),
        ),
        migrations.AddField(
            model_name='chart',
            name='sort_order',
            field=models.IntegerField(default=0, help_text='Order in which the chart appears within a dashboard (lower numbers appear first)'),
        ),
        migrations.AddField(
            model_name='dashboard',
            name='sort_order',
            field=models.IntegerField(default=0, help_text='Order in which the dashboard appears (lower numbers appear first)'),
        ),
        migrations.AlterField(
            model_name='chart',
            name='chart_type',
            field=models.CharField(choices=[('line', 'Line Chart'), ('bar', 'Bar Chart'), ('pie', 'Pie Chart'), ('scatter', 'Scatter Plot'), ('table', 'Table'), ('gauge', 'Gauge'), ('heatmap', 'Heatmap'), ('custom', 'Custom')], max_length=20),
        ),
    ]
