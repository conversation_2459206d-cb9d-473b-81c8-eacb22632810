# Generated by Django 5.1 on 2025-05-17 20:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analytics', '0002_alter_chart_options_alter_dashboard_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChartGroup',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('description', models.TextField(blank=True, help_text='Optional description of the chart group', null=True)),
                ('sort_order', models.IntegerField(default=0, help_text='Order in which the group appears within a dashboard (lower numbers appear first)')),
                ('dashboard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chart_groups', to='analytics.dashboard')),
            ],
            options={
                'db_table': 'mes_analytics_chart_groups',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.AddField(
            model_name='chart',
            name='chart_group',
            field=models.ForeignKey(blank=True, help_text='Optional group this chart belongs to', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='charts', to='analytics.chartgroup'),
        ),
    ]
