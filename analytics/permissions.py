from rest_framework import permissions
from authentication.models import Module, UserModulePermission, GroupModulePermission


class AnalyticsPermission(permissions.BasePermission):
    """
    Custom permission class for analytics module
    """
    def has_permission(self, request, view):
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return False
        
        # Superusers have all permissions
        if request.user.is_superuser:
            return True
        
        # Get the analytics module
        try:
            module = Module.objects.get(code='analytics')
        except Module.DoesNotExist:
            return False
        
        # Check user module permissions
        user_permissions = UserModulePermission.objects.filter(
            user=request.user,
            module=module,
            is_allowed=True
        )
        if user_permissions.exists():
            return True
        
        # Check group module permissions
        user_groups = request.user.mes_groups.all().values_list('group_id', flat=True)
        group_permissions = GroupModulePermission.objects.filter(
            group_id__in=user_groups,
            module=module,
            is_allowed=True
        )
        if group_permissions.exists():
            return True
        
        return False
    
    def has_object_permission(self, request, view, obj):
        # Superusers have all permissions
        if request.user.is_superuser:
            return True
        
        # Dashboard owners have full permissions on their dashboards
        if hasattr(obj, 'owner') and obj.owner == request.user:
            return True
        
        # For charts, check dashboard ownership
        if hasattr(obj, 'dashboard') and obj.dashboard.owner == request.user:
            return True
        
        # Public dashboards and their charts are readable by all authenticated users
        if request.method in permissions.SAFE_METHODS:
            if hasattr(obj, 'is_public') and obj.is_public:
                return True
            if hasattr(obj, 'dashboard') and obj.dashboard.is_public:
                return True
        
        return False