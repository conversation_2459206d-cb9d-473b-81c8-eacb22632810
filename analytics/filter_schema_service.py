from typing import Dict, Any, Optional
from .models import Chart


class FilterSchemaService:
    """
    Service for providing filter schema documentation and examples
    """

    @staticmethod
    def get_filter_types_documentation() -> Dict[str, Dict[str, Any]]:
        """
        Get documentation for all supported filter types

        Returns:
            Dictionary with filter types and their documentation
        """
        return {
            'int': {
                'type': 'int',
                'description': 'Integer filter that supports exact match or range filtering',
                'example': {
                    'exact_match': [123],  # Single value for exact match
                    'range': [100, 200]    # Two values for range (min, max)
                }
            },
            'str': {
                'type': 'str',
                'description': 'String filter that supports exact match or pattern matching with % wildcard',
                'example': {
                    'exact_match': 'example_value',
                    'pattern_match': '%partial%'  # Using % as wildcard
                }
            },
            'date': {
                'type': 'date',
                'description': 'Date filter that supports exact match or range filtering',
                'example': {
                    'exact_match': ['2023-01-01'],  # Single date for exact match
                    'range': ['2023-01-01', '2023-12-31']  # Date range (start, end)
                }
            },
            'bool': {
                'type': 'bool',
                'description': 'Boolean filter that accepts 0 (false) or 1 (true)',
                'example': {
                    'true_value': '1',
                    'false_value': '0'
                }
            }
        }

    @staticmethod
    def get_filter_configuration_formats() -> Dict[str, Any]:
        """
        Get documentation for supported filter configuration formats

        Returns:
            Dictionary with configuration format documentation
        """
        return {
            'legacy_format': {
                'description': 'Simple format where each key maps to a data type string',
                'structure': 'Key-value pairs where value is a string indicating the data type',
                'supported_types': ['int', 'str', 'date', 'bool'],
                'default_clause': 'WHERE (all filters applied to WHERE clause)',
                'example': {
                    'column1': 'str',
                    'column2': 'int',
                    'column3': 'date',
                    'column4': 'bool'
                }
            },
            'enhanced_format': {
                'description': 'Advanced format that specifies both data type and SQL clause',
                'structure': 'Key-value pairs where value is an object with type and clause properties',
                'supported_types': ['int', 'str', 'date', 'bool'],
                'supported_clauses': ['where', 'having'],
                'default_clause': 'WHERE (when clause is not specified)',
                'example': {
                    'cl1': {
                        'type': 'int',
                        'clause': 'having'
                    },
                    'cl2': {
                        'type': 'str',
                        'clause': 'where'
                    },
                    'cl3': {
                        'type': 'date',
                        'clause': 'where'
                    }
                }
            },
            'mixed_format': {
                'description': 'Both formats can be used together in the same configuration',
                'example': {
                    'legacy_column': 'str',  # Legacy format - defaults to WHERE clause
                    'enhanced_column': {     # Enhanced format - explicit clause specification
                        'type': 'int',
                        'clause': 'having'
                    }
                }
            }
        }

    @staticmethod
    def get_chart_filter_schema(chart_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Get filter schema for a specific chart or general documentation if no chart_id is provided

        Args:
            chart_id: Optional ID of the chart to get filter schema for

        Returns:
            Dictionary with filter schema documentation
        """
        filter_types = FilterSchemaService.get_filter_types_documentation()
        configuration_formats = FilterSchemaService.get_filter_configuration_formats()

        result = {
            'filter_types': filter_types,
            'configuration_formats': configuration_formats
        }

        # If chart_id is provided, include the chart's specific filter configuration
        if chart_id:
            try:
                chart = Chart.objects.get(id=chart_id)
                result['chart_filters'] = chart.filter_config
                # Also include the normalized version for reference
                result['normalized_chart_filters'] = chart.get_normalized_filter_config()
            except Chart.DoesNotExist:
                pass

        return result
