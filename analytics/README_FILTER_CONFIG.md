# Filter Configuration Schema Validation

## Overview

The `filter_config` field in the `Chart` model is designed to store a JSON schema that defines the filters that can be applied to a chart's query. This document explains the validation rules and expected format for this field.

## Schema Format

The `filter_config` field must follow these rules:

1. It must be a JSON object (dictionary)
2. Each key in the object represents a filter field name
3. Each value must be a string representing the data type of the filter
4. The allowed data type values are strictly limited to:
   - `"int"` - for integer filters
   - `"str"` - for string/text filters
   - `"date"` - for date filters
   - `"bool"` - for boolean filters

## Example

A valid filter configuration might look like:

```json
{
  "user_id": "int",
  "username": "str",
  "created_at": "date",
  "is_active": "bool"
}
```

## Implementation

The validation is implemented in the `Chart` model's `clean()` method, which calls a private `_validate_filter_config()` method to perform the actual validation. This ensures that any attempt to save a chart with an invalid filter configuration will raise a `ValidationError`.

## Validation Rules

The validation process checks:

1. That the filter_config is a dictionary
2. That all values in the dictionary are strings
3. That all values are one of the allowed types: "int", "str", "date", "bool"

## Testing

Test cases for the filter configuration validation can be found in `tests/test_filter_config.py`. These tests verify that:

1. Valid filter configurations are accepted
2. Non-dictionary filter configurations are rejected
3. Filter configurations with non-string values are rejected
4. Filter configurations with string values not in the allowed set are rejected