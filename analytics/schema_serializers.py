from rest_framework import serializers


class FilterTypeSchema(serializers.Serializer):
    """Schema for filter type documentation"""
    type = serializers.ChoiceField(
        choices=['int', 'str', 'date', 'bool'],
        help_text="The data type of the filter"
    )
    description = serializers.Char<PERSON>ield(
        help_text="Description of the filter"
    )
    example = serializers.J<PERSON><PERSON>ield(
        help_text="Example usage of the filter"
    )


class FilterSchemaSerializer(serializers.Serializer):
    """Serializer for filter schema documentation"""
    filter_types = serializers.DictField(
        child=FilterTypeSchema(),
        help_text="Available filter types and their descriptions"
    )
    
    chart_filters = serializers.DictField(
        help_text="Filter configuration for a specific chart",
        required=False
    )


class FilterUsageExampleSerializer(serializers.Serializer):
    """Serializer for filter usage examples"""
    int_filter = serializers.J<PERSON><PERSON>ield(
        help_text="Example of integer filter usage",
        default=[1, 100]  # Range example
    )
    date_filter = serializers.J<PERSON><PERSON><PERSON>(
        help_text="Example of date filter usage",
        default=["2023-01-01", "2023-12-31"]  # Date range example
    )
    str_filter = serializers.Char<PERSON>ield(
        help_text="Example of string filter usage",
        default="example_value"
    )
    bool_filter = serializers.ChoiceField(
        help_text="Example of boolean filter usage",
        choices=['0', '1'],
        default='1'
    )