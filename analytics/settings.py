# ClickHouse connection settings
from django.conf import settings

# Default ClickHouse connection settings
CLICKHOUSE_HOST = getattr(settings, 'CLICKHOUSE_HOST', 'localhost')
CLICKHOUSE_PORT = getattr(settings, 'CLICKHOUSE_PORT', 9000)
CLICKHOUSE_USER = getattr(settings, 'CLICKHOUSE_USER', 'default')
CLICKHOUSE_PASSWORD = getattr(settings, 'CLICKHOUSE_PASSWORD', '')
CLICKHOUSE_DB = getattr(settings, 'CLICKHOUSE_DB', 'default')

# Chart settings
DEFAULT_CHART_COLORS = [
    '#5470c6', '#91cc75', '#fac858', '#ee6666',
    '#73c0de', '#3ba272', '#fc8452', '#9a60b4',
    '#ea7ccc'
]

# Dashboard settings
DEFAULT_DASHBOARD_LAYOUT = {
    'cols': 12,  # Number of columns in the grid
    'rowHeight': 30,  # Height of a row in pixels
    'margin': [10, 10],  # Margin between items [x, y]
    'containerPadding': [10, 10],  # Padding inside the container [x, y]
    'verticalCompact': True,  # Whether to compact the layout vertically
}