# SQL Query Validation for Analytics Charts

## Overview

This document explains the SQL query validation system implemented to prevent malicious or dangerous SQL operations in the analytics charts feature.

## Problem

The `Chart` model stores raw SQL queries that are executed against the ClickHouse database to retrieve data for visualization. This creates a potential security risk, as users with access to create or edit charts could potentially insert harmful SQL operations such as:

- DELETE operations that remove data
- DROP operations that delete tables
- ALTER operations that modify database structure
- INSERT/UPDATE operations that modify data
- Other administrative commands that could compromise the database

## Solution

We've implemented a multi-layered validation approach:

1. **Model-level validation**: The `Chart` model validates SQL queries before saving to the database
2. **Service-level validation**: The `ClickHouseService` validates queries again before execution
3. **Whitelist approach**: Only explicitly allowed SQL operations are permitted

## Implementation Details

### SQLQueryValidator

The core of the solution is the `SQLQueryValidator` class in `validators.py`, which:

- Maintains a list of dangerous SQL operations that are blocked
- Validates that queries only contain read-only operations (SELECT, etc.)
- Optionally supports a whitelist of allowed tables

### Validation Points

1. **When saving a chart**: The `Chart.clean()` and `Chart.save()` methods validate the query
2. **When executing a query**: The `ClickHouseService.execute_query()` method validates before execution

### Error Handling

- Model validation raises `ValidationError` to prevent saving invalid queries
- Service validation returns a DataFrame with error information instead of executing invalid queries
- The `ChartService` transforms error DataFrames into user-friendly chart error messages

## Usage

### Adding Allowed Tables

To restrict queries to specific tables, update the `ALLOWED_TABLES` list in `SQLQueryValidator`:

```python
# In validators.py
class SQLQueryValidator:
    # ... existing code ...
    
    # Update this list with your allowed tables
    ALLOWED_TABLES = ['events', 'metrics', 'logs']
```

### Customizing Validation Rules

To modify the validation rules, update the `DANGEROUS_OPERATIONS` or `ALLOWED_OPERATIONS` lists in `SQLQueryValidator`.

## Testing

Test cases are provided in `tests/test_validators.py` to verify that:

- Valid read-only queries pass validation
- Dangerous operations are blocked
- Mixed queries containing both valid and invalid operations are blocked
- Validation is case-insensitive

Run the tests with:

```bash
python manage.py test analytics.tests.test_validators
```

## Security Considerations

- This validation system provides a good first line of defense but is not foolproof
- It's recommended to also implement proper database user permissions as an additional security layer
- Regular security audits of the system are recommended
- Consider implementing query rate limiting to prevent DoS attacks

## Future Improvements

- Implement a more sophisticated SQL parser for better query analysis
- Add support for parameterized queries to prevent SQL injection
- Create a UI that helps users build safe queries instead of writing raw SQL
- Implement query execution timeouts to prevent long-running queries