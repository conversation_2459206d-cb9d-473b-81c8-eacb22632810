FROM python:3.11-alpine

# First set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBUG=False

# Set the working directory
WORKDIR /usr/src/app

# Install PostgreSQL libraries and build tools
RUN apk add --no-cache gcc musl-dev postgresql-dev libffi-dev

COPY ./requirements.txt .

ENV AWS_ANALYTICS_API_BASE_URL=https://5owof4lxqd.execute-api.ap-south-1.amazonaws.com/default/GenerateEmbedUrlForAnonymousUser
ENV AWS_ANALYTICS_DASHBOARD_ID=7c437caa-9972-4990-aeb8-c2ebf5a5d7f5

# Install Python dependencies
RUN pip install --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Make entrypoint script executable
RUN chmod 755 entrypoint.sh

# Expose Django application port
EXPOSE 8000

# Define the entrypoint script
ENTRYPOINT ["./entrypoint.sh"]
