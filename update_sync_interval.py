#!/usr/bin/env python3
"""
Update PeerDB Sync Interval Script
Changes sync interval for existing mirrors from 30 seconds to 5 seconds
"""

import psycopg2
import sys

# PeerDB connection parameters
PEERDB_CONFIG = {
    'host': 'localhost',
    'port': 9900,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'peerdb'
}

def connect_to_peerdb():
    """Connect to PeerDB"""
    try:
        conn = psycopg2.connect(**PEERDB_CONFIG)
        conn.autocommit = True
        print("✅ Connected to PeerDB successfully")
        return conn
    except Exception as e:
        print(f"❌ Failed to connect to PeerDB: {e}")
        return None

def execute_sql(conn, sql: str, description: str) -> bool:
    """Execute SQL command and handle errors"""
    try:
        cursor = conn.cursor()
        cursor.execute(sql)
        cursor.close()
        print(f"✅ {description}")
        return True
    except Exception as e:
        print(f"❌ Failed to {description.lower()}: {e}")
        return False

def get_current_mirrors(conn):
    """Get list of current mirrors"""
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM peers WHERE type = 3 ORDER BY name;")  # type 3 = PostgreSQL
        pg_peers = [row[0] for row in cursor.fetchall()]
        
        cursor.execute("SELECT name FROM peers WHERE type = 8 ORDER BY name;")  # type 8 = ClickHouse  
        ch_peers = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 Found PostgreSQL peers: {pg_peers}")
        print(f"📋 Found ClickHouse peers: {ch_peers}")
        
        cursor.close()
        return pg_peers, ch_peers
    except Exception as e:
        print(f"❌ Failed to get mirrors: {e}")
        return [], []

def drop_existing_mirrors(conn):
    """Drop existing mirrors so we can recreate them with new settings"""
    mirrors_to_drop = ['mes_entities_mirror', 'mes_events_mirror']
    
    for mirror in mirrors_to_drop:
        sql = f"DROP MIRROR IF EXISTS {mirror};"
        execute_sql(conn, sql, f"Drop mirror '{mirror}'")

def recreate_entities_mirror_with_new_interval(conn):
    """Recreate entities mirror with 5-second sync interval"""
    
    # Tables with updated_at column
    tables_with_updates = [
        "mes_commodities", "mes_components", "mes_products", "mes_product_parts",
        "mes_product_components", "mes_scanners", "mes_areas", "mes_assembly_lines", 
        "mes_factory", "mes_process_blocks", "mes_form_config", "mes_routing",
        "mes_routing_product", "mes_routing_execution", "mes_bom_header", "mes_bom_item",
        "mes_work_orders", "mes_users", "mes_modules", "mes_groups", "mes_access_scopes",
        "mes_user_mes_groups", "mes_group_module_permissions", "mes_group_object_permissions",
        "mes_user_module_permissions", "mes_user_object_permissions", "mes_reference_categories",
        "mes_reference_values", "mes_master_program", "mes_master_program_product_param",
        "mes_sop", "mes_analytics_dashboards", "mes_analytics_charts", 
        "mes_analytics_chart_groups", "mes_aoi_daily_yield", "mes_aoi_rejection"
    ]
    
    table_mappings = []
    for table in tables_with_updates:
        table_mappings.append(f"public.{table}:{table}")
    
    mappings_str = ",\n      ".join(table_mappings)
    
    sql = f"""
    CREATE MIRROR IF NOT EXISTS mes_entities_mirror
    FROM postgres_mes_source TO clickhouse_mes_target
    WITH TABLE MAPPING (
      {mappings_str}
    )
    WITH (
      do_initial_copy = false,
      max_batch_size = 10000,
      sync_interval = 5,
      snapshot_num_rows_per_partition = 500000,
      snapshot_max_parallel_workers = 4,
      snapshot_num_tables_in_parallel = 4,
      soft_delete = true,
      synced_at_col_name = '_PEERDB_SYNCED_AT',
      soft_delete_col_name = '_PEERDB_IS_DELETED'
    );
    """
    return execute_sql(conn, sql, "Recreate entities mirror with 5-second sync interval")

def recreate_events_mirror_with_new_interval(conn):
    """Recreate events mirror with 5-second sync interval"""
    
    # Tables without updated_at column
    append_only_tables = [
        "mes_manufacturing_events", "mes_event_request_logs", 
        "mes_fifo_violation_logs", "mes_cache"
    ]
    
    table_mappings = []
    for table in append_only_tables:
        table_mappings.append(f"public.{table}:{table}")
    
    mappings_str = ",\n      ".join(table_mappings)
    
    sql = f"""
    CREATE MIRROR IF NOT EXISTS mes_events_mirror
    FROM postgres_mes_source TO clickhouse_mes_target
    WITH TABLE MAPPING (
      {mappings_str}
    )
    WITH (
      do_initial_copy = false,
      max_batch_size = 10000,
      sync_interval = 5,
      snapshot_num_rows_per_partition = 500000,
      snapshot_max_parallel_workers = 4,
      snapshot_num_tables_in_parallel = 4,
      soft_delete = false,
      synced_at_col_name = '_PEERDB_SYNCED_AT'
    );
    """
    return execute_sql(conn, sql, "Recreate events mirror with 5-second sync interval")

def main():
    print("🔄 PeerDB Sync Interval Update")
    print("==============================")
    print("📊 Updating sync interval from 30 seconds to 5 seconds")
    
    # Connect to PeerDB
    print("\n📡 Connecting to PeerDB...")
    conn = connect_to_peerdb()
    if not conn:
        sys.exit(1)
    
    # Get current mirrors
    print("\n📋 Checking current configuration...")
    pg_peers, ch_peers = get_current_mirrors(conn)
    
    if 'postgres_mes_source' not in pg_peers or 'clickhouse_mes_target' not in ch_peers:
        print("❌ Required peers not found. Please run configure_peerdb_programmatically.py first.")
        sys.exit(1)
    
    success_count = 0
    total_steps = 4
    
    # Step 1: Drop existing mirrors
    print("\n🗑️  Step 1: Dropping existing mirrors...")
    drop_existing_mirrors(conn)
    success_count += 1
    
    # Wait for cleanup
    print("\n⏳ Waiting for cleanup...")
    import time
    time.sleep(5)
    
    # Step 2: Recreate entities mirror
    print("\n🔄 Step 2: Recreating entities mirror with 5-second interval...")
    if recreate_entities_mirror_with_new_interval(conn):
        success_count += 1
    
    # Step 3: Recreate events mirror
    print("\n📝 Step 3: Recreating events mirror with 5-second interval...")
    if recreate_events_mirror_with_new_interval(conn):
        success_count += 1
    
    # Step 4: Verify
    print("\n🔍 Step 4: Verifying new configuration...")
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM peers ORDER BY name;")
        peers = cursor.fetchall()
        print(f"✅ Verified {len(peers)} peers are still active")
        success_count += 1
        cursor.close()
    except Exception as e:
        print(f"❌ Verification failed: {e}")
    
    # Close connection
    conn.close()
    
    # Summary
    print(f"\n{'='*50}")
    print(f"📊 Update Summary: {success_count}/{total_steps} steps completed")
    
    if success_count == total_steps:
        print("🎉 SUCCESS: Sync interval updated to 5 seconds!")
        print("\n📈 Changes Applied:")
        print("- ✅ Entities mirror: Now syncs every 5 seconds")
        print("- ✅ Events mirror: Now syncs every 5 seconds")
        print("- ✅ Initial copy disabled (data already synced)")
        print("- ✅ All other settings preserved")
        print("\n🔧 Monitor the new sync frequency at: http://localhost:3000")
    else:
        print("⚠️  PARTIAL SUCCESS: Some steps failed. Check the errors above.")

if __name__ == "__main__":
    main()
