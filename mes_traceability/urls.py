from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# Configure Swagger/OpenAPI documentation
schema_view = get_schema_view(
    openapi.Info(
        title="MES Analytics API",
        default_version='v1',
        description="API documentation for MES Analytics module",
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
)

urlpatterns = [
    path('mes_trace/admin/', admin.site.urls),
    path('mes_trace/auth/api/', include('authentication.urls')),
    path('mes_trace/workflow/api/', include('workflow_config.urls')),
    path('mes_trace/catalog/api/', include('catalog.urls')),
    path('mes_trace/operation/api/', include('operation.urls')),
    path('mes_trace/analytics/api/', include('analytics.urls')),
    path('mes_trace/bom/api/', include('bom.urls')),
    path('mes_trace/production-metrics/api/', include('production_metrics.urls')),

    # Swagger/OpenAPI documentation URLs
    re_path(r'^mes_trace/swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    re_path(r'^mes_trace/swagger/$', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    re_path(r'^mes_trace/redoc/$', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
]
