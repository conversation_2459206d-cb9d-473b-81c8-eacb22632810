import os
import json
from pathlib import Path
from datetime import timedelta
from dotenv import load_dotenv

load_dotenv()

# Logging is configured to use console only, no file logging

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-87jzr$8k-)nb@r7+&jz#+cob(6yh@2_g(zy79hs=b9u!-jmb(@'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DJANGO_DEBUG', 'True') == 'True'

ALLOWED_HOSTS = os.getenv('DJANGO_ALLOWED_HOSTS', '*').split(',')

# CSRF settings
CSRF_TRUSTED_ORIGINS = [
    "https://exicom.veridianmfg.com",
    "https://veridianmfg.com",
    "https://www.veridianmfg.com",
    "https://backend.smartmfg.in",
    "https://demo.smartmfg.in",
]
CSRF_COOKIE_SECURE = True

# Application definition
INSTALLED_APPS = [
    # Django apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Local apps
    'authentication.apps.AuthenticationConfig',  # Must come before admin

    # Third party apps
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
    'django_filters',
    'clickhouse_backend',
    'drf_yasg',

    # Project apps
    'core',
    'operation',
    'catalog',
    'workflow_config',
    'analytics',
    'bom',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'operation.middleware.request_logging.EventRequestLoggingMiddleware',
]

ROOT_URLCONF = 'mes_traceability.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'mes_traceability.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('POSTGRES_DB', 'mes_db'),
        'USER': os.getenv('POSTGRES_USER', 'postgres'),
        'PASSWORD': os.getenv('POSTGRES_PASSWORD', 'postgres'),
        'HOST': os.getenv('POSTGRES_HOST', 'localhost'),
        # 'HOST': 'host.docker.internal',
        'PORT': os.getenv('POSTGRES_PORT', '5432'),
    },
    'clickhouse': {
        'ENGINE': 'clickhouse_backend.backend',
        'NAME': os.getenv('CLICKHOUSE_DB', 'default'),
        'USER': os.getenv('CLICKHOUSE_USER', 'default'),
        'PASSWORD': os.getenv('CLICKHOUSE_PASSWORD', ''),
        'HOST': os.getenv('CLICKHOUSE_HOST', 'localhost'),
        'PORT': os.getenv('CLICKHOUSE_PORT', '9000'),
        'OPTIONS': {
            'connect_timeout': 5,
            'send_receive_timeout': 5,
            'sync_request_timeout': 5,
        },
    }
}

# cache settings
# `python3 manage.py createcachetable` creates cache table (as defined in LOCATION)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache', # Use main database as cache storage
        'LOCATION': 'mes_cache',  # Name of the table to store the cache
        'TIMEOUT': 300,  # Cache timeout in seconds (optional, defaults to 300)
        'OPTIONS': {
            'MAX_ENTRIES': 10_000,  # Limit for number of cache entries
            'CULL_FREQUENCY': 3, # Fraction of entries to remove when max is reached
        }
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'Asia/Kolkata'
USE_I18N = True
USE_L10N = True
USE_TZ = True


# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom user model
AUTH_USER_MODEL = 'authentication.User'

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ),
}

# JWT settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
    'ROTATE_REFRESH_TOKENS': True,  # Disable token rotation for faster processing
    'BLACKLIST_AFTER_ROTATION': True,  # Disable blacklisting since rotation is disabled
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    # 'UPDATE_LAST_LOGIN': False,  # Disable last login update for faster processing
}

# CORS settings
#CORS_ALLOW_ALL_ORIGINS = DEBUG
# CORS_ALLOWED_ORIGINS = os.getenv('CORS_ALLOWED_ORIGINS', '').split(',')
# if not DEBUG and not CORS_ALLOWED_ORIGINS[0]:
#     raise ValueError("CORS_ALLOWED_ORIGINS must be set in production")
#CORS_ALLOW_HEADERS = '*'

# CSRF settings
# Parse CSRF_TRUSTED_ORIGINS as a JSON array to ensure it is correctly interpreted
CSRF_TRUSTED_ORIGINS = json.loads(os.getenv('CSRF_TRUSTED_ORIGINS', '[]'))

# CORS settings
CORS_ALLOW_ALL_ORIGINS = DEBUG  # Allow all origins in debug mode (unsafe for production)
CORS_ALLOWED_ORIGINS = json.loads(os.getenv('CORS_ALLOWED_ORIGINS', '[]'))  # Parse as a list
CORS_ALLOW_HEADERS = json.loads(os.getenv('CORS_ALLOW_HEADERS', '[]'))  # Parse as a list











SUPERADMIN_USERNAME = os.getenv('SUPERADMIN_USERNAME', 'admin')
SUPERADMIN_EMAIL = os.getenv('SUPERADMIN_EMAIL', '<EMAIL>')
SUPERADMIN_PASSWORD = os.getenv('SUPERADMIN_PASSWORD', 'admin@123')

AWS_ANALYTICS_API_BASE_URL = os.getenv('AWS_ANALYTICS_API_BASE_URL')
AWS_ANALYTICS_DASHBOARD_ID = os.getenv('AWS_ANALYTICS_DASHBOARD_ID')

# ClickHouse settings
CLICKHOUSE_HOST = os.getenv('CLICKHOUSE_HOST', 'localhost')
CLICKHOUSE_PORT = int(os.getenv('CLICKHOUSE_PORT', '9000'))
CLICKHOUSE_USER = os.getenv('CLICKHOUSE_USER', 'default')
CLICKHOUSE_PASSWORD = os.getenv('CLICKHOUSE_PASSWORD', '')
CLICKHOUSE_DB = os.getenv('CLICKHOUSE_DB', 'default')

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'standard': {
            'format': '[{asctime}] {levelname} {name}: {message}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
        },
        # File handler removed to disable file logging
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'fifo_validation': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
