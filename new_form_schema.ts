import { FieldSchema, FormSchema, Workflow } from "./types";

export const sampleMesFormSchema: FormSchema = {
  id: "start-production-job",
  title: "Start Production Job",
  fields: [
    {
      id: "job_id",
      name: "job_id",
      label: "Job ID",
      type: "text",
      required: true,
      validation: {
        pattern: "^[A-Z0-9_-]{6,}$",
        message: "Job ID must be at least 6 characters and contain only A-Z, 0-9, _ or -",
      },
      layout: { md: 6 },
    },
    {
      id: "operator_name",
      name: "operator_name",
      label: "Operator Name",
      type: "text",
      required: true,
      layout: { md: 6 },
    },
    {
      id: "shift",
      name: "shift",
      label: "Shift",
      type: "select",
      required: true,
      options: [
        { value: "A", label: "Shift A" },
        { value: "B", label: "Shift B" },
        { value: "C", label: "Shift C", hideFields: ["supervisor_override"] },
      ],
      layout: { md: 4 },
    },
    {
      id: "supervisor_override",
      name: "supervisor_override",
      label: "Supervisor Override",
      type: "checkbox",
      defaultValue: false,
      layout: { md: 4 },
    },
    {
      id: "machine_id",
      name: "machine_id",
      label: "Machine",
      type: "select",
      required: true,
      options:[
        "Machine 1",
        "Machine 2",
        "Machine 3",
      ],
      layout: { md: 4 },
    },
    {
      id: "start_time",
      name: "start_time",
      label: "Start Time",
      type: "datetime",
      required: true,
      defaultValue: new Date(),
      layout: { md: 6 },
    },
    {
      id: "estimated_output",
      name: "estimated_output",
      label: "Estimated Output (Units)",
      type: "number",
      required: true,
      condition: {
        field: "supervisor_override",
        operator: "==",
        value: false,
      },
      layout: { md: 6 },
    },
    {
      id: "material_code",
      name: "material_code",
      label: "Material Code",
      type: "text",
      dependencies: {
        field: "machine_id",
        operator: "!=",
        value: "",
        validations: [
          {
            type: "regexMatch",
            config: { pattern: "MAT-[0-9]{4}" },
            message: "Material code must follow MAT-XXXX format",
          },
        ],
      },
      layout: { md: 6 },
    },
    {
      id: "confirm_conditions",
      name: "confirm_conditions",
      label: "I confirm all safety conditions are met.",
      type: "checkbox",
      required: true,
      layout: { md: 12 },
    },
  ],
};

export const sampleMesWorkflows: Workflow[] = [
  // Auto-fill material code based on machine
  {
    id: "populate-material-code",
    formId: "start-production-job",
    name: "Populate Material Code",
    trigger: {
      type: "field",
      event: "onChange",
      fieldName: "machine_id",
    },
    actions: [
      {
        type: "api_call",
        config: {
          url: "https://mes.api/fetch-material?machine_id={{machine_id}}",
          method: "GET",
          mapResponseToField: "material_code",
        },
      },
    ],
  },

  // Validate high output request
  {
    id: "warn-high-output",
    formId: "start-production-job",
    name: "Warn High Output",
    trigger: {
      type: "field",
      event: "onChange",
      fieldName: "estimated_output",
    },
    conditions: [
      {
        field: "estimated_output",
        operator: ">",
        value: 1000,
      },
    ],
    actions: [
      {
        type: "script",
        config: {
          code: alert('Warning: Estimated output exceeds recommended limit!');,
        }
      },
    ],
  },

  // On submit — send job start event
  {
    id: "start-job-submission",
    formId: "start-production-job",
    name: "Submit Start Job",
    trigger: {
      type: "form",
      event: "on_submit",
    },
    actions: [
      {
        type: "api_call",
        config: {
          url: "https://mes.api/jobs/start",
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: {
            job_id: "{{job_id}}",
            operator: "{{operator_name}}",
            machine: "{{machine_id}}",
            start_time: "{{start_time}}",
          },
        },
      },
    ],
  },

  // After submit — clear sensitive fields
  {
    id: "clear-sensitive-after-submit",
    formId: "start-production-job",
    name: "Clear Sensitive Info",
    trigger: {
      type: "form",
      event: "after_submit",
    },
    actions: [
      {
        type: "update_field",
        config: {
          field: "operator_name",
          value: "",
        },
      },
    ],
  },
];