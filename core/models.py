from django.db import models

class BaseModel(models.Model):
    """
    Abstract base model with common fields
    """
    id = models.AutoField(primary_key=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True


class BaseEntity(BaseModel):
    """
    Abstract base entity model providing common fields for entities.
    This includes fields for name, code, description, and active status,
    which can be reused in multiple entity models.
    """
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True
