import pandas as pd
from typing import Any, Optional


def get_column_value(row: pd.Series, column_name: str, default: Optional[Any] = None) -> Any:
    """
    Helper method to get column value while handling whitespace in column names.

    Args:
        row: DataFrame row
        column_name: The exact column name to look for
        default: Default value if column not found

    Returns:
        The value from the column or default if not found
    """
    # First try exact match
    if column_name in row:
        return row.get(column_name, default)

    # If exact match fails, try case-insensitive match with whitespace normalization
    column_name_normalized = column_name.strip().lower()
    for col in row.index:
        if isinstance(col, str) and col.strip().lower() == column_name_normalized:
            return row.get(col, default)

    return default
