from typing import Dict
import pandas as pd
from django.db import transaction
from django.core.exceptions import ValidationError
from datetime import datetime
from ..validators import ExcelValidator
from ..utils import get_column_value


class ExcelImportService:
    def __init__(self, validator: ExcelValidator):
        self.validator = validator

    def process_file(self, file) -> Dict:
        # Validate file
        is_valid, error = self.validator.validate_file(file)
        if not is_valid:
            return {'success': False, 'error': error}

        results = {'success': True, 'sheets': {}}

        try:
            # Read Excel file without converting dates
            excel_file = pd.ExcelFile(file)
            print(excel_file.sheet_names)
            print(excel_file)

            # Process each sheet
            for sheet_name, config in self.validator.config.SHEET_CONFIGS.items():
                try:
                    if sheet_name not in excel_file.sheet_names:
                        results['sheets'][sheet_name] = {
                            'success': False,
                            'error': f'Sheet "{sheet_name}" not found in Excel file'
                        }
                        continue

                    df = pd.read_excel(excel_file, sheet_name=sheet_name)

                    # Validate sheet
                    is_valid, errors = self.validator.validate_sheet(df, config)
                    if not is_valid:
                        results['sheets'][sheet_name] = {
                            'success': False,
                            'errors': errors
                        }
                        continue

                    # Import sheet data
                    with transaction.atomic():
                        imported_count = self._import_sheet_data(df, config)

                    results['sheets'][sheet_name] = {
                        'success': True,
                        'rows_imported': imported_count
                    }

                except Exception as e:
                    results['sheets'][sheet_name] = {
                        'success': False,
                        'error': str(e)
                    }

        except Exception as e:
            return {'success': False, 'error': f"Error processing Excel file: {str(e)}"}

        # Validate results
        is_valid = self.validator.validate_results(results)
        if not is_valid:
            results['success'] = False
        return results

    def _import_sheet_data(self, df: pd.DataFrame, config):
        records = []
        for _, row in df.iterrows():
            record = self._transform_record(row, config)
            if record:
                records.append(config.model_class(**record))

        if records:
            config.model_class.objects.bulk_create(records)
        return len(records)

    def _transform_record(self, row: pd.Series, config) -> Dict:
        transformed = {}

        # Convert date - pandas has already converted it to datetime
        date_val = get_column_value(row, 'Date')
        if pd.notna(date_val):
            transformed['date'] = date_val.date() if isinstance(date_val, (datetime, pd.Timestamp)) else None

        if transformed.get('date') is None:
            return None

        if config.name == 'AOI Daily Yield':
            fpy_str = str(get_column_value(row, 'FPY', '0'))
            rejection_str = str(get_column_value(row, 'Rejection %', '0'))
            transformed.update({
                'sr_no': int(get_column_value(row, 'S.No', 0)),
                'model': get_column_value(row, 'Model'),
                'total_cards_produced': int(get_column_value(row, 'Total Cards Produced', 0)),
                'total_fail_cards': int(get_column_value(row, 'Total Fail Cards', 0)),
                'total_real_faults': int(get_column_value(row, 'Total real faults card wise', 0)),
                'total_pass_cards': int(get_column_value(row, 'Total Pass card', 0)),
                'rejection_percentage': float(rejection_str.rstrip('%') if '%' in rejection_str else rejection_str),
                'fpy_percentage': float(fpy_str.rstrip('%') if '%' in fpy_str else fpy_str),
            })
        elif config.name == 'AOI Rejection':
            transformed.update({
                'sr_no': int(get_column_value(row, 'S.No', 0)),
                'model': get_column_value(row, 'MODEL NAME'),
                'serial_number': get_column_value(row, 'SERIAL NUMBER'),
                'component_part_no': get_column_value(row, "COMP'S PART NUMBER"),
                'location': get_column_value(row, 'LOCATION'),
                'defect': get_column_value(row, 'DEFECT'),
            })

        return transformed
