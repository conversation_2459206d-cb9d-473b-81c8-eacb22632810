from typing import <PERSON><PERSON>, List, Dict
import pandas as pd
from datetime import datetime
from .utils import get_column_value


class ExcelValidator:
    def __init__(self, config):
        self.config = config

    def validate_file(self, file) -> Tuple[bool, str]:
        # Check file size
        if file.size > self.config.MAX_FILE_SIZE_MB * 1024 * 1024:
            return False, f"File size exceeds {self.config.MAX_FILE_SIZE_MB}MB limit"

        # Check file extension
        if not file.name.endswith(self.config.ALLOWED_EXTENSIONS):
            return False, "Invalid file format"

        return True, ""

    def validate_sheet(self, df: pd.DataFrame, sheet_config) -> Tuple[bool, List[str]]:
        errors = []

        # Strip whitespace from column names for comparison
        df_columns_stripped = {col.strip() if isinstance(col, str) else col for col in df.columns}
        required_columns_stripped = {col.strip() for col in sheet_config.required_columns}
        non_required_columns_stripped = {col.strip() for col in sheet_config.non_required_columns}

        # Check required columns
        missing_cols = required_columns_stripped - df_columns_stripped
        if missing_cols:
            errors.append(f"Missing columns: {', '.join(missing_cols)}")

        # Check non-required columns
        extra_cols = df_columns_stripped - required_columns_stripped - non_required_columns_stripped
        if extra_cols:
            errors.append(f"Extra columns: {', '.join(extra_cols)}")

        # Check row count
        if len(df) > sheet_config.max_rows:
            errors.append(f"Exceeds maximum rows limit of {sheet_config.max_rows}")

        # Validate data types and formats
        try:
            # Read the Excel file without converting dates
            for idx, row in df.iterrows():
                # Date validation
                try:
                    date_val = get_column_value(row, 'Date')
                    if pd.isna(date_val):
                        errors.append(f"Empty date in row {idx + 2}")
                        continue

                    # No need to validate format since pandas already converted it to datetime
                    if not isinstance(date_val, (datetime, pd.Timestamp)):
                        errors.append(f"Invalid date in row {idx + 2}")

                except KeyError:
                    errors.append(f"Missing Date column in row {idx + 2}")
                except Exception as e:
                    errors.append(f"Error processing date in row {idx + 2}: {str(e)}")

        except Exception as e:
            errors.append(f"Data validation error: {str(e)}")

        return len(errors) == 0, errors

    def validate_results(self, results):
        return True if any(result['success'] is True for result in results['sheets'].values()) else False
