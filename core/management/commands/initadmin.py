from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from authentication.models import User


class Command(BaseCommand):
    def handle(self, *args, **options):
        username = getattr(settings, 'SUPERADMIN_USERNAME')
        try:
            if User.objects.filter(username=username).count() == 0:
                User.objects.create_superuser(
                    username=username,
                    email=getattr(settings, 'SUPERADMIN_EMAIL'),
                    password=getattr(settings, 'SUPERADMIN_PASSWORD'),
                )
                print(f"super user created for: {username}")
            else:
                print(f"{username} user already exist.")
        except Exception as e:
            print(e)
