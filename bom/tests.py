"""
Tests for the BOM app.

This module contains tests for the BOM models, services, and API endpoints.
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from catalog.models import Product
from .models import <PERSON><PERSON><PERSON>eader, BOMItem
from .services import BOMService
from .views import BOMHeaderViewSet

User = get_user_model()


class BOMModelTests(TestCase):
    """
    Tests for the BOM models.
    """

    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )

        # Create test products
        self.product = Product.objects.create(
            name='Test Product',
            code='TEST-PROD',
            description='Test product description'
        )

        self.component1 = Product.objects.create(
            name='Component 1',
            code='COMP-1',
            description='Test component 1'
        )

        self.component2 = Product.objects.create(
            name='Component 2',
            code='COMP-2',
            description='Test component 2'
        )

        self.subcomponent = Product.objects.create(
            name='Subcomponent',
            code='SUB-COMP',
            description='Test subcomponent'
        )

        # Create a BOM header
        self.bom_header = BOMHeader.objects.create(
            name='Test BOM',
            code='TEST-BOM',
            product=self.product,
            version='1.0',
            status='active',
            created_by=self.user
        )

    def test_bom_header_creation(self):
        """Test BOM header creation"""
        self.assertEqual(self.bom_header.name, 'Test BOM')
        self.assertEqual(self.bom_header.product, self.product)
        self.assertEqual(self.bom_header.version, '1.0')
        self.assertEqual(self.bom_header.status, 'active')
        self.assertEqual(self.bom_header.created_by, self.user)

    def test_bom_item_creation(self):
        """Test BOM item creation"""
        # Create top-level items
        item1 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component1,
            quantity=2,
            position='POS1',
            item_type='assembly'
        )

        item2 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component2,
            quantity=1,
            position='POS2',
            item_type='component'
        )

        # Create a child item
        subitem = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.subcomponent,
            parent_item=item1,
            quantity=3,
            position='SUBPOS',
            item_type='component'
        )

        # Verify the items were created correctly
        self.assertEqual(item1.component, self.component1)
        self.assertEqual(item1.quantity, 2)
        self.assertIsNone(item1.parent_item)

        self.assertEqual(subitem.component, self.subcomponent)
        self.assertEqual(subitem.parent_item, item1)
        self.assertEqual(subitem.quantity, 3)

    def test_circular_reference_prevention(self):
        """Test that circular references are prevented"""
        # Create items with a potential circular reference
        item1 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component1,
            quantity=1,
            item_type='assembly'
        )

        item2 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component2,
            parent_item=item1,
            quantity=1,
            item_type='assembly'
        )

        # Try to create a circular reference
        item1.parent_item = item2

        # This should raise a ValidationError
        with self.assertRaises(Exception):
            item1.save()


class BOMServiceTests(TestCase):
    """
    Tests for the BOM service functions.
    """

    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )

        # Create test products
        self.product = Product.objects.create(
            name='Test Product',
            code='TEST-PROD',
            description='Test product description'
        )

        self.component1 = Product.objects.create(
            name='Component 1',
            code='COMP-1',
            description='Test component 1'
        )

        self.component2 = Product.objects.create(
            name='Component 2',
            code='COMP-2',
            description='Test component 2'
        )

        self.subcomponent1 = Product.objects.create(
            name='Subcomponent 1',
            code='SUB-COMP-1',
            description='Test subcomponent 1'
        )

        self.subcomponent2 = Product.objects.create(
            name='Subcomponent 2',
            code='SUB-COMP-2',
            description='Test subcomponent 2'
        )

        # Create a BOM header
        self.bom_header = BOMHeader.objects.create(
            name='Test BOM',
            code='TEST-BOM',
            product=self.product,
            version='1.0',
            status='active',
            created_by=self.user
        )

        # Create BOM items
        self.item1 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component1,
            quantity=2,
            position='POS1',
            item_type='assembly'
        )

        self.item2 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component2,
            quantity=1,
            position='POS2',
            item_type='component'
        )

        self.subitem1 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.subcomponent1,
            parent_item=self.item1,
            quantity=3,
            position='SUBPOS1',
            item_type='component'
        )

        self.subitem2 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.subcomponent2,
            parent_item=self.item1,
            quantity=4,
            position='SUBPOS2',
            item_type='component'
        )

    def test_get_complete_bom(self):
        """Test getting the complete BOM structure"""
        complete_bom = BOMService.get_complete_bom(self.bom_header.id)

        # Verify the structure
        self.assertEqual(len(complete_bom), 2)  # Two top-level items

        # Find the assembly item
        assembly_item = next((item for item in complete_bom if item['component'].code == 'COMP-1'), None)
        self.assertIsNotNone(assembly_item)
        self.assertEqual(len(assembly_item['children']), 2)  # Two children

    def test_get_flat_bom(self):
        """Test getting a flattened BOM"""
        flat_bom = BOMService.get_flat_bom(self.bom_header.id)

        # Verify the structure
        self.assertEqual(len(flat_bom), 4)  # Four items total

    def test_get_bom_levels(self):
        """Test getting BOM items by level"""
        levels = BOMService.get_bom_levels(self.bom_header.id)

        # Verify the levels
        self.assertEqual(len(levels), 2)  # Two levels
        self.assertEqual(len(levels[0]), 2)  # Two items at level 0
        self.assertEqual(len(levels[1]), 2)  # Two items at level 1

    def test_find_component_usage(self):
        """Test finding component usage"""
        usage = BOMService.find_component_usage(self.component1.id)

        # Verify the usage
        self.assertEqual(len(usage), 1)  # Used in one BOM
        self.assertEqual(usage[0]['bom_header_id'], self.bom_header.id)

    def test_validate_bom_structure(self):
        """Test validating BOM structure"""
        validation = BOMService.validate_bom_structure(self.bom_header.id)

        # Verify the validation result
        self.assertTrue(validation['valid'])

    def test_get_product_boms(self):
        """Test getting BOMs by product"""
        # Create a test BOM structure
        self.test_get_complete_bom()

        # Test getting BOMs by product ID
        product_boms = BOMService.get_product_boms(str(self.product.id))

        # Verify the structure
        self.assertEqual(product_boms['product']['id'], self.product.id)
        self.assertEqual(product_boms['bom_count'], 1)
        self.assertEqual(len(product_boms['boms']), 1)
        self.assertEqual(product_boms['boms'][0]['id'], self.bom_header.id)

        # Test getting BOMs by product code
        product_boms = BOMService.get_product_boms(self.product.code)

        # Verify the structure
        self.assertEqual(product_boms['product']['code'], self.product.code)
        self.assertEqual(product_boms['bom_count'], 1)

        # Test filtering by status
        product_boms = BOMService.get_product_boms(str(self.product.id), 'active')
        self.assertEqual(product_boms['bom_count'], 1)

        product_boms = BOMService.get_product_boms(str(self.product.id), 'draft')
        self.assertEqual(product_boms['bom_count'], 0)

        # Test with non-existent product
        product_boms = BOMService.get_product_boms('999999')
        self.assertIn('error', product_boms)
        self.assertIsNone(product_boms['product'])


class BOMAPITests(APITestCase):
    """
    Tests for the BOM API endpoints.
    """

    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )

        # Create test products
        self.product = Product.objects.create(
            name='Test Product',
            code='TEST-PROD',
            description='Test product description'
        )

        self.component1 = Product.objects.create(
            name='Component 1',
            code='COMP-1',
            description='Test component 1'
        )

        self.component2 = Product.objects.create(
            name='Component 2',
            code='COMP-2',
            description='Test component 2'
        )

        # Create a BOM header
        self.bom_header = BOMHeader.objects.create(
            name='Test BOM',
            code='TEST-BOM',
            product=self.product,
            version='1.0',
            status='active',
            created_by=self.user
        )

        # Create BOM items
        self.item1 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component1,
            quantity=2,
            position='POS1',
            item_type='assembly'
        )

        self.item2 = BOMItem.objects.create(
            bom_header=self.bom_header,
            component=self.component2,
            quantity=1,
            position='POS2',
            item_type='component'
        )

        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

    def test_product_boms_endpoint(self):
        """Test the product_boms endpoint"""
        # Test with product ID
        url = reverse('bomheader-product-boms')
        response = self.client.get(f"{url}?product_id={self.product.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['product']['id'], self.product.id)
        self.assertEqual(response.data['bom_count'], 1)

        # Test with product code
        response = self.client.get(f"{url}?product_code={self.product.code}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['product']['code'], self.product.code)

        # Test with status filter
        response = self.client.get(f"{url}?product_id={self.product.id}&status=active")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['bom_count'], 1)

        response = self.client.get(f"{url}?product_id={self.product.id}&status=draft")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['bom_count'], 0)

        # Test with missing parameters
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test with non-existent product
        response = self.client.get(f"{url}?product_id=999999")

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)
