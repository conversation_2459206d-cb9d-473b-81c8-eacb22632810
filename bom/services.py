"""
Services for BOM operations and utilities.

This module provides service functions for working with Bill of Materials (BOM)
structures, including traversal, validation, and transformation operations.
"""
from typing import Dict, List, Any, Optional
from django.db.models import Q
from catalog.models import Product
from .models import B<PERSON>Header, BOMItem


class BOMService:
    """
    Service class for BOM operations.

    Provides methods for traversing, validating, and manipulating BOM structures.
    """

    @staticmethod
    def get_complete_bom(bom_header_id: int) -> List[Dict[str, Any]]:
        """
        Get the complete BOM structure for a given BOM header.

        Args:
            bom_header_id: The ID of the BOM header

        Returns:
            A list of dictionaries representing the top-level items and their children
        """
        try:
            bom_header = BOMHeader.objects.get(id=bom_header_id)
            return bom_header.get_complete_bom()
        except BOMHeader.DoesNotExist:
            return []

    @staticmethod
    def get_children(parent_item_id: int, bom_header_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get all children of a specific BOM item.

        Args:
            parent_item_id: The ID of the parent BOM item
            bom_header_id: Optional BOM header ID to restrict the query

        Returns:
            A list of dictionaries representing the children and their children
        """
        try:
            # Get the parent item
            query = BOMItem.objects.filter(id=parent_item_id)
            if bom_header_id:
                query = query.filter(bom_header_id=bom_header_id)

            parent_item = query.first()

            if parent_item:
                # Get all children recursively
                return parent_item.get_children()
            return []
        except Exception:
            return []

    @staticmethod
    def get_flat_bom(bom_header_id: int) -> List[Dict[str, Any]]:
        """
        Get a flattened list of all items in the BOM.

        Args:
            bom_header_id: The ID of the BOM header

        Returns:
            A flat list of all BOM items with their quantities
        """
        try:
            bom_header = BOMHeader.objects.get(id=bom_header_id)
            items = bom_header.bom_items.all().select_related('component')

            # Create a flat list of all items
            flat_items = []
            for item in items:
                flat_items.append({
                    'id': item.id,
                    'component_id': item.component.id,
                    'component_code': item.component.code,
                    'component_name': item.component.name,
                    'quantity': str(item.quantity),  # Convert Decimal to string for JSON serialization
                    'position': item.position,
                    'item_type': item.item_type,
                    'notes': item.notes,
                    'parent_item_id': item.parent_item_id
                })

            return flat_items
        except BOMHeader.DoesNotExist:
            return []

    @staticmethod
    def get_bom_levels(bom_header_id: int) -> Dict[int, List[BOMItem]]:
        """
        Get BOM items organized by their level in the hierarchy.

        Args:
            bom_header_id: The ID of the BOM header

        Returns:
            A dictionary mapping level numbers to lists of BOM items at that level
        """
        try:
            bom_header = BOMHeader.objects.get(id=bom_header_id)
            all_items = bom_header.bom_items.all().select_related('component', 'parent_item')

            # Initialize result dictionary
            levels: Dict[int, List[BOMItem]] = {}

            # First, identify all top-level items (level 0)
            top_level_items = [item for item in all_items if item.parent_item is None]
            levels[0] = top_level_items

            # Process each level
            current_level = 0
            while True:
                next_level_items = []

                # For each item at the current level, find its children
                for parent in levels.get(current_level, []):
                    children = [item for item in all_items if item.parent_item_id == parent.id]
                    next_level_items.extend(children)

                # If no items at the next level, we're done
                if not next_level_items:
                    break

                # Store items for the next level
                current_level += 1
                levels[current_level] = next_level_items

            return levels
        except BOMHeader.DoesNotExist:
            return {}

    @staticmethod
    def find_component_usage(component_id: int) -> List[Dict[str, Any]]:
        """
        Find all BOMs where a specific component is used.

        Args:
            component_id: The ID of the component to search for

        Returns:
            A list of dictionaries with BOM header information where the component is used
        """
        # Find all BOM items that use this component
        items = BOMItem.objects.filter(
            component_id=component_id
        ).select_related('bom_header', 'bom_header__product')

        # Group by BOM header
        usage = []
        for item in items:
            usage.append({
                'bom_header_id': item.bom_header.id,
                'bom_name': item.bom_header.name,
                'bom_version': item.bom_header.version,
                'bom_status': item.bom_header.status,
                'product_id': item.bom_header.product.id,
                'product_name': item.bom_header.product.name,
                'product_code': item.bom_header.product.code,
                'item_id': item.id,
                'quantity': str(item.quantity),  # Convert Decimal to string for JSON serialization
                'position': item.position,
                'item_type': item.item_type
            })

        return usage

    @staticmethod
    def get_product_boms(product_identifier: str, status: Optional[str] = None) -> Dict[str, Any]:
        """
        Get all BOMs for a specific product, including their complete structures.

        Args:
            product_identifier: The product ID or code
            status: Optional filter for BOM status (e.g., 'active', 'draft', 'obsolete')

        Returns:
            A dictionary with product details and all associated BOMs with their structures
        """
        try:
            # Try to find the product by ID or code
            try:
                product_id = int(product_identifier)
                product_filter = Q(id=product_id)
            except ValueError:
                # If not an integer, assume it's a product code
                product_filter = Q(code=product_identifier)

            product = Product.objects.get(product_filter)

            # Get all BOM headers for this product
            bom_headers_query = BOMHeader.objects.filter(product=product).select_related('created_by')

            # Apply status filter if provided
            if status:
                bom_headers_query = bom_headers_query.filter(status=status)

            # Get all BOM headers
            bom_headers = bom_headers_query.order_by('-created_at')

            # Prepare product details
            product_details = {
                'id': product.id,
                'code': product.code,
                'name': product.name,
                'description': product.description,
                'type_id': product.type_id,
                'is_active': product.is_active
            }

            # Prepare BOM structures
            bom_structures = []
            for header in bom_headers:
                bom_structure = {
                    'id': header.id,
                    'name': header.name,
                    'code': header.code,
                    'version': header.version,
                    'status': header.status,
                    'effective_date': header.effective_date.isoformat() if header.effective_date else None,
                    'created_by': header.created_by.username,
                    'created_at': header.created_at.isoformat(),
                    'updated_at': header.updated_at.isoformat(),
                    'structure': BOMService.get_complete_bom(header.id)
                }
                bom_structures.append(bom_structure)

            # Combine product details and BOM structures
            result = {
                'product': product_details,
                'bom_count': len(bom_structures),
                'boms': bom_structures
            }

            return result
        except Product.DoesNotExist:
            return {
                'error': f"Product with identifier '{product_identifier}' does not exist",
                'product': None,
                'bom_count': 0,
                'boms': []
            }
        except Exception as e:
            return {
                'error': str(e),
                'product': None,
                'bom_count': 0,
                'boms': []
            }

    @staticmethod
    def get_product_bom_summary(product_id: int, status: Optional[str] = 'active') -> Dict[str, Any]:
        """
        Get a summary of BOM information for a product, suitable for inclusion in product details.

        Args:
            product_id: The product ID
            status: Optional filter for BOM status (default: 'active')

        Returns:
            A dictionary with BOM summary information
        """
        try:
            # Get the product
            product = Product.objects.get(id=product_id)

            # Get BOM headers for this product with the specified status
            bom_query = BOMHeader.objects.filter(product=product)
            if status:
                bom_query = bom_query.filter(status=status)

            bom_headers = bom_query.order_by('-created_at')

            if not bom_headers.exists():
                return {
                    'has_bom': False,
                    'bom_count': 0,
                    'active_bom': None,
                    'boms': []
                }

            # Prepare BOM summary information
            bom_summaries = []
            for header in bom_headers:
                # Get top-level items count
                top_level_count = header.bom_items.filter(parent_item__isnull=True).count()
                # Get total items count
                total_items_count = header.bom_items.count()

                bom_summary = {
                    'id': header.id,
                    'name': header.name,
                    'code': header.code,
                    'version': header.version,
                    'status': header.status,
                    'effective_date': header.effective_date.isoformat() if header.effective_date else None,
                    'top_level_items_count': top_level_count,
                    'total_items_count': total_items_count
                }
                bom_summaries.append(bom_summary)

            # Get the active BOM (if any)
            active_bom = next((bom for bom in bom_summaries if bom['status'] == 'active'), None)

            return {
                'has_bom': True,
                'bom_count': len(bom_summaries),
                'active_bom': active_bom,
                'boms': bom_summaries
            }
        except Product.DoesNotExist:
            return {
                'has_bom': False,
                'bom_count': 0,
                'active_bom': None,
                'boms': []
            }
        except Exception as e:
            return {
                'error': str(e),
                'has_bom': False,
                'bom_count': 0,
                'active_bom': None,
                'boms': []
            }

    @staticmethod
    def validate_bom_structure(bom_header_id: int) -> Dict[str, Any]:
        """
        Validate the BOM structure for circular references and other issues.

        Args:
            bom_header_id: The ID of the BOM header to validate

        Returns:
            A dictionary with validation results
        """
        try:
            bom_header = BOMHeader.objects.get(id=bom_header_id)
            all_items = bom_header.bom_items.all()

            # Check for circular references
            for item in all_items:
                if item.parent_item_id:
                    # Start with the item's parent
                    parent_id = item.parent_item_id
                    visited = {item.id}

                    # Traverse up the hierarchy
                    while parent_id:
                        # If we've seen this parent before, we have a cycle
                        if parent_id in visited:
                            return {
                                'valid': False,
                                'error': f"Circular reference detected: Item {item.id} is in a cycle"
                            }

                        visited.add(parent_id)

                        # Get the next parent
                        parent = next((i for i in all_items if i.id == parent_id), None)
                        parent_id = parent.parent_item_id if parent else None

            # If we get here, no circular references were found
            return {'valid': True}
        except BOMHeader.DoesNotExist:
            return {'valid': False, 'error': f"BOM Header with ID {bom_header_id} does not exist"}
        except Exception as e:
            return {'valid': False, 'error': str(e)}
