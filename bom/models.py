from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from core.models import BaseModel, BaseEntity
from catalog.models import Product


class BOMHeader(BaseEntity):
    """
    Bill of Materials Header model.

    Represents a specific version of a product's BOM structure.
    Each BOM header can have multiple BOM items that define the components
    and their hierarchical relationships.
    """
    STATUSES = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('obsolete', 'Obsolete'),
    ]

    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='bom_headers',
        help_text="The product this BOM defines"
    )
    version = models.CharField(
        max_length=20,
        help_text="Version number of this BOM (e.g., '1.0')"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUSES,
        default='draft',
        help_text="Current status of this BOM"
    )
    effective_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when this BOM becomes effective"
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_boms'
    )

    class Meta:
        db_table = 'mes_bom_header'
        ordering = ['-created_at']
        unique_together = ['product', 'version']
        verbose_name = "BOM Header"
        verbose_name_plural = "BOM Headers"

    def __str__(self):
        return f"{self.product.name} BOM v{self.version}"

    def clean(self):
        """Additional model validation"""
        super().clean()

        # Check if there's already an active BOM for this product
        if self.status == 'active':
            active_boms = BOMHeader.objects.filter(
                product=self.product,
                status='active'
            ).exclude(pk=self.pk)

            if active_boms.exists():
                raise ValidationError(
                    "There is already an active BOM for this product. "
                    "Please set the existing active BOM to obsolete first."
                )

    def save(self, *args, **kwargs):
        """Ensure validation runs on save"""
        self.full_clean()
        super().save(*args, **kwargs)

    def get_complete_bom(self):
        """
        Get the complete BOM structure as a hierarchical dictionary.

        Returns:
            list: A list of dictionaries representing the top-level items and their children
        """
        # Get all top-level items (items with no parent)
        top_level_items = self.bom_items.filter(parent_item__isnull=True).select_related('component')

        complete_bom = []

        # For each top-level item, recursively get its children
        for item in top_level_items:
            # Serialize the component data
            component_data = {
                'id': item.component.id,
                'code': item.component.code,
                'name': item.component.name,
                'description': item.component.description
            }

            item_data = {
                'id': item.id,
                'component_id': item.component.id,
                'component': component_data,
                'quantity': str(item.quantity),  # Convert Decimal to string for JSON serialization
                'position': item.position,
                'item_type': item.item_type,
                'notes': item.notes,
                'children': item.get_children()
            }
            complete_bom.append(item_data)

        return complete_bom


class BOMItem(BaseModel):
    """
    Bill of Materials Item model.

    Represents a component within a BOM structure. Each item can have a parent item
    (except for top-level items) and can be a parent to other items, creating a
    hierarchical structure.
    """
    ITEM_TYPES = [
        ('assembly', 'Assembly'),
        ('component', 'Component'),
        ('material', 'Material'),
        ('consumable', 'Consumable'),
    ]

    bom_header = models.ForeignKey(
        BOMHeader,
        on_delete=models.CASCADE,
        related_name='bom_items',
        help_text="The BOM header this item belongs to"
    )
    component = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='bom_components',
        help_text="The product/component this BOM item represents"
    )
    parent_item = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        help_text="Parent item in the BOM hierarchy (null for top-level items)"
    )
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        default=1,
        help_text="Quantity of this component needed"
    )
    position = models.CharField(
        max_length=100,
        blank=True,
        help_text="Position or location identifier (e.g., 'FRONT', 'A1', etc.)"
    )
    item_type = models.CharField(
        max_length=20,
        choices=ITEM_TYPES,
        default='component',
        help_text="Type of this BOM item"
    )
    notes = models.TextField(
        blank=True,
        help_text="Additional notes or instructions for this item"
    )

    class Meta:
        db_table = 'mes_bom_item'
        ordering = ['bom_header__id', 'position', 'id']
        verbose_name = "BOM Item"
        verbose_name_plural = "BOM Items"

    def __str__(self):
        return f"{self.component.name} ({self.quantity}) in {self.bom_header}"

    def clean(self):
        """Additional model validation"""
        super().clean()

        # Prevent circular references
        if self.parent_item and self.pk:
            # Check if this item is not set as its own parent
            if self.parent_item_id == self.pk:
                raise ValidationError("An item cannot be its own parent.")

            # Check if this item is not set as a parent of any of its ancestors
            parent = self.parent_item
            while parent:
                if parent.parent_item_id == self.pk:
                    raise ValidationError("Circular reference detected in BOM hierarchy.")
                parent = parent.parent_item

    def save(self, *args, **kwargs):
        """Ensure validation runs on save"""
        self.full_clean()
        super().save(*args, **kwargs)

    def get_children(self):
        """
        Get all children of this BOM item recursively.

        Returns:
            list: A list of dictionaries representing the children and their children
        """
        result = []
        children = self.children.all().select_related('component')

        for child in children:
            # Serialize the component data
            component_data = {
                'id': child.component.id,
                'code': child.component.code,
                'name': child.component.name,
                'description': child.component.description
            }

            child_data = {
                'id': child.id,
                'component_id': child.component.id,
                'component': component_data,
                'quantity': str(child.quantity),  # Convert Decimal to string for JSON serialization
                'position': child.position,
                'item_type': child.item_type,
                'notes': child.notes,
                'children': child.get_children()
            }
            result.append(child_data)

        return result
