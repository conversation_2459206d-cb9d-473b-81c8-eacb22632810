from django.contrib import admin
from django.utils.html import format_html
from .models import BOMHeader, BOMItem


class BOMItemInline(admin.TabularInline):
    """
    Inline admin for BOM items to be displayed within BOM header admin
    """
    model = BOMItem
    fk_name = 'bom_header'
    extra = 1
    fields = ('component', 'parent_item', 'quantity', 'position', 'item_type', 'notes')
    autocomplete_fields = ['component', 'parent_item']

    def get_queryset(self, request):
        """
        Override to optimize query with select_related
        """
        queryset = super().get_queryset(request)
        # Use a simple ordering to avoid infinite loops
        return queryset.select_related('component', 'parent_item').order_by('id')


@admin.register(BOMHeader)
class BOMHeaderAdmin(admin.ModelAdmin):
    """
    Admin configuration for BOM Header model
    """
    list_display = ('name', 'code', 'product_link', 'version', 'status', 'effective_date', 'created_by', 'created_at')
    list_filter = ('status', 'effective_date', 'created_at')
    search_fields = ('name', 'code', 'product__name', 'product__code', 'version')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ['product', 'created_by']
    inlines = [BOMItemInline]
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Product Information', {
            'fields': ('product', 'version', 'status', 'effective_date')
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def product_link(self, obj):
        """
        Display a clickable link to the product
        """
        if obj.product:
            return format_html(
                '<a href="{}">{}</a>',
                f'/admin/catalog/product/{obj.product.id}/change/',
                obj.product.name
            )
        return "-"
    product_link.short_description = "Product"
    product_link.admin_order_field = "product__name"


@admin.register(BOMItem)
class BOMItemAdmin(admin.ModelAdmin):
    """
    Admin configuration for BOM Item model
    """
    list_display = ('id', 'bom_header_link', 'component_link', 'parent_item_link', 'quantity', 'position', 'item_type')
    list_filter = ('item_type', 'bom_header__status')
    search_fields = ('component__name', 'component__code', 'bom_header__name', 'position', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ['bom_header', 'component', 'parent_item']
    fieldsets = (
        (None, {
            'fields': ('bom_header', 'component', 'parent_item')
        }),
        ('Item Details', {
            'fields': ('quantity', 'position', 'item_type', 'notes')
        }),
        ('Audit Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def bom_header_link(self, obj):
        """
        Display a clickable link to the BOM header
        """
        if obj.bom_header:
            return format_html(
                '<a href="{}">{}</a>',
                f'/admin/bom/bomheader/{obj.bom_header.id}/change/',
                obj.bom_header.name
            )
        return "-"
    bom_header_link.short_description = "BOM Header"
    bom_header_link.admin_order_field = "bom_header__name"

    def component_link(self, obj):
        """
        Display a clickable link to the component
        """
        if obj.component:
            return format_html(
                '<a href="{}">{}</a>',
                f'/admin/catalog/product/{obj.component.id}/change/',
                obj.component.name
            )
        return "-"
    component_link.short_description = "Component"
    component_link.admin_order_field = "component__name"

    def parent_item_link(self, obj):
        """
        Display a clickable link to the parent item
        """
        if obj.parent_item:
            return format_html(
                '<a href="{}">{}</a>',
                f'/admin/bom/bomitem/{obj.parent_item.id}/change/',
                f"{obj.parent_item.component.name}"
            )
        return "Top Level"
    parent_item_link.short_description = "Parent Item"
    # Removed admin_order_field to prevent ordering issues
