"""
Migration to fix BOMItem ordering.

This migration updates the ordering in the BOMItem model to prevent infinite loops.
"""
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('bom', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='bomitem',
            options={'ordering': ['bom_header__id', 'position', 'id'], 'verbose_name': 'BOM Item', 'verbose_name_plural': 'BOM Items'},
        ),
    ]
