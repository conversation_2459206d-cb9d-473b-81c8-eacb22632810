"""
Initial migration for the BOM app.

This migration creates the BOMHeader and BOMItem models.
"""
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('catalog', '0005_product_fifo_custom_config_product_fifo_enabled_and_more'),
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BOMHeader',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('version', models.CharField(help_text="Version number of this BOM (e.g., '1.0')", max_length=20)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('obsolete', 'Obsolete')], default='draft', help_text='Current status of this BOM', max_length=20)),
                ('effective_date', models.DateField(blank=True, help_text='Date when this BOM becomes effective', null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_boms', to='authentication.user')),
                ('product', models.ForeignKey(help_text='The product this BOM defines', on_delete=django.db.models.deletion.CASCADE, related_name='bom_headers', to='catalog.product')),
            ],
            options={
                'verbose_name': 'BOM Header',
                'verbose_name_plural': 'BOM Headers',
                'db_table': 'mes_bom_header',
                'ordering': ['-created_at'],
                'unique_together': {('product', 'version')},
            },
        ),
        migrations.CreateModel(
            name='BOMItem',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.DecimalField(decimal_places=3, default=1, help_text='Quantity of this component needed', max_digits=10)),
                ('position', models.CharField(blank=True, help_text="Position or location identifier (e.g., 'FRONT', 'A1', etc.)", max_length=100)),
                ('item_type', models.CharField(choices=[('assembly', 'Assembly'), ('component', 'Component'), ('material', 'Material'), ('consumable', 'Consumable')], default='component', help_text='Type of this BOM item', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Additional notes or instructions for this item')),
                ('bom_header', models.ForeignKey(help_text='The BOM header this item belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='bom_items', to='bom.bomheader')),
                ('component', models.ForeignKey(help_text='The product/component this BOM item represents', on_delete=django.db.models.deletion.CASCADE, related_name='bom_components', to='catalog.product')),
                ('parent_item', models.ForeignKey(blank=True, help_text='Parent item in the BOM hierarchy (null for top-level items)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='bom.bomitem')),
            ],
            options={
                'verbose_name': 'BOM Item',
                'verbose_name_plural': 'BOM Items',
                'db_table': 'mes_bom_item',
                'ordering': ['bom_header', 'parent_item', 'position'],
            },
        ),
    ]
