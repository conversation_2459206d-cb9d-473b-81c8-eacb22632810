# Bill of Materials (BOM) Module for MES Project

This module provides Bill of Materials (BOM) functionality for the MES project, allowing users to define and manage hierarchical product structures with versioning and component relationships.

## Features

- BOM header management (create, update, delete, list)
- BOM item management with hierarchical parent-child relationships
- Version control for BOMs with status tracking
- Hierarchical traversal of BOM structures
- Integration with the Product catalog
- Validation of BOM structures to prevent circular references
- API endpoints for BOM operations

## API Endpoints

### BOM Headers

- `GET /mes_trace/bom/api/headers/` - List all BOM headers
- `POST /mes_trace/bom/api/headers/` - Create a new BOM header
- `GET /mes_trace/bom/api/headers/{id}/` - Retrieve a specific BOM header
- `PUT /mes_trace/bom/api/headers/{id}/` - Update a specific BOM header
- `DELETE /mes_trace/bom/api/headers/{id}/` - Delete a specific BOM header

#### BOM Header Actions

- `GET /mes_trace/bom/api/headers/{id}/complete_bom/` - Get the complete BOM structure for a specific BOM header
- `GET /mes_trace/bom/api/headers/{id}/flat_bom/` - Get a flattened list of all items in the BOM
- `GET /mes_trace/bom/api/headers/{id}/bom_levels/` - Get BOM items organized by their level in the hierarchy
- `GET /mes_trace/bom/api/headers/{id}/validate/` - Validate the BOM structure for circular references and other issues

#### Product-Based BOM Retrieval

- `GET /mes_trace/bom/api/headers/product_boms/` - Get all BOMs for a specific product

  **Query Parameters:**
  - `product_id` - The product ID (required if product_code not provided)
  - `product_code` - The product code (required if product_id not provided)
  - `status` - Optional filter for BOM status (e.g., 'active', 'draft', 'obsolete')

  **Example:**
  ```
  GET /mes_trace/bom/api/headers/product_boms/?product_id=123
  GET /mes_trace/bom/api/headers/product_boms/?product_code=PROD-123
  GET /mes_trace/bom/api/headers/product_boms/?product_id=123&status=active
  ```

  **Response:**
  ```json
  {
    "product": {
      "id": 123,
      "code": "PROD-123",
      "name": "Product Name",
      "description": "Product Description",
      "type_id": "part",
      "is_active": true
    },
    "bom_count": 2,
    "boms": [
      {
        "id": 1,
        "name": "BOM Name",
        "code": "BOM-001",
        "version": "1.0",
        "status": "active",
        "effective_date": "2025-05-01",
        "created_by": "username",
        "created_at": "2025-05-01T12:00:00Z",
        "updated_at": "2025-05-01T12:00:00Z",
        "structure": [
          {
            "id": 1,
            "component_id": 456,
            "component": {
              "id": 456,
              "code": "COMP-456",
              "name": "Component Name",
              "description": "Component Description"
            },
            "quantity": "2.000",
            "position": "POS1",
            "item_type": "assembly",
            "notes": "",
            "children": [
              {
                "id": 3,
                "component_id": 789,
                "component": {
                  "id": 789,
                  "code": "COMP-789",
                  "name": "Subcomponent Name",
                  "description": "Subcomponent Description"
                },
                "quantity": "3.000",
                "position": "SUBPOS",
                "item_type": "component",
                "notes": "",
                "children": []
              }
            ]
          }
        ]
      }
    ]
  }
  ```

### BOM Items

- `GET /mes_trace/bom/api/items/` - List all BOM items
- `POST /mes_trace/bom/api/items/` - Create a new BOM item
- `GET /mes_trace/bom/api/items/{id}/` - Retrieve a specific BOM item
- `PUT /mes_trace/bom/api/items/{id}/` - Update a specific BOM item
- `DELETE /mes_trace/bom/api/items/{id}/` - Delete a specific BOM item

#### BOM Item Actions

- `GET /mes_trace/bom/api/items/{id}/children/` - Get all children of a specific BOM item
- `GET /mes_trace/bom/api/items/component_usage/?component_id={id}` - Find all BOMs where a specific component is used

## Models

### BOMHeader

Represents a specific version of a product's BOM structure.

**Fields:**
- `id` - Primary key
- `name` - Name of the BOM
- `code` - Unique code for the BOM
- `description` - Description of the BOM
- `is_active` - Whether the BOM is active
- `product` - Foreign key to the Product model
- `version` - Version number of the BOM
- `status` - Status of the BOM (draft, active, obsolete)
- `effective_date` - Date when the BOM becomes effective
- `created_by` - User who created the BOM
- `created_at` - Date and time when the BOM was created
- `updated_at` - Date and time when the BOM was last updated

### BOMItem

Represents a component within a BOM structure. Each item can have a parent item (except for top-level items) and can be a parent to other items, creating a hierarchical structure.

**Fields:**
- `id` - Primary key
- `bom_header` - Foreign key to the BOMHeader model
- `component` - Foreign key to the Product model
- `parent_item` - Self-referencing foreign key to the BOMItem model (null for top-level items)
- `quantity` - Quantity of the component needed
- `position` - Position or location identifier
- `item_type` - Type of the BOM item (assembly, component, material, consumable)
- `notes` - Additional notes or instructions for the item
- `created_at` - Date and time when the item was created
- `updated_at` - Date and time when the item was last updated

## Implementation Details

This module implements the Enhanced Relational BOM Structure design, which uses a self-referencing parent-child relationship in the BOMItem model to represent hierarchical product structures. This approach provides a good balance of simplicity and functionality, with the ability to represent multi-level BOMs while maintaining compatibility with the existing architecture.

## Usage Examples

### Creating a BOM

```python
from bom.models import BOMHeader, BOMItem
from catalog.models import Product
from django.contrib.auth import get_user_model

User = get_user_model()

# Get the product and user
product = Product.objects.get(code='PROD-123')
user = User.objects.get(username='admin')

# Create a BOM header
bom_header = BOMHeader.objects.create(
    name='Product BOM',
    code='BOM-001',
    product=product,
    version='1.0',
    status='draft',
    created_by=user
)

# Create top-level items
assembly = BOMItem.objects.create(
    bom_header=bom_header,
    component=Product.objects.get(code='COMP-456'),
    quantity=1,
    position='POS1',
    item_type='assembly'
)

# Create child items
subcomponent = BOMItem.objects.create(
    bom_header=bom_header,
    component=Product.objects.get(code='COMP-789'),
    parent_item=assembly,
    quantity=2,
    position='SUBPOS',
    item_type='component'
)
```

### Retrieving a Complete BOM

```python
from bom.services import BOMService

# Get the complete BOM structure
complete_bom = BOMService.get_complete_bom(bom_header_id=1)

# Get all BOMs for a specific product
product_boms = BOMService.get_product_boms(product_identifier='PROD-123', status='active')
```
