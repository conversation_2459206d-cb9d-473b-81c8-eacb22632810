export type FieldType =
  | "text"
  | "number"
  | "checkbox"
  | "select"
  | "multiselect"
  | "date"
  | "time"
  | "datetime"
  | "break";
export type Operator = "==" | "!=" | ">" | "<" | ">=" | "<=";

interface ComparisonValidationConfig {
  operator: Operator;
  value: number | string;
}

interface AllowedValuesConfig {
  values: string[];
}

interface RegexMatchConfig {
  pattern: string;
}

interface CustomScriptConfig {
  script: string; // JS snippet (e.g., "(val, context) => val % 2 === 0")
}

export type ValidationRule =
  | {
      type: "comparison";
      config: ComparisonValidationConfig;
      message?: string;
    }
  | {
      type: "allowedValues";
      config: AllowedValuesConfig;
      message?: string;
    }
  | {
      type: "regexMatch";
      config: RegexMatchConfig;
      message?: string;
    }
  | {
      type: "customScript";
      config: CustomScriptConfig;
      message?: string;
    };

export interface FieldSchema<Type extends FieldType = FieldType> {
  id: string;
  name: string;
  label: string;
  placeholder?: string;
  description?: string;
  type: FieldType;
  required?: boolean;
  options?: (
    | string
    | {
        value: string;
        label: string;
        hideFields?: string[];
      }
  )[];
  validation?: {
    pattern?: string;
    message?: string;
  };
  condition?: {
    field: string;
    operator: Operator;
    value: any;
  };
  dependencies?: {
    field: string;
    operator: Operator;
    value: any;
    validations?: ValidationRule[];
  };
  asyncOptionsConfig?: Type extends "dropdown" | "multiselect"
    ? {
        identifier: string;
      }
    : never;
  defaultValue?: any;
  render?: (props: any) => React.ReactNode;
  layout?: {
    xs?: number; // 12 = full width
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    rowBreak?: boolean;
  };
  section?: string;
}

export interface FormSchema {
  id: string;
  title: string;
  fields: FieldSchema[];
}

export interface DynamicFormRendererProps {
  schema: FieldSchema[];
  onSubmit: (data: Record<string, any>) => void;
  workflows: Workflow[];
}

export type RecordEvent =
  | "created"
  | "edited"
  | "deleted"
  | "created_or_edited";
export type FieldEvent = "onChange" | "onFocus" | "onBlur";

export type FormEvent =
  | "load"
  | "user_input"
  | "before_submit"
  | "on_submit"
  | "after_submit"
  | "update_field"
  | "validate";

export type WorkflowActionType = "api_call" | "update_field" | "script";

export interface ApiCallActionConfig {
  url: string;
  method: "GET" | "POST" | "PUT" | "DELETE";
  headers?: Record<string, string>;
  body?: Record<string, any>;
  mapResponseToField?: string;
}

export interface UpdateFieldActionConfig {
  field: string;
  value: any;
}

export interface ScriptActionConfig {
  code: string; // JS code to be executed
}

// Discriminated union of typed actions
export type WorkflowAction =
  | {
      type: "api_call";
      config: ApiCallActionConfig;
    }
  | {
      type: "update_field";
      config: UpdateFieldActionConfig;
    }
  | {
      type: "script";
      config: ScriptActionConfig;
    };

export interface WorkflowTrigger {
  type: "form" | "field" | "record";
  event: FormEvent | FieldEvent | RecordEvent;
  fieldName?: string; // required only when type === 'field'
}

export interface WorkflowCondition {
  field: string;
  operator: Operator;
  value: any;
}

export interface Workflow {
  id: string;
  formId: string;
  name: string;

  trigger: WorkflowTrigger;

  conditions?: WorkflowCondition[];
  actions: WorkflowAction[];
}
