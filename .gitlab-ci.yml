image: docker:19.03.10

services:
  - docker:dind

variables:
  REPOSITORY_URL: 476114124192.dkr.ecr.ap-south-1.amazonaws.com/dev-veridian/mes-backend
  TASK_DEFINITION_NAME: dev-veridian-backend-td
  CLUSTER_NAME: dev-veridian
  SERVICE_NAME: dev-veridian-backend-svc
before_script:
  - apk add --no-cache curl jq python py-pip
  - pip install awscli --no-build-isolation
  - echo $AWS_ACCESS_KEY_ID $AWS_DEFAULT_REGION  $AWS_SECRET_ACCESS_KEY
  - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
  - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
  - aws configure set region $AWS_DEFAULT_REGION
  - $(aws ecr get-login --no-include-email --region "${AWS_DEFAULT_REGION}")
  - IMAGE_TAG="$(echo $CI_COMMIT_SHA | head -c 8)"

stages:
  - build
  - deploy

build:
  stage: build
  script:
    - echo "Building image..."
    - docker build -t $REPOSITORY_URL:latest .
    - echo "Tagging image..."
    - docker tag $REPOSITORY_URL:latest $REPOSITORY_URL:$IMAGE_TAG
    - echo "Pushing image..."
    - docker push $REPOSITORY_URL:latest
    - docker push $REPOSITORY_URL:$IMAGE_TAG
  only:
    - development

deploy:
  stage: deploy
  script:
    - echo $REPOSITORY_URL:$IMAGE
    - echo "Registering new container definition..."
    - echo "Updating the service..."
    - aws ecs update-service --region "${AWS_DEFAULT_REGION}" --cluster "${CLUSTER_NAME}" --service "${SERVICE_NAME}"  --task-definition "${TASK_DEFINITION_NAME}" --force-new-deployment 
  only:
    - development
