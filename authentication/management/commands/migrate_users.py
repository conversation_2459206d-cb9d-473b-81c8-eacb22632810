from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import connection

User = get_user_model()

class Command(BaseCommand):
    help = 'Migrate existing auth_user data to custom user model'

    def handle(self, *args, **kwargs):
        with connection.cursor() as cursor:
            # Check if auth_user table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'auth_user'
                );
            """)
            auth_user_exists = cursor.fetchone()[0]

            if auth_user_exists:
                # Fetch existing users
                cursor.execute("""
                    SELECT username, password, email, first_name, last_name, 
                           is_superuser, is_staff, is_active, date_joined, last_login 
                    FROM auth_user;
                """)
                users = cursor.fetchall()

                # Create new users
                for user in users:
                    User.objects.create(
                        username=user[0],
                        password=user[1],  # Already hashed
                        email=user[2],
                        first_name=user[3],
                        last_name=user[4],
                        is_superuser=user[5],
                        is_staff=user[6],
                        is_active=user[7],
                        date_joined=user[8],
                        last_login=user[9],
                        employee_id=f"LEGACY_{user[0]}",  # Default employee_id
                        department="Legacy Import"  # Default department
                    )
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully migrated {len(users)} users')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('No auth_user table found. No migration needed.')
                )
