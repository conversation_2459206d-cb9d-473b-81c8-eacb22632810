# Generated by Django 5.1 on 2024-12-07 18:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccessScope',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'mes_access_scopes',
            },
        ),
        migrations.AlterField(
            model_name='user',
            name='department',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='user',
            name='employee_id',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=50, null=True, unique=True),
        ),
        migrations.CreateModel(
            name='Group',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_groups', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'mes_groups',
            },
        ),
        migrations.CreateModel(
            name='Module',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('permission_type', models.CharField(choices=[('none', 'No Restrictions'), ('module', 'Module Level'), ('object', 'Object Level')], default='module', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_modules', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'mes_modules',
            },
        ),
        migrations.CreateModel(
            name='GroupObjectPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_allowed', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('object_id', models.IntegerField()),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='object_permissions', to='authentication.group')),
                ('scope', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.accessscope')),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.module')),
            ],
            options={
                'db_table': 'mes_group_object_permissions',
                'unique_together': {('group', 'module', 'object_id', 'scope')},
            },
        ),
        migrations.CreateModel(
            name='GroupModulePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_allowed', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='module_permissions', to='authentication.group')),
                ('scope', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.accessscope')),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.module')),
            ],
            options={
                'db_table': 'mes_group_module_permissions',
                'unique_together': {('group', 'module', 'scope')},
            },
        ),
        migrations.CreateModel(
            name='UserGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_user_groups', to=settings.AUTH_USER_MODEL)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mes_group_users', to='authentication.group')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mes_groups', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'mes_user_mes_groups',
                'unique_together': {('user', 'group')},
            },
        ),
        migrations.CreateModel(
            name='UserModulePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_allowed', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.module')),
                ('scope', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.accessscope')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='module_permissions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'mes_user_module_permissions',
                'unique_together': {('user', 'module', 'scope')},
            },
        ),
        migrations.CreateModel(
            name='UserObjectPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_allowed', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('object_id', models.IntegerField()),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.module')),
                ('scope', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.accessscope')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='object_permissions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'mes_user_object_permissions',
                'unique_together': {('user', 'module', 'object_id', 'scope')},
            },
        ),
    ]
