# Generated by Django 5.1 on 2024-12-08 08:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0002_accessscope_alter_user_department_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='groupmodulepermission',
            name='allow_all_objects',
            field=models.BooleanField(default=False, help_text="If True and module type is 'object', all group members will have access to all objects for this scope"),
        ),
        migrations.AddField(
            model_name='usermodulepermission',
            name='allow_all_objects',
            field=models.BooleanField(default=False, help_text="If True and module type is 'object', user will have access to all objects for this scope"),
        ),
    ]
