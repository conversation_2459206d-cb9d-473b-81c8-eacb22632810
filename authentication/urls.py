from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from .views.health_view import HealthCheckView
from .views.auth_views import AuthViewSet, CustomTokenObtainPairView
from .views.permission_views import UserPermissionsView

router = DefaultRouter()
router.register('', AuthViewSet, basename='auth')

urlpatterns = [
    path('', include(router.urls)),
    path('health/', HealthCheckView.as_view(), name='health_check'),
    path('login/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('permissions/', UserPermissionsView.as_view(), name='user-permissions'),
]
