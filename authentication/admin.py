from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import Permission
from django.utils.translation import gettext_lazy as _
from .models import (
    User, Module, AccessScope, Group, UserGroup,
    UserModulePermission, GroupModulePermission,
    UserObjectPermission, GroupObjectPermission
)


class UserModulePermissionInline(admin.TabularInline):
    model = UserModulePermission
    extra = 0
    autocomplete_fields = ['module', 'scope']
    can_delete = True
    verbose_name = "Module Permission"
    verbose_name_plural = "Module Permissions"
    fk_name = 'user'
    classes = ['collapse']


class UserObjectPermissionInline(admin.TabularInline):
    model = UserObjectPermission
    extra = 0
    autocomplete_fields = ['module', 'scope']
    can_delete = True
    verbose_name = "Object Permission"
    verbose_name_plural = "Object Permissions"
    fk_name = 'user'
    classes = ['collapse']


class UserGroupInline(admin.TabularInline):
    model = UserGroup
    extra = 0
    autocomplete_fields = ['group']
    can_delete = True
    verbose_name = "MES Group"
    verbose_name_plural = "MES Groups"
    fk_name = 'user'
    classes = ['collapse']


class GroupModulePermissionInline(admin.TabularInline):
    model = GroupModulePermission
    extra = 0
    autocomplete_fields = ['module', 'scope']
    can_delete = True
    verbose_name = "Module Permission"
    verbose_name_plural = "Module Permissions"
    classes = ['collapse']

class GroupObjectPermissionInline(admin.TabularInline):
    model = GroupObjectPermission
    extra = 0
    autocomplete_fields = ['module', 'scope']
    can_delete = True
    verbose_name = "Object Permission"
    verbose_name_plural = "Object Permissions"
    classes = ['collapse']


@admin.register(User)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'employee_id', 'department', 'is_staff', 'is_superuser')
    search_fields = ('username', 'email', 'employee_id', 'department')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'department')
    
    def get_inlines(self, request, obj=None):
        """Dynamically add inlines based on user type"""
        if obj is None:  # This is the add form
            return []
        
        inlines = [UserGroupInline]
        
        # Add permission inlines for all users
        inlines.extend([UserModulePermissionInline, UserObjectPermissionInline])
        
        return inlines

    def get_fieldsets(self, request, obj=None):
        if obj is None:  # This is the add form
            return self.add_fieldsets

        # Start with basic fieldsets
        custom_fieldsets = [
            (None, {'fields': ('username', 'password')}),
            (_('Personal info'), {'fields': ('first_name', 'last_name', 'email', 'employee_id', 'department'), 'classes': ('collapse',)}),
            (_('Status'), {'fields': ('is_active', 'is_staff', 'is_superuser'), 'classes': ('collapse',)}),
            (_('Important dates'), {'fields': ('last_login', 'date_joined'), 'classes': ('collapse',)}),
        ]

        # Add Django's permission management for staff/superusers
        if obj.is_staff or obj.is_superuser:
            custom_fieldsets.insert(3, (_('Django Permissions'), {
                'fields': ('groups', 'user_permissions'),
                'classes': ('collapse',),
                'description': _('These are Django\'s built-in permissions. For MES-specific permissions, please check the sections below.')
            }))

        return custom_fieldsets

    def get_readonly_fields(self, request, obj=None):
        """Make certain fields readonly after creation"""
        if obj:  # This is the edit form
            return ('last_login', 'date_joined')
        return ()

    # Custom fields for add user page
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'password1', 'password2', 'employee_id', 'department', 'is_staff', 'is_superuser'),
        }),
    )


@admin.register(Module)
class ModuleAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'permission_type', 'is_active')
    search_fields = ('name', 'code')
    list_filter = ('permission_type', 'is_active')


@admin.register(AccessScope)
class AccessScopeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code')
    search_fields = ('name', 'code')


class GroupUserInline(admin.TabularInline):
    model = UserGroup
    extra = 0
    autocomplete_fields = ['user']
    can_delete = True
    verbose_name = "User"
    verbose_name_plural = "Users"
    fk_name = 'group'
    classes = ['collapse']


@admin.register(Group)
class GroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)
    inlines = [GroupUserInline, GroupModulePermissionInline, GroupObjectPermissionInline]

    def get_users_count(self, obj):
        return obj.mes_group_users.count()
    get_users_count.short_description = 'Users Count'


# @admin.register(GroupModulePermission)
# class GroupModulePermissionAdmin(admin.ModelAdmin):
#     list_display = ('group', 'module', 'scope', 'is_allowed')
#     search_fields = ('group__name', 'module__name', 'scope__name')
#     list_filter = ('is_allowed', 'module', 'scope')
#     autocomplete_fields = ['group', 'module', 'scope']


# @admin.register(GroupObjectPermission)
# class GroupObjectPermissionAdmin(admin.ModelAdmin):
#     list_display = ('group', 'module', 'object_id', 'scope', 'is_allowed')
#     search_fields = ('group__name', 'module__name', 'scope__name', 'object_id')
#     list_filter = ('is_allowed', 'module', 'scope')
#     autocomplete_fields = ['group', 'module', 'scope']


# @admin.register(UserGroup)
# class UserGroupAdmin(admin.ModelAdmin):
#     list_display = ('user', 'group')
#     search_fields = ('user__username', 'group__name')
#     autocomplete_fields = ['user', 'group']


# @admin.register(UserModulePermission)
# class UserModulePermissionAdmin(admin.ModelAdmin):
#     list_display = ('user', 'module', 'scope', 'is_allowed')
#     search_fields = ('user__username', 'module__name', 'scope__name')
#     list_filter = ('is_allowed', 'module', 'scope')
#     autocomplete_fields = ['user', 'module', 'scope']


# @admin.register(UserObjectPermission)
# class UserObjectPermissionAdmin(admin.ModelAdmin):
#     list_display = ('user', 'module', 'object_id', 'scope', 'is_allowed')
#     search_fields = ('user__username', 'module__name', 'scope__name', 'object_id')
#     list_filter = ('is_allowed', 'module', 'scope')
#     autocomplete_fields = ['user', 'module', 'scope']
