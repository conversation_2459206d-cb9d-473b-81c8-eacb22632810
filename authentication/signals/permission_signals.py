from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import Signal, receiver
from django.contrib.auth import get_user_model
from ..models import (
    UserGroup, Group, UserModulePermission, GroupModulePermission,
    UserObjectPermission, GroupObjectPermission
)
from ..cache.permission_cache import PermissionCacheManager

# Custom signal for permission changes
permission_changed = Signal()

# Initialize cache manager
cache_manager = PermissionCacheManager()

def _get_affected_user_ids(instance) -> set:
    """Helper function to get all user IDs affected by a permission change"""
    affected_users = set()
    
    if hasattr(instance, 'user_id'):
        affected_users.add(instance.user_id)
    elif hasattr(instance, 'group_id'):
        # For group-related changes, get all users in the group
        affected_users.update(
            UserGroup.objects.filter(group_id=instance.group_id)
            .values_list('user_id', flat=True)
        )
    
    return affected_users

@receiver([post_save, post_delete], sender=UserGroup)
def handle_user_group_change(sender, instance, **kwargs):
    """Handle changes to user-group associations"""
    # When user-group association changes, invalidate cache for the user
    if instance.user_id:
        cache_manager.invalidate_users({instance.user_id})

@receiver([post_save, post_delete], sender=UserModulePermission)
def handle_user_module_permission_change(sender, instance, **kwargs):
    """Handle changes to user module permissions"""
    if instance.user_id:
        cache_manager.invalidate_users({instance.user_id})

@receiver([post_save, post_delete], sender=GroupModulePermission)
def handle_group_module_permission_change(sender, instance, **kwargs):
    """Handle changes to group module permissions"""
    # Invalidate cache for all users in the group
    affected_users = set(
        UserGroup.objects.filter(group_id=instance.group_id)
        .values_list('user_id', flat=True)
    )
    if affected_users:
        cache_manager.invalidate_users(affected_users)

@receiver([post_save, post_delete], sender=UserObjectPermission)
def handle_user_object_permission_change(sender, instance, **kwargs):
    """Handle changes to user object permissions"""
    if instance.user_id:
        cache_manager.invalidate_users({instance.user_id})

@receiver([post_save, post_delete], sender=GroupObjectPermission)
def handle_group_object_permission_change(sender, instance, **kwargs):
    """Handle changes to group object permissions"""
    # Invalidate cache for all users in the group
    affected_users = set(
        UserGroup.objects.filter(group_id=instance.group_id)
        .values_list('user_id', flat=True)
    )
    if affected_users:
        cache_manager.invalidate_users(affected_users)

@receiver(post_save, sender=Group)
def handle_group_change(sender, instance, **kwargs):
    """Handle changes to group properties"""
    # When group properties change, invalidate cache for all users in the group
    affected_users = set(
        UserGroup.objects.filter(group_id=instance.id)
        .values_list('user_id', flat=True)
    )
    if affected_users:
        cache_manager.invalidate_users(affected_users)

@receiver(m2m_changed, sender=get_user_model().groups.through)
def handle_user_groups_m2m_change(sender, instance, action, pk_set, **kwargs):
    """Handle changes to user-group many-to-many relationships"""
    if action.startswith("post_"):
        if isinstance(instance, get_user_model()):
            # If the instance is a user, just invalidate that user
            cache_manager.invalidate_users({instance.id})
        else:
            # If the instance is a group, invalidate all users in that group
            affected_users = set(
                UserGroup.objects.filter(group_id=instance.id)
                .values_list('user_id', flat=True)
            )
            if affected_users:
                cache_manager.invalidate_users(affected_users)

@receiver(m2m_changed, sender=get_user_model().user_permissions.through)
def handle_user_permissions_m2m_change(sender, instance, action, pk_set, **kwargs):
    """Handle changes to user-permission many-to-many relationships"""
    if action.startswith("post_"):
        if isinstance(instance, get_user_model()):
            cache_manager.invalidate_users({instance.id})
