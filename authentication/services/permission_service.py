from typing import Dict, List, Set, Optional, Any, Union, Tuple
from django.conf import settings
from django.core.cache import cache
from django.db.models import Q
from ..models import (
    User, Module, AccessScope, UserGroup,
    UserModulePermission, GroupModulePermission,
    UserObjectPermission, GroupObjectPermission
)
from ..cache.permission_cache import PermissionCacheManager
from ..signals.permission_signals import permission_changed


class PermissionService:
    """
    A comprehensive service class for managing and checking user permissions.

    This class implements a singleton pattern and utilizes caching for optimized performance.
    It handles various types of permissions including module-level and object-level permissions
    for both users and groups.

    Key Features:
    1. Singleton Implementation: Ensures a single instance is used throughout the application.
    2. Caching Mechanism: Utilizes PermissionCacheManager for efficient permission retrieval.
    3. Permission Calculation: Computes permissions based on user and group assignments.
    4. Superuser Handling: Provides full permissions to superusers.
    5. Permission Validation: Ensures the integrity of the permission structure.
    6. Permission Checking: Allows verification of user permissions for specific modules, scopes, and objects.

    Main Components:
    - Permission Structure: A dictionary with modules as keys, containing information about
      scopes, object permissions, and all-objects-allowed flags.
    - Module Types: Supports different module types (e.g., 'object' for object-level permissions).
    - Scopes: Defines the types of actions allowed within a module.
    - Object Permissions: Granular permissions for specific objects within a module.

    Usage:
    - Retrieve permissions: Use _get_user_permissions_impl() to get a user's permissions.
    - Check permissions: Use check_permissions() to verify if a user has specific permissions.
    - Calculate permissions: _calculate_user_permissions() computes permissions for a non-superuser.
    - Handle permission changes: _handle_permission_change() is connected to permission change signals.

    Note: This class handles complex permission scenarios and caching. It's crucial to understand
    the permission structure and the implications of changes to this class, as it can affect
    system-wide access control.
    """

    _instance = None
    _cache_manager = None

    @classmethod
    def get_instance(cls):
        """
        Retrieves the singleton instance of PermissionService.
        Initializes the instance and connects to permission change signals if not already done.
        """
        if cls._instance is None:
            cls._instance = cls()
            cls._cache_manager = PermissionCacheManager()
            permission_changed.connect(cls._handle_permission_change)
        return cls._instance

    @classmethod
    def _handle_permission_change(cls, sender, user_ids: List[int], **kwargs):
        """
        Handles permission change signals by invalidating the cache for affected users.
        This ensures that the next permission check will recalculate permissions.

        Args:
            sender: The sender of the signal (unused in this method).
            user_ids (List[int]): List of user IDs whose permissions have changed.
            **kwargs: Additional keyword arguments (unused in this method).
        """
        cls._cache_manager.invalidate_users(set(user_ids))

    @classmethod
    def _create_empty_module_permission(cls, module_type: str) -> Dict:
        """
        Creates an empty permission structure for a module.

        Args:
            module_type (str): The type of the module (e.g., 'object').

        Returns:
            Dict: An empty permission structure for the module.
        """
        return {
            'module_type': module_type,
            'scopes': set(),
            'object_permissions': {},
            'all_objects_allowed': {}
        }

    @classmethod
    def _validate_permissions(cls, permissions: Dict) -> None:
        """
        Validates the structure of the permissions dictionary.
        Ensures that all required keys are present and have the correct types.

        Args:
            permissions (Dict): The permissions dictionary to validate.

        Raises:
            AssertionError: If the permissions structure is invalid.
        """
        for module_code, module_data in permissions.items():
            assert isinstance(module_data, dict), f"Invalid module data for {module_code}"
            assert 'module_type' in module_data, f"Missing module_type for {module_code}"
            assert 'scopes' in module_data, f"Missing scopes for {module_code}"
            assert isinstance(module_data['scopes'], set), f"Invalid scopes type for {module_code}"
            assert 'object_permissions' in module_data, f"Missing object_permissions for {module_code}"
            assert 'all_objects_allowed' in module_data, f"Missing all_objects_allowed for {module_code}"

            if module_data['module_type'] == 'object':
                for scope in module_data['scopes']:
                    assert isinstance(module_data['all_objects_allowed'].get(scope, False), bool), \
                        f"Invalid all_objects_allowed type for {module_code}.{scope}"
                    if scope in module_data['object_permissions']:
                        assert isinstance(module_data['object_permissions'][scope], set), \
                            f"Invalid object_permissions type for {module_code}.{scope}"

    @classmethod
    def _get_user_permissions(cls, user_id: int) -> Dict:
        """
        Retrieves user permissions from cache or calculates them if not cached.

        Args:
            user_id (int): The ID of the user.

        Returns:
            Dict: The permissions dictionary for the user.
        """
        return cls.get_instance()._get_user_permissions_impl(user_id)

    def _get_user_permissions_impl(self, user_id: int) -> Dict:
        """
        Implementation of get user permissions. Checks cache first, then calculates if necessary.

        Args:
            user_id (int): The ID of the user.

        Returns:
            Dict: The permissions dictionary for the user.

        Note:
            This method handles caching, superuser permissions, and permission structure validation.
        """
        # Check cache first
        permissions = self._cache_manager.get_permissions(user_id)
        if permissions is not None:
            return permissions

        # Calculate permissions if not in cache
        try:
            user = User.objects.get(id=user_id)
            if user.is_superuser:
                permissions = self._get_superuser_permissions()
            else:
                permissions = self._calculate_user_permissions(user)

            # Validate permissions before caching
            self._validate_permissions(permissions)

            # Cache the permissions
            self._cache_manager.set_permissions(user_id, permissions)
            return permissions

        except User.DoesNotExist:
            return {}
        except AssertionError as e:
            # Log the error but return empty permissions to prevent system failure
            import logging
            logging.error(f"Invalid permission structure: {str(e)}")
            return {}

    def _calculate_user_permissions(self, user: User) -> Dict:
        """
        Calculates all permissions for a non-superuser.

        This method retrieves and processes:
        1. User's group memberships
        2. User's direct module permissions
        3. Group module permissions
        4. User's object permissions
        5. Group object permissions

        It then processes these permissions, with user permissions taking precedence over group permissions.

        Args:
            user (User): The user object for whom to calculate permissions.

        Returns:
            Dict: A dictionary containing all calculated permissions for the user.
        """
        # Get user's groups
        user_groups = set(UserGroup.objects.filter(
            user_id=user.id
        ).values_list('group_id', flat=True))

        # Get module permissions
        # User's direct module permissions
        user_module_perms = UserModulePermission.objects.filter(
            user=user,
            is_allowed=True
        ).select_related('module', 'scope')

        # Group module permissions
        group_module_perms = GroupModulePermission.objects.filter(
            group__id__in=user_groups,
            is_allowed=True
        ).select_related('module', 'scope')

        # Object permissions
        user_object_perms = UserObjectPermission.objects.filter(
            user=user,
            is_allowed=True
        ).select_related('module', 'scope')

        group_object_perms = GroupObjectPermission.objects.filter(
            group__id__in=user_groups,
            is_allowed=True
        ).select_related('module', 'scope')

        # Process permissions
        permissions = {}
        # Process group permissions first, then user permissions to maintain precedence
        self._process_module_permissions(permissions, group_module_perms, is_group=True)
        self._process_module_permissions(permissions, user_module_perms, is_group=False)
        self._process_object_permissions(permissions, group_object_perms, is_group=True)
        self._process_object_permissions(permissions, user_object_perms, is_group=False)

        return permissions

    def _get_superuser_permissions(self) -> Dict:
        """
        Retrieves all permissions for a superuser.

        This method grants full access to all active modules and all available scopes.

        Returns:
            Dict: A dictionary containing full permissions for all active modules.
        """
        modules = Module.objects.filter(is_active=True)
        permissions = {}
        
        # Get all available scopes
        all_scopes = set(AccessScope.objects.values_list('code', flat=True))
        
        for module in modules:
            permissions[module.code] = {
                'module_type': module.permission_type,
                'scopes': all_scopes,
                'object_permissions': {},
                'all_objects_allowed': {scope: True for scope in all_scopes}
            }
        
        return permissions

    def _process_module_permissions(self, permissions: Dict, perms, *, is_group: bool) -> None:
        """
        Processes module-level permissions.

        This method handles both group and user permissions, with user permissions taking precedence.
        It populates the permissions dictionary with scopes and all-objects-allowed flags.

        Args:
            permissions (Dict): The permissions dictionary to update.
            perms: QuerySet of permission objects to process.
            is_group (bool): Flag indicating if processing group permissions (True) or user permissions (False).
        """
        for perm in perms:
            module_code = perm.module.code
            scope = perm.scope.code
            
            if module_code not in permissions:
                permissions[module_code] = self._create_empty_module_permission(perm.module.permission_type)
            
            permissions[module_code]['scopes'].add(scope)
            
            # For group permissions, only set if not already set by user
            # For user permissions, always override
            if not is_group or scope not in permissions[module_code]['all_objects_allowed']:
                if perm.allow_all_objects:
                    permissions[module_code]['all_objects_allowed'][scope] = True

    def _process_object_permissions(self, permissions: Dict, perms, *, is_group: bool) -> None:
        """
        Processes object-level permissions.

        This method handles both group and user object permissions. It adds specific object IDs
        to the permissions dictionary for each module and scope.

        Args:
            permissions (Dict): The permissions dictionary to update.
            perms: QuerySet of object permission objects to process.
            is_group (bool): Flag indicating if processing group permissions (True) or user permissions (False).
        """
        for perm in perms:
            module_code = perm.module.code
            scope = perm.scope.code
            
            if module_code not in permissions:
                permissions[module_code] = self._create_empty_module_permission(perm.module.permission_type)
            
            permissions[module_code]['scopes'].add(scope)
            
            # Skip if all objects are already allowed for this scope
            if not permissions[module_code]['all_objects_allowed'].get(scope):
                if scope not in permissions[module_code]['object_permissions']:
                    permissions[module_code]['object_permissions'][scope] = set()
                
                # Add the object permission
                permissions[module_code]['object_permissions'][scope].add(perm.object_id)

    @classmethod
    def check_permissions(
        cls,
        user_id: int,
        required_permissions: List[Tuple[str, str, Optional[int]]]
    ) -> Tuple[bool, Dict[Tuple[str, str], str]]:
        """
        Check multiple permissions at once.

        Args:
            user_id: The user ID to check permissions for
            required_permissions: List of tuples (module_code, scope, object_id=None)

        Returns:
            tuple: (bool, dict) - (all_permitted, {failed_permission: reason})
        """
        instance = cls.get_instance()

        # First check if user is superuser
        try:
            user = User.objects.get(id=user_id)
            if user.is_superuser:
                return True, {}  # Superuser has all permissions
        except User.DoesNotExist:
            pass  # Continue with normal permission check if user not found

        permissions = instance._get_user_permissions_impl(user_id)
        failed_permissions = {}

        for module_code, scope, object_id in required_permissions:
            if module_code not in permissions:
                failed_permissions[(module_code, scope)] = "Module not found"
                continue

            module_data = permissions[module_code]

            # Check if user has the required scope
            if scope not in module_data['scopes']:
                failed_permissions[(module_code, scope)] = "Scope not allowed"
                continue

            # For object-level permissions, check object access
            if object_id is not None and module_data['module_type'] == 'object':
                # First check if user has all_objects_allowed for this scope
                if not module_data['all_objects_allowed'].get(scope):
                    # If not, check specific object permissions
                    if (scope not in module_data['object_permissions'] or 
                        object_id not in module_data['object_permissions'][scope]):
                        failed_permissions[(module_code, scope)] = f"No access to object {object_id}"

        return not bool(failed_permissions), failed_permissions
