from functools import wraps
from rest_framework.exceptions import PermissionDenied
from .services.permission_service import PermissionService


def requires_permission(*permissions):
    """
    Decorator to check if user has required permissions.
    Supports both module and object-level permissions.

    Usage:
        # Single module permission
        @requires_permission('event', 'get')
        
        # Multiple module permissions
        @requires_permission(('event', 'get'), ('catalog', 'get'))
        
        # Object-level permission
        @requires_permission('form', 'get', lambda req: req.parser_context['kwargs'].get('pk'))
        
        # Multiple mixed permissions
        @requires_permission(
            ('event', 'get'),  # module permission
            ('form', 'update', lambda req: req.data.get('form_id'))  # object permission
        )
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(view_instance, request, *args, **kwargs):
            user_id = request.user.id

            # Normalize permissions to list of (module, scope, object_id) tuples
            required_permissions = []

            # Handle both single and multiple permission formats
            for perm in permissions:
                if len(perm) == 2:  # Module-level permission
                    module_code, scope = perm
                    required_permissions.append((module_code, scope, None))
                else:  # Object-level permission
                    module_code, scope, obj_id_func = perm
                    object_id = obj_id_func(request)
                    required_permissions.append((module_code, scope, object_id))

            # Check all permissions at once
            is_permitted, failed_permissions = PermissionService.check_permissions(
                user_id, required_permissions
            )

            if not is_permitted:
                error_messages = []
                for (module, scope), reason in failed_permissions.items():
                    error_messages.append(f"{module}.{scope}: {reason}")
                raise PermissionDenied(
                    f"Permission denied: {'; '.join(error_messages)}"
                )

            return view_func(view_instance, request, *args, **kwargs)
        return _wrapped_view
    return decorator
