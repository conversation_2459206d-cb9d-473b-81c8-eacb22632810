from django.apps import AppConfig


class AuthenticationConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'authentication'
    
    def ready(self):
        """
        Import and register signals when Django starts.
        This ensures all our permission cache invalidation signals are properly connected.
        """
        from .signals import permission_signals  # noqa F401
