from django.contrib.auth.models import AbstractUser
from django.db import models
from django.conf import settings


class User(AbstractUser):
    """
    Custom user model with additional fields
    """
    employee_id = models.CharField(
        max_length=50,
        unique=True,
        null=True,
        blank=True
    )
    department = models.CharField(max_length=100, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)
    last_login_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'mes_users'
        ordering = ['username']

    def __str__(self):
        return f"{self.username} ({self.employee_id})"


class Module(models.Model):
    """System modules and their permission requirements"""
    PERMISSION_TYPE_CHOICES = [
        ('none', 'No Restrictions'),
        ('module', 'Module Level'),
        ('object', 'Object Level')
    ]

    name = models.CharField(max_length=100)
    code = models.Char<PERSON><PERSON>(max_length=50, unique=True)
    description = models.TextField(blank=True)
    permission_type = models.CharField(
        max_length=20,
        choices=PERMISSION_TYPE_CHOICES,
        default='module'
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_modules'
    )

    class Meta:
        db_table = 'mes_modules'

    def __str__(self):
        return self.name


class AccessScope(models.Model):
    """Available permission scopes"""
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=50, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'mes_access_scopes'

    def __str__(self):
        return self.name


class Group(models.Model):
    """User groups for permission inheritance"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_groups'
    )

    class Meta:
        db_table = 'mes_groups'

    def __str__(self):
        return self.name


class UserGroup(models.Model):
    """User-Group association"""
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='mes_groups'
    )
    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='mes_group_users'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_user_groups'
    )

    class Meta:
        unique_together = ('user', 'group')
        db_table = 'mes_user_mes_groups'


class BasePermission(models.Model):
    """Abstract base class for permissions"""
    module = models.ForeignKey(Module, on_delete=models.CASCADE)
    scope = models.ForeignKey(AccessScope, on_delete=models.CASCADE)
    is_allowed = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='%(class)s_created'
    )

    class Meta:
        abstract = True


class UserModulePermission(BasePermission):
    """Module-level permissions for users"""
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='module_permissions'
    )
    allow_all_objects = models.BooleanField(
        default=False,
        help_text="If True and module type is 'object', user will have access to all objects for this scope"
    )

    class Meta:
        unique_together = ('user', 'module', 'scope')
        db_table = 'mes_user_module_permissions'


class GroupModulePermission(BasePermission):
    """Module-level permissions for groups"""
    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='module_permissions'
    )
    allow_all_objects = models.BooleanField(
        default=False,
        help_text="If True and module type is 'object', all group members will have access to all objects for this scope"
    )

    class Meta:
        unique_together = ('group', 'module', 'scope')
        db_table = 'mes_group_module_permissions'


class UserObjectPermission(BasePermission):
    """Object-level permissions for users"""
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='object_permissions'
    )
    object_id = models.IntegerField()

    class Meta:
        unique_together = ('user', 'module', 'object_id', 'scope')
        db_table = 'mes_user_object_permissions'


class GroupObjectPermission(BasePermission):
    """Object-level permissions for groups"""
    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='object_permissions'
    )
    object_id = models.IntegerField()

    class Meta:
        unique_together = ('group', 'module', 'object_id', 'scope')
        db_table = 'mes_group_object_permissions'
