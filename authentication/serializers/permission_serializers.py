from rest_framework import serializers
from ..models import Mo<PERSON>le, AccessScope, UserModulePermission, UserObjectPermission


class ModulePermissionSerializer(serializers.Serializer):
    module_code = serializers.CharField()
    module_name = serializers.CharField()
    permission_type = serializers.CharField()
    scopes = serializers.ListField(child=serializers.CharField())
    allowed_objects = serializers.DictField(
        child=serializers.ListField(child=serializers.IntegerField()),
        required=False,
        help_text="Dictionary mapping scope to list of object IDs the user has permission for. Only present if permission_type is 'object'"
    )
    object_scopes = serializers.DictField(
        child=serializers.ListField(child=serializers.CharField()),
        required=False,
        help_text="Dictionary mapping object IDs to list of allowed scopes. Only present if permission_type is 'object'"
    )

    class Meta:
        fields = ('module_code', 'module_name', 'permission_type', 'scopes', 
                 'allowed_objects', 'object_scopes')


class UserPermissionsSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()
    username = serializers.CharField()
    is_superuser = serializers.BooleanField()
    is_staff = serializers.BooleanField()
    modules = serializers.ListField(child=serializers.DictField())

    def validate_modules(self, value):
        """Validate module data structure"""
        for module in value:
            # Required fields
            if not all(key in module for key in ['module_code', 'module_name', 'permission_type', 'scopes']):
                raise serializers.ValidationError("Missing required module fields")

            # Validate permission type
            if module['permission_type'] not in ['module', 'object']:
                raise serializers.ValidationError("Invalid permission type")

            # For object-level permissions
            if module['permission_type'] == 'object':
                if 'allowed_objects' in module:
                    allowed_objects = module['allowed_objects']
                    for scope, objects in allowed_objects.items():
                        # Allow either a list of IDs or '*' for all objects
                        if not (isinstance(objects, list) or objects == '*'):
                            raise serializers.ValidationError(
                                f"Invalid allowed_objects format for scope {scope}. "
                                "Expected either a list of IDs or '*' for all objects."
                            )

                if 'object_scopes' in module:
                    object_scopes = module['object_scopes']
                    if not isinstance(object_scopes, dict):
                        raise serializers.ValidationError("object_scopes must be a dictionary")
                    for obj_id, scopes in object_scopes.items():
                        if not isinstance(scopes, list):
                            raise serializers.ValidationError(f"Invalid scopes format for object {obj_id}")

        return value

    class Meta:
        fields = ('user_id', 'username', 'is_superuser', 'is_staff', 'modules')
