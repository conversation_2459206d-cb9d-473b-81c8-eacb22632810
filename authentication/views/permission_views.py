from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON><PERSON>icated

from ..models import Module
from ..serializers.permission_serializers import UserPermissionsSerializer
from ..services.permission_service import PermissionService


class UserPermissionsView(APIView):
    """
    API endpoint that returns all permissions for the authenticated user.
    This includes both module-level and object-level permissions,
    taking into account both direct user permissions and group permissions.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get all permissions for the authenticated user.
        
        Returns:
            A structured response containing:
            - User information
            - Module permissions
            - Object-level permissions where applicable
            
        Example Response:
        {
            "user_id": 1,
            "username": "john.doe",
            "is_superuser": false,
            "is_staff": true,
            "modules": [
                {
                    "module_code": "event",
                    "module_name": "Events",
                    "permission_type": "module",
                    "scopes": ["get", "create", "update"]
                },
                {
                    "module_code": "form",
                    "module_name": "Forms",
                    "permission_type": "object",
                    "scopes": ["get", "update", "create"],
                    "allowed_objects": {
                        "get": "*",
                        "update": [1, 2],
                        "create": [1]
                    },
                    "object_scopes": {
                        "*": ["get"],
                        "1": ["update", "create"],
                        "2": ["update"]
                    }
                }
            ]
        }
        """
        user = request.user
        modules_data = []

        # Get cached permissions using PermissionService
        permissions = PermissionService._get_user_permissions(user.id)
        
        # Get all active modules with names
        modules = {
            module.code: module for module in Module.objects.filter(is_active=True)
        }

        for module_code, module_perms in permissions.items():
            module = modules.get(module_code)
            if not module:
                continue

            module_data = {
                'module_code': module_code,
                'module_name': module.name,
                'permission_type': module_perms['module_type'],
                'scopes': list(module_perms['scopes'])  # Convert set to list for serialization
            }

            # Add allowed objects for object-level permissions
            if module_perms['module_type'] == 'object':
                allowed_objects = {}
                object_scopes = {}

                # First process all_objects_allowed scopes
                all_objects_scopes = []
                for scope in module_perms['scopes']:
                    if module_perms['all_objects_allowed'].get(scope):
                        allowed_objects[scope] = '*'
                        all_objects_scopes.append(scope)

                # Add '*' key to object_scopes if there are any all_objects_allowed scopes
                if all_objects_scopes:
                    object_scopes['*'] = all_objects_scopes

                # Then process specific object permissions
                for scope in module_perms['scopes']:
                    # Skip if this scope allows all objects
                    if scope not in module_perms['all_objects_allowed']:
                        objects = module_perms['object_permissions'].get(scope, set())
                        if objects:  # Only include scopes with object permissions
                            allowed_objects[scope] = list(objects)  # Convert set to list
                            # Build object_scopes mapping
                            for obj_id in objects:
                                str_obj_id = str(obj_id)
                                if str_obj_id not in object_scopes:
                                    object_scopes[str_obj_id] = []
                                object_scopes[str_obj_id].append(scope)

                if allowed_objects:  # Only include if there are object permissions
                    module_data['allowed_objects'] = allowed_objects
                    module_data['object_scopes'] = object_scopes

            modules_data.append(module_data)

        # Prepare the complete response
        data = {
            'user_id': user.id,
            'username': user.username,
            'is_superuser': user.is_superuser,
            'is_staff': user.is_staff,
            'modules': modules_data
        }

        serializer = UserPermissionsSerializer(data=data)
        serializer.is_valid(raise_exception=True)

        return Response(serializer.data)
