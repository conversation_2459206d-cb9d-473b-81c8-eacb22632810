from typing import List, Optional, Set
from django.core.cache import caches
from django.core.cache.backends.base import DEFAULT_TIMEOUT
from contextlib import contextmanager
import threading
import time

class CacheLock:
    """Thread-safe lock implementation using cache"""
    def __init__(self, cache, key: str, timeout: int = 60):
        self.cache = cache
        self.key = f"lock_{key}"
        self.timeout = timeout

    def acquire(self) -> bool:
        return self.cache.add(self.key, 1, self.timeout)

    def release(self) -> None:
        self.cache.delete(self.key)

@contextmanager
def cache_lock(cache, key: str, timeout: int = 60):
    """Context manager for cache-based locking"""
    lock = CacheLock(cache, key, timeout)
    try:
        acquired = False
        # Try to acquire lock with exponential backoff
        for i in range(5):  # Maximum 5 attempts
            if lock.acquire():
                acquired = True
                break
            time.sleep(0.1 * (2 ** i))  # Exponential backoff
        if not acquired:
            raise TimeoutError(f"Could not acquire lock for {key}")
        yield
    finally:
        if acquired:
            lock.release()

class PermissionCacheManager:
    """Manages caching of user permissions with versioning support"""
    
    def __init__(self, cache_name: str = 'default', timeout: int = DEFAULT_TIMEOUT):
        self.cache = caches[cache_name]
        self.timeout = timeout
        self.version_prefix = "perm_version_"
        self.data_prefix = "perm_data_"

    def get_cache_key(self, user_id: int) -> str:
        """Get the cache key for user permissions including version"""
        version = self.get_version(user_id)
        return f"{self.data_prefix}{user_id}_v{version}"

    def get_version(self, user_id: int) -> int:
        """Get the current version number for a user's permissions"""
        return self.cache.get(f"{self.version_prefix}{user_id}", 1)

    def increment_version(self, user_id: int) -> None:
        """Increment the version number for a user's permissions"""
        with cache_lock(self.cache, f"perm_{user_id}"):
            version_key = f"{self.version_prefix}{user_id}"
            current = self.cache.get(version_key, 1)
            self.cache.set(version_key, current + 1, timeout=None)  # Version numbers don't expire

    def bulk_increment_version(self, user_ids: Set[int]) -> None:
        """Increment version numbers for multiple users efficiently"""
        for user_id in user_ids:
            print(f"__bulk_increment_version__user_id: {user_id}")
            self.increment_version(user_id)

    def get_permissions(self, user_id: int) -> Optional[dict]:
        """Get cached permissions for a user"""
        cache_key = self.get_cache_key(user_id)
        return self.cache.get(cache_key)

    def set_permissions(self, user_id: int, permissions: dict) -> None:
        """Set permissions in cache for a user"""
        cache_key = self.get_cache_key(user_id)
        self.cache.set(cache_key, permissions, timeout=self.timeout)

    def invalidate_user(self, user_id: int) -> None:
        """Invalidate cache for a single user"""
        self.increment_version(user_id)

    def invalidate_users(self, user_ids: Set[int]) -> None:
        """Invalidate cache for multiple users"""
        self.bulk_increment_version(user_ids)

    def clear_all(self) -> None:
        """Clear all permission-related cache entries"""
        # Note: This is a potentially expensive operation
        self.cache.delete_pattern(f"{self.version_prefix}*")
        self.cache.delete_pattern(f"{self.data_prefix}*")
