from typing import Dict, Any
from jsonschema import validate, ValidationError as JsonSchemaError
from django.core.exceptions import ValidationError


# Schema for form field actions
ACTION_SCHEMA = {
    "type": "object",
    "required": ["handler", "action_type"],
    "properties": {
        "handler": {"type": "string"},
        "action_type": {
            "type": "string",
            "enum": ["onChange", "onBlur", "onFocus", "onClick"]
        }
    }
}

# Schema for individual form fields
FORM_FIELD_SCHEMA = {
    "type": "object",
    "required": ["name", "type", "label"],
    "properties": {
        "name": {"type": "string"},
        "type": {
            "type": "string",
            "enum": ["text", "radio", "checkbox", "select", "number", "date", "datetime", "multi-select", "component"]
        },
        "label": {"type": "string"},
        "regex": {"type": "string"},
        "width": {
            "type": ["object", "null"],
            "properties": {
                "desktop": {"type": ["string", "null"]},
                "tablet": {"type": ["string", "null"]},
                "mobile": {"type": ["string", "null"]}
            },
            "additionalProperties": False
        },
        "height": {
            "type": ["object", "null"],
            "properties": {
                "desktop": {"type": ["string", "null"]},
                "tablet": {"type": ["string", "null"]},
                "mobile": {"type": ["string", "null"]}
            },
            "additionalProperties": False
        },
        "actions": {
            "type": "array",
            "items": ACTION_SCHEMA
        },
        "options": {
            "type": "array",
            "items": {
                "type": "object",
                "required": ["label", "value"],
                "properties": {
                    "label": {"type": "string"},
                    "value": {"type": ["string", "number", "boolean"]}
                }
            }
        },
        "position": {"type": "integer"},
        "readonly": {"type": "boolean"},
        "required": {"type": "boolean"},
        "alignment": {"type": "string"},
        "isVisible": {"type": "boolean"},
        "marginRight": {
            "type": ["object", "null"],
            "properties": {
                "desktop": {"type": ["string", "null"]},
                "tablet": {"type": ["string", "null"]},
                "mobile": {"type": ["string", "null"]}
            },
            "additionalProperties": False
        },
        "placeholder": {"type": "string"},
        "used_in_grid": {"type": "boolean"},
        "sortable": {"type": "boolean"},
        "filterable": {"type": "boolean"},
        "max_value": {"type": ["number", "string"]},  # Can be number or date string
        "min_value": {"type": ["number", "string"]}   # Can be number or date string
    },
    "allOf": [
        {
            "if": {
                "properties": {"type": {"const": "number"}},
                "required": ["type"]
            },
            "then": {
                "properties": {
                    "max_value": {"type": "number"},
                    "min_value": {"type": "number"}
                }
            }
        },
        {
            "if": {
                "properties": {"type": {"enum": ["date", "datetime"]}},
                "required": ["type"]
            },
            "then": {
                "properties": {
                    "max_value": {"type": "string", "format": "date-time"},
                    "min_value": {"type": "string", "format": "date-time"}
                }
            }
        }
    ]
}

# Schema for the entire form configuration
FORM_SCHEMA = {
    "type": "object",
    "required": ["form_fields"],
    "properties": {
        "form_fields": {
            "type": "array",
            "items": FORM_FIELD_SCHEMA
        }
    }
}


def validate_form_schema(schema_data: Dict[str, Any]) -> None:
    """
    Validate form schema against the defined JSON schema.

    Args:
        schema_data: The form schema data to validate

    Raises:
        ValidationError: If schema validation fails
    """
    try:
        # Validate against the base schema first
        validate(instance=schema_data, schema=FORM_SCHEMA)

        # Additional validation for min_value and max_value
        if 'form_fields' in schema_data:
            for field in schema_data['form_fields']:
                field_type = field.get('type')
                min_value = field.get('min_value')
                max_value = field.get('max_value')

                # Skip if no min/max values are defined
                if min_value is None and max_value is None:
                    continue

                # Validate min/max relationship if both are present
                if min_value is not None and max_value is not None:
                    if field_type == 'number':
                        if float(min_value) > float(max_value):
                            raise ValidationError(
                                f"Field '{field.get('name')}': min_value ({min_value}) cannot be greater than max_value ({max_value})"
                            )
                    elif field_type in ['date', 'datetime']:
                        from datetime import datetime
                        min_date = datetime.fromisoformat(min_value.replace('Z', '+00:00'))
                        max_date = datetime.fromisoformat(max_value.replace('Z', '+00:00'))
                        if min_date > max_date:
                            raise ValidationError(
                                f"Field '{field.get('name')}': min_value ({min_value}) cannot be greater than max_value ({max_value})"
                            )

    except JsonSchemaError as e:
        path = " -> ".join(str(p) for p in e.path)
        raise ValidationError(f"Invalid form schema at {path}: {e.message}")


# Example schema matching the provided sample
EXAMPLE_SCHEMA = {
    "form_fields": [
        {
            "name": "serial_number",
            "type": "text",
            "label": "Serial number",
            "regex": "",
            "width": "45%",
            "actions": [
                {
                    "handler": "handleBlur",
                    "action_type": "onBlur"
                }
            ],
            "options": [],
            "position": 5,
            "readonly": False,
            "required": True,
            "alignment": "0px",
            "isVisible": True,
            "marginRight": "250px",
            "placeholder": "Enter Serial Number",
            "used_in_grid": False
        },
        {
            "name": "form_status",
            "type": "radio",
            "label": "Weather Pass or failed ?",
            "regex": "",
            "width": "100%",
            "actions": [
                {
                    "handler": "handleChange",
                    "action_type": "onChange"
                }
            ],
            "options": [
                {
                    "label": "Pass",
                    "value": "pass"
                },
                {
                    "label": "Fail",
                    "value": "fail"
                }
            ],
            "position": 9,
            "readonly": False,
            "required": True,
            "alignment": "0px",
            "isVisible": True,
            "marginRight": "0px",
            "placeholder": "",
            "used_in_grid": False
        }
    ]
}
