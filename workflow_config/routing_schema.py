from typing import Dict, Any, List, Optional, Union
from jsonschema import validate, ValidationError as JsonSchemaError
from django.core.exceptions import ValidationError
from django.db import models


# Schema for node types in components
NODE_TYPE_SCHEMA = {
    "type": "string",
    "enum": ["machine", "scanner", "rework_station", "computer"]
}

# Schema for delay_time in fifo_config
DELAY_TIME_SCHEMA = {
    "type": "object",
    "properties": {
        "min": {"type": "integer"},
        "max": {"type": "integer"}
    }
}

# Schema for fifo_config
FIFO_CONFIG_SCHEMA = {
    "type": "object",
    "properties": {
        "delay_time": DELAY_TIME_SCHEMA
    }
}

# Schema for component properties
COMPONENT_SCHEMA = {
    "type": "object",
    "required": ["node_type"],
    "properties": {
        "node_type": NODE_TYPE_SCHEMA,
        "event_required": {"type": "boolean"},
        "pre_process": {"type": "boolean"},
        "post_process": {"type": "boolean"},
        "allowed_producers": {
            "type": "array",
            "items": {"type": "string"}
        },
        "comment": {"type": "string"},
        "fifo_config": FIFO_CONFIG_SCHEMA
    }
}

# Schema for route conditions
CONDITION_SCHEMA = {
    "type": "object",
    "required": ["operator", "left", "right", "target"],
    "properties": {
        "operator": {
            "type": "string",
            "enum": ["equals", "not_equals", "greater_than", "less_than", "contains"]
        },
        "left": {
            "type": "object",
            "required": ["type"],
            "properties": {
                "type": {"type": "string", "enum": ["property", "value"]},
                "node": {"type": "string"},
                "path": {"type": "string"}
            }
        },
        "right": {
            "type": "object",
            "required": ["type"],
            "properties": {
                "type": {"type": "string", "enum": ["property", "value"]},
                "value": {"type": ["string", "number", "boolean", "null"]}
            }
        },
        "target": {"type": "string"},
        "route_type": {"type": "string", "enum": ["main", "rework"]}
    }
}

# Schema for towards property in connections
TOWARDS_SCHEMA = {
    "type": "object",
    "properties": {
        "default": {"type": "string"},
        "route_type": {"type": "string", "enum": ["main", "rework"]},
        "conditions": {
            "type": ["array", "null"],
            "items": CONDITION_SCHEMA
        },
        "end": {"type": "boolean"}
    }
}

# Schema for pre/post process route
PROCESS_ROUTE_SCHEMA = {
    "type": "object",
    "required": ["start", "end", "route"],
    "properties": {
        "start": {"type": "string"},
        "end": {"type": "string"},
        "route": {
            "type": "object",
            "additionalProperties": {
                "type": "object",
                "properties": {
                    "to": {"type": "string"},
                    "end": {"type": "boolean"},
                    "event_required": {"type": "boolean"}
                }
            }
        }
    }
}

# Schema for connection in route
CONNECTION_SCHEMA = {
    "type": "object",
    "properties": {
        "start": {"type": "boolean"},
        "towards": TOWARDS_SCHEMA,
        "towards_many": {
            "type": "array",
            "items": {"type": "string"}
        },
        "pre_process": PROCESS_ROUTE_SCHEMA,
        "post_process": PROCESS_ROUTE_SCHEMA
    }
}

# Schema for the entire routing configuration
ROUTING_SCHEMA = {
    "type": "object",
    "required": ["routing_schema"],
    "properties": {
        "routing_schema": {
            "type": "object",
            "required": ["components", "route"],
            "properties": {
                "components": {
                    "type": "object",
                    "additionalProperties": COMPONENT_SCHEMA
                },
                "route": {
                    "type": "object",
                    "required": ["start", "end", "connections"],
                    "properties": {
                        "start": {"type": "string"},
                        "end": {"type": "string"},
                        "connections": {
                            "type": "object",
                            "additionalProperties": CONNECTION_SCHEMA
                        }
                    }
                }
            }
        }
    }
}


def validate_routing_schema(schema_data: Dict[str, Any]) -> None:
    """
    Validate routing schema against the defined JSON schema.

    Args:
        schema_data: The routing schema data to validate

    Raises:
        ValidationError: If schema validation fails
    """
    try:
        # Validate against the base schema first
        validate(instance=schema_data, schema=ROUTING_SCHEMA)

        # Get the routing schema
        routing_schema = schema_data.get("routing_schema", {})

        # Additional validation for components and routes
        components = routing_schema.get("components", {})
        route = routing_schema.get("route", {})

        # Collect all node IDs for batch validation
        all_node_ids = set(components.keys())

        # Import ProcessBlock model here to avoid circular imports
        from workflow_config.models import ProcessBlock
        from catalog.models import Scanner

        # Fetch all valid process block codes in a single query
        valid_process_block_codes = set(ProcessBlock.objects.values_list('code', flat=True))

        # Fetch all valid scanner codes in a single query
        valid_scanner_codes = set(Scanner.objects.values_list('code', flat=True))

        # Combine both sets for validation
        valid_codes = valid_process_block_codes.union(valid_scanner_codes)

        # Validate all node IDs correspond to valid ProcessBlock or Scanner codes
        invalid_nodes = [node_id for node_id in all_node_ids if node_id not in valid_codes]
        if invalid_nodes:
            raise ValidationError(f"The following nodes are not valid ProcessBlock or Scanner codes: {', '.join(invalid_nodes)}")

        # Validate start and end nodes exist in components
        start_node = route.get("start")
        end_node = route.get("end")

        if start_node and start_node not in components:
            raise ValidationError(f"Start node '{start_node}' not found in components")

        if end_node and end_node not in components:
            raise ValidationError(f"End node '{end_node}' not found in components")

        # Validate connections reference valid components
        connections = route.get("connections", {})
        for node_id, connection in connections.items():
            if node_id not in components:
                raise ValidationError(f"Connection node '{node_id}' not found in components")

            # Validate towards references
            towards = connection.get("towards", {})
            default_target = towards.get("default")

            if default_target and default_target != "end" and default_target not in components:
                raise ValidationError(f"Default target '{default_target}' for node '{node_id}' not found in components")

            # Validate conditions targets
            conditions = towards.get("conditions", [])
            if conditions:
                for i, condition in enumerate(conditions):
                    target = condition.get("target")
                    if target and target not in components:
                        raise ValidationError(f"Condition target '{target}' for node '{node_id}' not found in components")

            # Validate towards_many references
            towards_many = connection.get("towards_many", [])
            for target in towards_many:
                if target not in components and not target.endswith('`'):
                    raise ValidationError(f"Target '{target}' in towards_many for node '{node_id}' not found in components")

            # Validate pre-process routes
            pre_process_route = connection.get("pre_process")
            if pre_process_route:
                process_start = pre_process_route.get("start")
                process_end = pre_process_route.get("end")

                if process_start and process_start not in components:
                    raise ValidationError(f"Pre-process start node '{process_start}' not found in components")

                if process_end and process_end not in components:
                    raise ValidationError(f"Pre-process end node '{process_end}' not found in components")

                # Validate process route connections
                process_route_connections = pre_process_route.get("route", {})
                for proc_node_id, proc_connection in process_route_connections.items():
                    if proc_node_id not in components:
                        raise ValidationError(f"Pre-process route node '{proc_node_id}' not found in components")

                    proc_to = proc_connection.get("to")
                    if proc_to and proc_to != "end" and proc_to not in components:
                        raise ValidationError(f"Pre-process route target '{proc_to}' for node '{proc_node_id}' not found in components")

            # Validate post-process routes
            post_process_route = connection.get("post_process")
            if post_process_route:
                process_start = post_process_route.get("start")
                process_end = post_process_route.get("end")

                if process_start and process_start not in components:
                    raise ValidationError(f"Post-process start node '{process_start}' not found in components")

                if process_end and process_end not in components:
                    raise ValidationError(f"Post-process end node '{process_end}' not found in components")

                # Validate process route connections
                process_route_connections = post_process_route.get("route", {})
                for proc_node_id, proc_connection in process_route_connections.items():
                    if proc_node_id not in components:
                        raise ValidationError(f"Post-process route node '{proc_node_id}' not found in components")

                    proc_to = proc_connection.get("to")
                    if proc_to and proc_to != "end" and proc_to not in components:
                        raise ValidationError(f"Post-process route target '{proc_to}' for node '{proc_node_id}' not found in components")

    except JsonSchemaError as e:
        path = " -> ".join(str(p) for p in e.path)
        raise ValidationError(f"Invalid routing schema at {path}: {e.message}")
