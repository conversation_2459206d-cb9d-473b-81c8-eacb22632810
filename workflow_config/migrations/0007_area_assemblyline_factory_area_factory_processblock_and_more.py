# Generated by Django 5.1 on 2025-03-02 18:09

import django.db.models.deletion
import workflow_config.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0004_product_type_id'),
        ('workflow_config', '0006_alter_formconfig_code_alter_formconfig_description'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Area',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('atype', models.CharField(choices=[('sf', 'Shop Floor'), ('wh', 'Warehouse')], max_length=50)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_areas', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'mes_areas',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='AssemblyLine',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('area', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='lines', to='workflow_config.area')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_assembly_lines', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'mes_assembly_lines',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Factory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('org_id', models.CharField(default=workflow_config.models.generate_unique_org_id, max_length=50, unique=True)),
                ('location', models.CharField(max_length=100)),
                ('established_date', models.DateField()),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='managed_factories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'mes_factory',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='area',
            name='factory',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='areas', to='workflow_config.factory'),
        ),
        migrations.CreateModel(
            name='ProcessBlock',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('area', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='process_blocks', to='workflow_config.area')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_process_blocks', to=settings.AUTH_USER_MODEL)),
                ('line_loc', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='process_blocks', to='workflow_config.assemblyline')),
            ],
            options={
                'db_table': 'mes_process_blocks',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='MasterProgram',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_master_programs', to=settings.AUTH_USER_MODEL)),
                ('process_block', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='master_programs', to='workflow_config.processblock')),
            ],
            options={
                'db_table': 'mes_master_program',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='formconfig',
            name='process_block',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='forms', to='workflow_config.processblock'),
        ),
        migrations.CreateModel(
            name='Routing',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('schema', models.JSONField()),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_routings', to=settings.AUTH_USER_MODEL)),
                ('product', models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='routings', to='catalog.product')),
            ],
            options={
                'db_table': 'mes_routing',
            },
        ),
        migrations.CreateModel(
            name='sop',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_sops', to=settings.AUTH_USER_MODEL)),
                ('process_block', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='sops', to='workflow_config.processblock')),
            ],
            options={
                'db_table': 'mes_sop',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='MasterProgramProductParam',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('params', models.JSONField()),
                ('master_program', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='product_params', to='workflow_config.masterprogram')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='catalog.product')),
            ],
            options={
                'db_table': 'mes_master_program_product_param',
                'unique_together': {('master_program', 'product')},
            },
        ),
    ]
