# Generated by Django 5.1 on 2025-03-03 16:54

import django.db.models.deletion
import workflow_config.routing_schema
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0004_product_type_id'),
        ('workflow_config', '0007_area_assemblyline_factory_area_factory_processblock_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='routing',
            name='schema',
            field=models.J<PERSON><PERSON>ield(validators=[workflow_config.routing_schema.validate_routing_schema]),
        ),
        migrations.CreateModel(
            name='RoutingExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_number', models.CharField(max_length=100, unique=True)),
                ('executed_sequence', models.JSONField(default=list)),
                ('next_executable', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='catalog.product')),
            ],
            options={
                'db_table': 'mes_routing_execution',
            },
        ),
    ]
