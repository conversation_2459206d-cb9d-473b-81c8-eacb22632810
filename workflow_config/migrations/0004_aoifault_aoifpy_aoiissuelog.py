# Generated by Django 5.1 on 2025-01-01 07:03

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workflow_config', '0003_alter_formconfig_form_schema'),
    ]

    operations = [
        migrations.CreateModel(
            name='AOIFault',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('model', models.CharField(max_length=50)),
                ('total_cards_produced', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('total_fail_cards', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('total_real_faults', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('rejection_percentage', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'mes_aoi_fault',
            },
        ),
        migrations.CreateModel(
            name='AOIFPY',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('model', models.CharField(max_length=50)),
                ('fpy_percentage', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'mes_aoi_fpy',
            },
        ),
        migrations.CreateModel(
            name='AOIIssueLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('product', models.CharField(max_length=50)),
                ('serial_number', models.CharField(max_length=100)),
                ('component_part_no', models.CharField(max_length=50)),
                ('location', models.CharField(max_length=50)),
                ('defect', models.CharField(max_length=100)),
                ('related_issue', models.CharField(max_length=100)),
                ('machine', models.CharField(max_length=20)),
                ('tool', models.CharField(max_length=20)),
                ('hydra_tool', models.CharField(blank=True, max_length=20, null=True)),
                ('package_type', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'mes_aoi_issue_log',
            },
        ),
    ]
