# Generated by Django 5.1 on 2025-03-15 19:11

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workflow_config', '0011_aoidailyyield_aoirejection_delete_aoifault_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='aoidailyyield',
            name='fpy_percentage',
            field=models.DecimalField(decimal_places=4, max_digits=7, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
        migrations.AlterField(
            model_name='aoidailyyield',
            name='rejection_percentage',
            field=models.DecimalField(decimal_places=4, max_digits=7, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
    ]
