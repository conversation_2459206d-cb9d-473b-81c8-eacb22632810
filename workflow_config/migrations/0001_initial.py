# Generated by Django 5.1 on 2024-11-30 19:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ReferenceCategory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name_plural': 'Reference Categories',
                'db_table': 'mes_reference_categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='FormConfig',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('form_schema', models.JSONField()),
                ('version', models.PositiveIntegerField(default=1)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.EmailField(max_length=254)),
            ],
            options={
                'db_table': 'mes_form_config',
                'ordering': ['-id', '-version'],
                'unique_together': {('name', 'version')},
            },
        ),
        migrations.CreateModel(
            name='ReferenceValue',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=50)),
                ('value', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', to='workflow_config.referencecategory')),
            ],
            options={
                'db_table': 'mes_reference_values',
                'ordering': ['category', 'sort_order', 'value'],
                'unique_together': {('category', 'code')},
            },
        ),
    ]
