# Generated by Django 5.1 on 2025-03-08 18:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0004_product_type_id'),
        ('workflow_config', '0008_alter_routing_schema_routingexecution'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='routing',
            name='product',
        ),
        migrations.CreateModel(
            name='RoutingProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='catalog.product')),
                ('routing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='workflow_config.routing')),
            ],
            options={
                'db_table': 'mes_routing_product',
                'unique_together': {('routing', 'product')},
            },
        ),
        migrations.AddField(
            model_name='routing',
            name='products',
            field=models.ManyToManyField(related_name='routings', through='workflow_config.RoutingProduct', to='catalog.product'),
        ),
    ]
