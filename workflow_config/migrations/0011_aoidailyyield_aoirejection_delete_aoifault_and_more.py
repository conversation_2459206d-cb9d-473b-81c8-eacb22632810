# Generated by Django 5.1 on 2025-03-15 18:11

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workflow_config', '0010_routingexecution_is_valid'),
    ]

    operations = [
        migrations.CreateModel(
            name='AOIDailyYield',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sr_no', models.IntegerField(blank=True, null=True)),
                ('date', models.DateField()),
                ('model', models.CharField(blank=True, max_length=100, null=True)),
                ('total_cards_produced', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('total_fail_cards', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('total_real_faults', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('total_pass_cards', models.IntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('rejection_percentage', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('fpy_percentage', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'mes_aoi_daily_yield',
            },
        ),
        migrations.CreateModel(
            name='AOIRejection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sr_no', models.IntegerField(blank=True, null=True)),
                ('date', models.DateField()),
                ('model', models.CharField(blank=True, max_length=100, null=True)),
                ('serial_number', models.CharField(blank=True, max_length=100, null=True)),
                ('component_part_no', models.CharField(blank=True, max_length=100, null=True)),
                ('location', models.CharField(blank=True, max_length=100, null=True)),
                ('defect', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'mes_aoi_rejection',
            },
        ),
        migrations.DeleteModel(
            name='AOIFault',
        ),
        migrations.DeleteModel(
            name='AOIFPY',
        ),
        migrations.DeleteModel(
            name='AOIIssueLog',
        ),
    ]
