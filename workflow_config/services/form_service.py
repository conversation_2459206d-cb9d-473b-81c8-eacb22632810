from dataclasses import dataclass
from typing import Dict, Any, <PERSON><PERSON>, List, Optional
from django.core.exceptions import ValidationError
from operation.models import ManufacturingEvent
from ..models import FormConfig
import re
from typing import Dict, Optional, Tuple, List
from rest_framework.exceptions import ValidationError


class FormService:
    @staticmethod
    def validate_event_data(form_config: FormConfig, event_data: Dict[str, Any]) -> <PERSON><PERSON>[bool, List[Dict[str, Any]]]:
        """
        Validate event data against form configuration schema.
        Returns a tuple of (is_valid, validation_errors)

        Args:
            form_config: FormConfig instance containing the schema
            event_data: Event data to validate

        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (is_valid, list of validation errors)
        """
        validation_errors = []

        def validate_field(field_schema: Dict[str, Any], field_value: Any, field_path: str) -> None:
            """Validate a single field against its schema"""
            # Required field validation
            if field_schema.get('required', False) and field_value is None:
                validation_errors.append({
                    'field': field_path,
                    'error': 'This field is required',
                    'value': field_value
                })
                return

            # Skip further validation if value is None and field is not required
            if field_value is None:
                return

            # Type validation
            field_type = field_schema.get('type')
            if field_type == 'number' and not isinstance(field_value, (int, float)):
                validation_errors.append({
                    'field': field_path,
                    'error': f'Expected number, got {type(field_value).__name__}',
                    'value': field_value
                })
            elif field_type == 'boolean' and not isinstance(field_value, bool):
                validation_errors.append({
                    'field': field_path,
                    'error': f'Expected boolean, got {type(field_value).__name__}',
                    'value': field_value
                })
            # elif field_type in ['text', 'radio', 'select'] and not isinstance(field_value, str):
            #     validation_errors.append({
            #         'field': field_path,
            #         'error': f'Expected string, got {type(field_value).__name__}',
            #         'value': field_value
            #     })

            # Min/Max value validation
            def validate_numeric_value(value, field_path, min_value=None, max_value=None):
                """Helper function to validate numeric values against min/max constraints"""
                try:
                    numeric_value = float(value)
                    if min_value is not None:
                        min_val = float(min_value)
                        if numeric_value < min_val:
                            error_msg = f'Value must be greater than or equal to {min_val}'
                            raise ValidationError({
                                'field': field_path,
                                'error': error_msg,
                                'value': value
                            })
                    if max_value is not None:
                        print("validate_numeric_value for max_value")
                        max_val = float(max_value)
                        print(f"validate_numeric_value: max_val {max_val}, numeric_value {numeric_value}")
                        if numeric_value > max_val:
                            error_msg = f'Value must be less than or equal to {max_val}'
                            raise ValidationError({
                                'field': field_path,
                                'error': error_msg,
                                'value': value
                            })
                    return True
                except (TypeError, ValueError):
                    return False

            if field_type == 'number':
                print("validate_numeric_value for number")
                validate_numeric_value(
                    field_value,
                    field_path,
                    field_schema.get('min_value'),
                    field_schema.get('max_value')
                )
            elif field_type in ['text', 'radio', 'select']:
                # Try numeric validation only if min_value or max_value is specified
                if 'min_value' in field_schema or 'max_value' in field_schema:
                    # Only validate if the string value looks like a number
                    if isinstance(field_value, str) and field_value.replace('.', '', 1).replace('-', '', 1).isdigit():
                        validate_numeric_value(
                            field_value,
                            field_path,
                            field_schema.get('min_value'),
                            field_schema.get('max_value')
                        )
            elif field_type in ['date', 'datetime']:
                from datetime import datetime
                try:
                    value_date = datetime.fromisoformat(str(field_value).replace('Z', '+00:00'))
                    if 'min_value' in field_schema:
                        min_date = datetime.fromisoformat(str(field_schema['min_value']).replace('Z', '+00:00'))
                        if value_date < min_date:
                            error_msg = f'Date must be after {field_schema["min_value"]}'
                            raise ValidationError({
                                'field': field_path,
                                'error': error_msg,
                                'value': field_value
                            })
                    if 'max_value' in field_schema:
                        max_date = datetime.fromisoformat(str(field_schema['max_value']).replace('Z', '+00:00'))
                        if value_date > max_date:
                            error_msg = f'Date must be before {field_schema["max_value"]}'
                            raise ValidationError({
                                'field': field_path,
                                'error': error_msg,
                                'value': field_value
                            })
                except (TypeError, ValueError):
                    # Skip min/max validation if value cannot be parsed as date
                    pass

            # Options validation for radio and select
            if field_type in ['radio', 'select'] and field_schema.get('options'):
                valid_values = [opt['value'] for opt in field_schema['options']]
                if field_value not in valid_values:
                    validation_errors.append({
                        'field': field_path,
                        'error': f'Invalid choice. Valid choices are: {", ".join(str(v) for v in valid_values)}',
                        'value': field_value
                    })

            # Regex validation
            if field_schema.get('regex') and isinstance(field_value, str):
                try:
                    if not re.match(field_schema['regex'], field_value):
                        validation_errors.append({
                            'field': field_path,
                            'error': 'Value does not match the required pattern',
                            'value': field_value
                        })
                except re.error:
                    validation_errors.append({
                        'field': field_path,
                        'error': 'Invalid regex pattern in schema',
                        'value': field_value
                    })

        # Validate each field in the schema
        for field in form_config.form_schema.get('form_fields', []):
            field_name = field['name']
            field_value = event_data.get(field_name)
            validate_field(field, field_value, field_name)

            # Handle nested fields
            if 'fields' in field:
                for nested_field in field['fields']:
                    nested_name = f"{field_name}.{nested_field['name']}"
                    nested_value = event_data.get(nested_name) if isinstance(event_data, dict) else None
                    validate_field(nested_field, nested_value, nested_name)

        return len(validation_errors) == 0, validation_errors

    @staticmethod
    def create_manufacturing_event(form_config: FormConfig, form_data: Dict[str, Any], user) -> ManufacturingEvent:
        """
        Create a manufacturing event after validating form data
        """
        # Validate form data
        is_valid, errors = FormService.validate_event_data(form_config, form_data.event_data)

        # Create event
        event = ManufacturingEvent.objects.create(
            **form_data,
            form=form_config,
            created_by=user,
            validation_status='valid' if is_valid else 'invalid',
            validation_errors=errors
        )

        return event

    @staticmethod
    def get_form_history(form_config: FormConfig) -> Dict[str, Any]:
        """
        Get form submission history with statistics
        """
        events = ManufacturingEvent.objects.filter(form=form_config)

        # Calculate statistics
        total_submissions = events.count()
        valid_submissions = events.filter(validation_status='valid').count()
        invalid_submissions = events.filter(validation_status='invalid').count()

        return {
            'total_submissions': total_submissions,
            'valid_submissions': valid_submissions,
            'invalid_submissions': invalid_submissions,
            'latest_events': events.order_by('-created_at')[:10]
        }


def parse_serial_number(serial_number: str) -> Dict[str, Any]:
    """
    Parse serial number in format: HE317171-35.12#D9241000010001
    Returns dict with:
    - part_code: everything before '#'
    - work_order: 7 digits before last 4 digits
    - board: last 4 digits as sequence number
    - validation_errors: list of validation errors if any
    """
    validation_errors = []
    parsed_data = {
        'part_code': '',
        'work_order': '',
        'board': 0,
        'validation_errors': validation_errors
    }

    try:
        part_code, remainder = serial_number.split('#') # Split by '#' to get part number
        board_sequence = remainder[-4:] # Get last 4 digits as board sequence
        work_order = remainder[-11:-4] # Get previous 7 digits as work order
        board_number = int(board_sequence) # Convert board sequence to integer, removing leading zeros

        parsed_data.update({
            'part_code': part_code,
            'work_order': work_order,
            'board': board_number
        })
    except Exception as e:
        validation_errors.append(f'Serial number parsing error: {str(e)}')
        return parsed_data

    # Validate work order (must be 7 digits)
    if not re.match(r'^\d{7}$', parsed_data['work_order']):
        validation_errors.append(f'Invalid work order format: {parsed_data["work_order"]}')

    # Validate board sequence (must be between 1 and 9999)
    if not (1 <= parsed_data['board'] <= 9999):
        validation_errors.append(f'Board sequence must be between 1 and 9999: {parsed_data["board"]}')

    return parsed_data


def validate_serial_number_fields(parsed_data: Dict[str, str]) -> None:
    """
    Validate the individual fields extracted from serial number
    Add any specific business validation rules here
    """
    # Validate work order (must be 7 digits)
    if not re.match(r'^\d{7}$', parsed_data['work_order']):
        raise ValidationError({
            'work_order': f'Invalid work order format: {parsed_data["work_order"]}'
        })

    # Validate board sequence (must be between 1 and 9999)
    if not (1 <= parsed_data['board'] <= 9999):
        raise ValidationError({
            'board': f'Board sequence must be between 1 and 9999: {parsed_data["board"]}'
        })


def process_serial_number(serial_number: str) -> Dict[str, str]:
    """
    Main function to process and validate serial number
    Returns validated dictionary of fields
    """
    # Parse the serial number
    parsed_data = parse_serial_number(serial_number)
    
    # Validate the parsed fields
    validate_serial_number_fields(parsed_data)

    return parsed_data
