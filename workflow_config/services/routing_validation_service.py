from django.db import transaction
from django.core.exceptions import ValidationError
from workflow_config.models import Routing, RoutingExecution, FormConfig, ProcessBlock
from catalog.models import Product
from operation.services.event_service import ManufacturingEventService


class RouteEvaluator:
    def __init__(self, route_schema):
        self.route_schema = route_schema
        self.operator_map = {
            "equals": lambda x, y: x == y,
            "not_equals": lambda x, y: x != y,
            "greater_than": lambda x, y: x > y,
            "less_than": lambda x, y: x < y,
            "greater_equals": lambda x, y: x >= y,
            "less_equals": lambda x, y: x <= y,
            "contains": lambda x, y: y in x,
        }

    def get_property_value(self, event_data, property_ref):
        """Extract a value from node outputs using a property path reference"""
        print(f"get_property_value: Evaluating property reference {property_ref}")
        if property_ref["type"] == "value":
            print(f"get_property_value: Property reference {property_ref} is a value")
            return property_ref["value"]

        if property_ref["type"] == "property":
            node_name = property_ref["node"]
            property_path = property_ref["path"]

            print(f"get_property_value: Property reference {property_ref} is a property")
            print(f"get_property_value: Attempting to retrieve node {node_name} from event data")

            if node_name not in event_data:
                print(f"get_property_value: Node {node_name} not found in event data")
                # return None

            print(f"get_property_value: Node {node_name} found in event data")

            # Navigate the property path
            current = event_data.copy()
            for key in property_path.split('.'):
                print(f"get_property_value: Attempting to retrieve property {key} from {current}")
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    print(f"get_property_value: Property {key} not found in {current}")
                    return None
            print(f"get_property_value: Successfully retrieved property {property_path} from event data")
            return current

        print(f"get_property_value: Unknown property reference {property_ref}")
        return None

    def evaluate_condition(self, condition, event_data):
        """Evaluate a single condition against the current node outputs"""
        print(f"evaluate_condition: Evaluating condition {condition}")
        left_value = self.get_property_value(event_data, condition["left"])
        right_value = self.get_property_value(event_data, condition["right"])

        # If we couldn't resolve either value, condition fails
        if left_value is None or right_value is None:
            print(f"evaluate_condition: One or both values could not be resolved for condition {condition}")
            return False

        operator = condition["operator"]
        if operator in self.operator_map:
            print(f"evaluate_condition: Evaluating condition {condition} with operator {operator}")
            try:
                result = self.operator_map[operator](left_value, right_value)
                print(f"evaluate_condition: Condition {condition} evaluated to {result}")
                return result
            except Exception as e:
                # Handle type mismatches or other errors
                print(f"evaluate_condition: Error evaluating condition {condition}: {e}")
                return False

        print(f"evaluate_condition: Unknown operator {operator} for condition {condition}")
        return False


class RoutingValidationService:
    """Service for validating events against routing schemas"""

    @staticmethod
    def get_process_block_from_form(form_id):
        """Extract process block from form"""
        try:
            form = FormConfig.objects.get(id=form_id)
            if not form.process_block:
                return None, "Form has no associated process block"
            return form.process_block, None
        except FormConfig.DoesNotExist:
            return None, f"Form with ID {form_id} not found"

    @staticmethod
    def get_first_required_node(routing_schema, start_node):
        """
        Find the first node that requires an event, starting from the given node

        Args:
            routing_schema: The routing schema
            start_node: The starting node to check

        Returns:
            str: First node that requires an event
        """
        routing_schema_data = routing_schema.get('routing_schema', {})
        components = routing_schema_data.get('components', {})

        # If the start node requires an event, return it
        if components.get(start_node, {}).get('event_required', True):
            return start_node

        # Otherwise, find the next node that requires an event
        return RoutingValidationService.get_next_executable(routing_schema, start_node)

    @staticmethod
    def get_next_executable(routing_schema, current_node, event_data=None):
        """
        Determine the next executable node based on routing schema,
        skipping any nodes with event_required: false

        Args:
            routing_schema: The routing schema
            current_node: The current process block code
            event_data: Optional event data for condition evaluation

        Returns:
            str: Next executable node code or None if end node
        """
        print(f"get_next_executable: Getting next executable node for {current_node}")

        # Get routing schema components
        routing_schema_data = routing_schema.get('routing_schema', {})
        connections = routing_schema_data.get('route', {}).get('connections', {})
        components = routing_schema_data.get('components', {})
        end_node = routing_schema_data.get('route', {}).get('end')

        # If current node is end node, there's no next executable
        if current_node == end_node:
            print(f"get_next_executable: Node {current_node} is end node")
            return None

        # Get current node's connections
        node_connections = connections.get(current_node, {})

        # Check if node has towards_many property
        towards_many = node_connections.get('towards_many', [])
        if towards_many:
            print(f"get_next_executable: Node {current_node} has towards_many property")
            # For simplicity, return the first node in towards_many
            # In a real implementation, it might need to be handled differently
            next_node = towards_many[0]
            # Check if this node requires an event
            if not components.get(next_node, {}).get('event_required', True):
                # Recursively find the next required node
                return RoutingValidationService.get_next_executable(routing_schema, next_node, event_data)
            return next_node

        # Get towards property
        towards = node_connections.get('towards', {})

        # Check if this is an end node
        if towards.get('end', False):
            print(f"get_next_executable: Node {current_node} is end node")
            return None

        # Check conditions if event_data is provided
        if event_data and 'conditions' in towards:
            print(f"get_next_executable: Evaluating conditions for node {current_node}")
            evaluator = RouteEvaluator(routing_schema_data)
            conditions = towards.get('conditions', [])
            print("__before conditions loop:__", conditions)
            if conditions is not None:
                for condition in conditions:
                    try:
                        print(f"get_next_executable: Evaluating condition {condition}")
                        if evaluator.evaluate_condition(condition, event_data):
                            print(f"get_next_executable: Condition {condition} evaluated to True")
                            next_node = condition.get('target')
                            # Check if this node requires an event
                            if not components.get(next_node, {}).get('event_required', True):
                                # Recursively find the next required node
                                return RoutingValidationService.get_next_executable(routing_schema, next_node, event_data)
                            return next_node
                    except Exception as e:
                        print(f"get_next_executable: Error evaluating condition {condition}: {e}")

        # Get default target
        next_node = towards.get('default')
        print(f"get_next_executable: Default target for node {current_node} is {next_node}")

        # Check if this node requires an event
        if next_node and not components.get(next_node, {}).get('event_required', True):
            print(f"get_next_executable: Node {next_node} does not require an event, finding next required node")
            # Recursively find the next required node
            return RoutingValidationService.get_next_executable(routing_schema, next_node, event_data)

        return next_node

    @staticmethod
    def update_routing_execution_with_event_ids(serial_number, event_ids):
        """
        Update the last execution step in the routing execution with multiple event IDs

        Args:
            serial_number (str): The serial number of the product
            event_ids (list): List of event IDs associated with this execution step

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            routing_execution = RoutingExecution.objects.get(serial_number=serial_number)
            if routing_execution.executed_sequence:
                # Update the last execution step with all event IDs
                last_step = routing_execution.executed_sequence[-1]
                if 'event_id' in last_step:
                    if isinstance(last_step['event_id'], list):
                        last_step['event_id'].extend(event_ids)
                    else:
                        last_step['event_id'] = [last_step['event_id']] + event_ids
                else:
                    last_step['event_id'] = event_ids

                routing_execution.save()
                return True
            return False
        except RoutingExecution.DoesNotExist:
            return False


    @staticmethod
    @transaction.atomic
    def validate_event(event_data):
        """
        Validate an event against routing schema

        Args:
            event_data: The event data to validate

        Returns:
            tuple: (is_valid, validation_errors, next_executable)
        """
        print("__inside validation__")
        # Extract necessary data
        serial_number = event_data.get('serial_number', )
        form_id = event_data.get('form')
        timestamp = event_data.get('timestamp', None)

        # Extract product using ManufacturingEventService.process_serial_number
        processed_data = ManufacturingEventService.process_serial_number(serial_number)
        if not processed_data:
            return False, [{
                "field": "serial_number",
                "error": "Serial number processing failed",
                "value": serial_number
            }], None

        print("__processed_data:__", processed_data)
        product = processed_data.get('product')
        validation_errors = processed_data.get('validation_errors', [])

        if validation_errors:
            return False, validation_errors, None

        print("__validation_errors:__", validation_errors)
        # Extract process block from form
        process_block, pb_error = RoutingValidationService.get_process_block_from_form(form_id)
        if pb_error:
            return False, [{
                'field': 'event_data.form',
                'error': pb_error,
                'value': form_id
            }], None

        print("__process_block:__", process_block)
        # Get associated routing - a product can only have one routing
        routing = Routing.objects.filter(products=product).first()
        if not routing:
            return False, [{
                'field': 'event_data.serial_number',
                'error': f"No routing defined for product {product.code}",
                'value': serial_number
            }], None

        routing_schema = routing.schema
        print("__routing_schema:__", routing_schema)
        # Get routing start node
        start_node = routing_schema.get('routing_schema', {}).get('route', {}).get('start')
        print("__start_node:__", start_node)

        # Find the first node that requires an event
        first_required_node = RoutingValidationService.get_first_required_node(routing_schema, start_node)
        print("__first_required_node:__", first_required_node)

        # Check if this is the first event for this serial number
        routing_execution, created = RoutingExecution.objects.get_or_create(
            serial_number=serial_number,
            defaults={
                'product': product,
                'executed_sequence': [],
                'next_executable': first_required_node
            }
        )

        print("__routing_execution:__", routing_execution)
        # Validate current process block against next executable
        current_node = process_block.code
        expected_node = routing_execution.next_executable

        is_valid = (current_node == expected_node)

        print("__current_node:__", current_node)
        print("__expected_node:__", expected_node)
        print("__is_valid:__", is_valid)

        # Add validation error if nodes don't match
        if not is_valid:
            # Get the name of the expected process block
            expected_process_block = None
            try:
                expected_process_block = ProcessBlock.objects.get(code=expected_node)
                expected_name = expected_process_block.name
                current_name = process_block.name
                error_message = f"Invalid workstation. Expected from '{expected_name}'" if expected_node else f"Invalid workstation. Execution is already done."
            except ProcessBlock.DoesNotExist:
                # Fallback to code if process block not found
                error_message = f"Invalid workstation. Expected from '{expected_node}'" if expected_node else f"Invalid workstation. Execution is already done."
            raise ValidationError(error_message)

            validation_errors.append({
                'field': 'routing',
                'error': error_message,
                'value': current_node
            })
            next_node = expected_node
        else:
            # Calculate next executable node
            next_node = RoutingValidationService.get_next_executable(
                routing_schema,
                current_node,
                event_data
            )
        print("__next_node:__", next_node)

        routing_execution.add_execution(current_node, expected_node, timestamp)
        routing_execution.next_executable = next_node
        if routing_execution.is_valid:
            routing_execution.is_valid = is_valid
        routing_execution.save()

        print("__routing_execution after update:__", routing_execution)

        return is_valid, validation_errors, next_node
