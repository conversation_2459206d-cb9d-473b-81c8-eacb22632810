from operation.models import ManufacturingEvent
from django.db.models import Max

class ExecutionValidationService:
    """
    Service for validating execution steps based on event data
    """

    @staticmethod
    def get_step_validity(event_ids):
        """
        Determine if an execution step is valid based on event IDs
        Args:
            event_ids: List or single event ID associated with the execution step
        Returns:
            dict: Dictionary with validity status and reason
                {
                    'is_valid': bool,
                    'reason': str (optional)
                }
            or None if no event IDs are provided
        """
        if not event_ids:
            return None

        if not isinstance(event_ids, list):
            event_ids = [event_ids]

        if not event_ids:
            return None

        # Get the max event ID (most recent event)
        max_event_id = max(event_ids)

        try:
            event = ManufacturingEvent.objects.get(id=max_event_id)
            is_valid = event.inspection_status
            return {
                'is_valid': is_valid,
                'reason': None if is_valid else f"Failed inspection in event {max_event_id}"
            }
        except ManufacturingEvent.DoesNotExist:
            return {
                'is_valid': False,
                'reason': f"Event {max_event_id} not found"
            }

    @classmethod
    def get_validator_for_form(cls, form_code=None):
        """
        Factory method to get the appropriate validator for a form
        This allows for future extension with different validation logic per form
        Args:
            form_code: The form code to get a validator for
        Returns:
            function: A function that takes event_ids and returns validity info
        """
        # In the future, this could return different validator functions based on form_code
        return cls.get_step_validity
