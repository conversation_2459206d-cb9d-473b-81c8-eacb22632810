from dataclasses import dataclass, field
from typing import Dict, List
from ..models import A<PERSON>IDailyYield, AOIRejection


@dataclass
class SheetConfig:
    name: str
    required_columns: List[str]
    model_class: object
    max_rows: int
    non_required_columns: List[str] = field(default_factory=list)


@dataclass
class ImportConfig:
    MAX_FILE_SIZE_MB: int = 10
    ALLOWED_EXTENSIONS: tuple = ('.xlsx',)
    DATETIME_FORMAT: str = '%d-%b-%y'  # Format for "16-Oct-24"

    @staticmethod
    def get_sheet_configs() -> Dict[str, SheetConfig]:
        return {
            'AOI Daily Yield': SheetConfig(
                name='AOI Daily Yield',
                required_columns=[
                    'Date', 'Model', 'Total Cards Produced', 
                    'Total Fail Cards', 'Total real faults card wise',
                    'Total Pass card', 'Rejection %', 'FPY',
                ],
                non_required_columns=['S.No',],
                model_class=AOIDailyYield,
                max_rows=10_000
            ),
            'AOI Rejection': SheetConfig(
                name='AOI Rejection',
                required_columns=[
                    'Date', 'MODEL NAME', 'SERIAL NUMBER', 'LOCATION',
                    "COMP'S PART NUMBER", 'DEFECT',
                ],
                non_required_columns=['S.No',],
                model_class=AOIRejection,
                max_rows=10_000
            )
        }

    SHEET_CONFIGS: Dict[str, SheetConfig] = field(default_factory=get_sheet_configs)
