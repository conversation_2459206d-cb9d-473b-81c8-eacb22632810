from rest_framework import serializers
from ..models import FormConfig


class FormConfigSummarySerializer(serializers.ModelSerializer):
    """Serializer for minimal form config details in event responses"""
    class Meta:
        model = FormConfig
        fields = ['id', 'name', 'code', 'description', 'is_active']


class BaseFormConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = FormConfig
        fields = '__all__'
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at', 'version']

    def validate_form_schema(self, value):
        required_keys = {'form_fields'}
        if not all(key in value for key in required_keys):
            raise serializers.ValidationError(
                f"Form schema must contain all required keys: {required_keys}"
            )

        for field in value.get('form_fields', []):
            if not all(key in field for key in {'name', 'label', 'type', 'required',}):
                raise serializers.ValidationError(
                    "Each form field must contain 'name', 'label', 'type', and 'required'"
                )
        return value


class FormConfigSerializer(BaseFormConfigSerializer):
    class Meta(BaseFormConfigSerializer.Meta):
        pass


class FormConfigUpdateSerializer(BaseFormConfigSerializer):
    class Meta(BaseFormConfigSerializer.Meta):
        read_only_fields = ['created_by', 'created_at', 'updated_at', 'version', 'code',]
