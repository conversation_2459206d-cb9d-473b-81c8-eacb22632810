from rest_framework import serializers
from ..models import ReferenceCategory, ReferenceValue


class ReferenceValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReferenceValue
        fields = ['id', 'code', 'value', 'description', 'sort_order', 'is_active']


class ReferenceCategorySerializer(serializers.ModelSerializer):
    values = ReferenceValueSerializer(many=True, read_only=True)

    class Meta:
        model = ReferenceCategory
        fields = ['id', 'code', 'name', 'description', 'is_active', 'values']
