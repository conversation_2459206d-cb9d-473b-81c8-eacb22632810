from rest_framework import serializers
from ..models import ProcessBlock, Area, AssemblyLine


class ProcessBlockSerializer(serializers.ModelSerializer):
    """
    Serializer for the ProcessBlock model.
    """
    area_name = serializers.ReadOnlyField(source='area.name')
    line_name = serializers.ReadOnlyField(source='line_loc.name', default=None)
    created_by_name = serializers.ReadOnlyField(source='created_by.username')

    class Meta:
        model = ProcessBlock
        fields = [
            'id', 'name', 'code', 'description', 'is_active', 
            'area', 'area_name', 'line_loc', 'line_name', 
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by', 'created_by_name']

    def create(self, validated_data):
        """
        Create and return a new ProcessBlock instance, given the validated data.
        """
        # Set the created_by field to the current user
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)
