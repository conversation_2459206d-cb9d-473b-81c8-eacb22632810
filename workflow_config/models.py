import uuid
from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from core.models import BaseModel, BaseEntity
from .schemas import validate_form_schema
from .routing_schema import validate_routing_schema


def generate_unique_org_id():
    return str(uuid.uuid4())


def generate_unique_code():
    return str(uuid.uuid4())[:8]


class OrgIDValidator:
    def __call__(self, value):
        # Custom validation logic for org_id
        # Here, we just verify that it's a valid UUID format
        try:
            uuid_obj = uuid.UUID(value, version=4)
            if str(uuid_obj) != value:
                raise ValidationError(f'{value} is not a valid UUID')
        except ValueError:
            raise ValidationError(f'{value} is not a valid UUID')


class Factory(BaseEntity):
    org_id = models.CharField(
        max_length=50,
        unique=True,
        default=generate_unique_org_id
    )
    location = models.CharField(max_length=100)
    established_date = models.DateField()
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='managed_factories'
    )

    class Meta:
        db_table = 'mes_factory'
        ordering = ['name']

    def __str__(self):
        return self.name


class Area(BaseEntity):
    AREATYPECHOICES = [
        ('sf', 'Shop Floor'),
        ('wh', 'Warehouse'),
    ]
    factory = models.ForeignKey(Factory, on_delete=models.CASCADE, related_name='areas')
    atype = models.CharField(max_length=50, choices=AREATYPECHOICES)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_areas'
    )

    class Meta:
        db_table = 'mes_areas'
        ordering = ['name']

    def __str__(self):
        return self.name


class AssemblyLine(BaseEntity):
    area = models.ForeignKey(Area, on_delete=models.PROTECT, related_name='lines')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_assembly_lines'
    )

    class Meta:
        db_table = 'mes_assembly_lines'
        ordering = ['name']

    def __str__(self):
        return self.name


class ProcessBlock(BaseEntity):
    area = models.ForeignKey(Area, on_delete=models.PROTECT, related_name='process_blocks')
    line_loc = models.ForeignKey(AssemblyLine, on_delete=models.PROTECT, null=True, blank=True, related_name='process_blocks')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_process_blocks'
    )

    class Meta:
        db_table = 'mes_process_blocks'
        ordering = ['name']

    def __str__(self):
        return self.name


class Sop(BaseEntity):
    process_block = models.OneToOneField(ProcessBlock, on_delete=models.CASCADE, related_name='sops')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_sops'
    )

    class Meta:
        db_table = 'mes_sop'
        ordering = ['name']

    def __str__(self):
        return self.name


class MasterProgram(BaseEntity):
    process_block = models.OneToOneField(ProcessBlock, on_delete=models.CASCADE, related_name='master_programs')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_master_programs'
    )

    class Meta:
        db_table = 'mes_master_program'
        ordering = ['name']

    def __str__(self):
        return self.name


class MasterProgramProductParam(BaseModel):
    master_program = models.ForeignKey(MasterProgram, on_delete=models.PROTECT, related_name='product_params')
    product = models.ForeignKey('catalog.Product', on_delete=models.PROTECT)
    params = models.JSONField()

    class Meta:
        db_table = 'mes_master_program_product_param'
        unique_together = ('master_program', 'product')


class Routing(BaseEntity):
    schema = models.JSONField(validators=[validate_routing_schema])
    products = models.ManyToManyField('catalog.Product', through='RoutingProduct', related_name='routings')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_routings'
    )

    class Meta:
        db_table = 'mes_routing'

    def __str__(self):
        return self.name

    def clean(self):
        """Additional model validation"""
        super().clean()
        if self.schema:
            validate_routing_schema(self.schema)

    def save(self, *args, **kwargs):
        """Ensure validation runs on save"""
        self.full_clean()
        super().save(*args, **kwargs)


class RoutingProduct(models.Model):
    """
    Through model for the many-to-many relationship between Routing and Product
    """
    routing = models.ForeignKey('Routing', on_delete=models.CASCADE)
    product = models.ForeignKey('catalog.Product', on_delete=models.CASCADE)

    class Meta:
        db_table = 'mes_routing_product'
        unique_together = ('routing', 'product')  # Prevent duplicate relationships

    def clean(self):
        """
        Validate that a product can only have one routing
        """
        from django.core.exceptions import ValidationError

        # Skip validation if product is not set yet
        if not hasattr(self, 'product') or not self.product:
            return

        # Check if this product already has a routing (excluding this instance)
        existing_routing = RoutingProduct.objects.filter(
            product=self.product
        ).exclude(pk=self.pk if self.pk else None).first()

        if existing_routing:
            raise ValidationError({
                'product': f"This product is already associated with routing '{existing_routing.routing.name}'. "
                          f"A product can only have one routing at this time."
            })

    def save(self, *args, **kwargs):
        """
        Ensure validation runs on save
        """
        self.full_clean()
        super().save(*args, **kwargs)


class RoutingExecution(models.Model):
    """
    Tracks the execution sequence of process blocks for a serial number
    """
    serial_number = models.CharField(max_length=100, unique=True)
    executed_sequence = models.JSONField(default=list)  # List of objects with executed/should_execute
    next_executable = models.CharField(max_length=100, null=True, blank=True)
    product = models.ForeignKey('catalog.Product', on_delete=models.CASCADE, null=True)
    is_valid = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_routing_execution'

    def add_execution(self, executed, should_execute, timestamp=None, event_ids=None):
        """
        Add a new execution step to the sequence

        Args:
            executed (str): The process block code that was actually executed
            should_execute (str): The process block code that should have been executed
            timestamp (datetime): The time when the event was processed(extracted from form data)
            event_ids (list, optional): List of event IDs associated with this execution step
        """
        execution_step = {
            "executed": executed,
            "should_execute": should_execute,
            "time": timestamp,
        }

        if event_ids is not None:
            execution_step["event_id"] = event_ids

        self.executed_sequence.append(execution_step)
        self.save()

    def get_incorrect_executions(self):
        """
        Returns a list of all incorrect execution steps
        """
        return [
            step for step in self.executed_sequence
            if step["executed"] != step["should_execute"]
        ]

    def get_last_execution(self):
        """
        Returns the last execution step or None if no executions
        """
        if not self.executed_sequence:
            return None
        return self.executed_sequence[-1]

    def __str__(self):
        return f"{self.serial_number} - {self.product.name if self.product else 'Unknown'}"


class FormConfig(BaseEntity):
    """
    Dynamic form configuration for manufacturing process blocks/workstations
    """
    process_block = models.ForeignKey(
        ProcessBlock,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='forms'
    )
    form_schema = models.JSONField(validators=[validate_form_schema])
    version = models.PositiveIntegerField(default=1)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_forms'
    )

    class Meta:
        db_table = 'mes_form_config'

    def __str__(self):
        return self.name

    def clean(self):
        """Additional model validation"""
        super().clean()
        if self.form_schema:
            validate_form_schema(self.form_schema)

    def save(self, *args, **kwargs):
        """Ensure validation runs on save"""
        self.full_clean()
        super().save(*args, **kwargs)


class ReferenceCategory(BaseEntity):
    """
    Categories for reference data (e.g., 'fault_types', 'fault_causes', 'machine_states')
    """

    class Meta:
        db_table = 'mes_reference_categories'
        ordering = ['name']
        verbose_name_plural = 'Reference Categories'

    def __str__(self):
        return self.name


class ReferenceValue(BaseModel):
    """
    Reference values for each category
    """
    category = models.ForeignKey(ReferenceCategory, on_delete=models.CASCADE, related_name='values')
    code = models.CharField(max_length=50)
    value = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    sort_order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_reference_values'
        ordering = ['category', 'sort_order', 'value']
        unique_together = ['category', 'code']

    def __str__(self):
        return f"{self.category.code} - {self.value}"


class AOIDailyYield(models.Model):
    sr_no = models.IntegerField(null=True, blank=True)
    date = models.DateField()
    model = models.CharField(max_length=100, null=True, blank=True)
    total_cards_produced = models.IntegerField(validators=[MinValueValidator(0)])
    total_fail_cards = models.IntegerField(validators=[MinValueValidator(0)])
    total_real_faults = models.IntegerField(validators=[MinValueValidator(0)])
    total_pass_cards = models.IntegerField(validators=[MinValueValidator(0)])
    rejection_percentage = models.DecimalField(
        max_digits=7,
        decimal_places=4,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    fpy_percentage = models.DecimalField(
        max_digits=7,
        decimal_places=4,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'mes_aoi_daily_yield'


class AOIRejection(models.Model):
    TEXT_FIELD_KWARGS = {'max_length': 100, 'null': True, 'blank': True}

    sr_no = models.IntegerField(null=True, blank=True)
    date = models.DateField()
    model = models.CharField(**TEXT_FIELD_KWARGS)
    serial_number = models.CharField(**TEXT_FIELD_KWARGS)
    component_part_no = models.CharField(**TEXT_FIELD_KWARGS)
    location = models.CharField(**TEXT_FIELD_KWARGS)
    defect = models.CharField(**TEXT_FIELD_KWARGS)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'mes_aoi_rejection'
