from django.contrib import admin
from django.core.exceptions import ValidationError
from django.forms import ModelForm
from . import models as config_models


@admin.register(config_models.Area)
class AreaAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'factory', 'atype', 'is_active', 'created_by', 'created_at')
    search_fields = ('name', 'code')
    list_filter = ('is_active', 'atype', 'created_at')
    autocomplete_fields = ['factory']


@admin.register(config_models.AssemblyLine)
class AssemblyLineAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'area', 'is_active', 'created_by', 'created_at')
    search_fields = ('name', 'code')
    list_filter = ('is_active', 'created_at')
    autocomplete_fields = ['area']


@admin.register(config_models.Factory)
class FactoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'org_id', 'location', 'is_active', 'established_date', 'created_at')
    search_fields = ('name', 'code', 'location')
    list_filter = ('is_active', 'created_at')


@admin.register(config_models.FormConfig)
class FormConfigAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'description', 'is_active', 'created_by', 'created_at')
    search_fields = ('name', 'description')
    list_filter = ('is_active', 'created_at')

    readonly_fields = ('code',)


class ReferenceValueInline(admin.TabularInline):
    model = config_models.ReferenceValue
    extra = 0


@admin.register(config_models.ReferenceCategory)
class ReferenceCategoryAdmin(admin.ModelAdmin):
    inlines = [ReferenceValueInline]
    list_display = ('code', 'name', 'is_active', 'created_at', 'updated_at')
    search_fields = ('name', 'code')
    list_filter = ('is_active', 'created_at')


@admin.register(config_models.AOIDailyYield)
class AOIDailyYieldAdmin(admin.ModelAdmin):
    list_display = ('sr_no', 'date', 'model', 'total_cards_produced', 'total_fail_cards', 'total_real_faults', 'total_pass_cards', 'rejection_percentage', 'fpy_percentage', 'created_at')
    search_fields = ('date', 'model')


@admin.register(config_models.AOIRejection)
class AOIRejectionAdmin(admin.ModelAdmin):
    list_display = ('sr_no', 'date', 'model', 'serial_number', 'component_part_no', 'location', 'defect', 'created_at')
    search_fields = ('date', 'model', 'serial_number')


@admin.register(config_models.ProcessBlock)
class ProcessBlockAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'area', 'line_loc', 'is_active', 'created_by', 'created_at')
    search_fields = ('name', 'code')
    list_filter = ('is_active', 'area', 'created_at')
    autocomplete_fields = ['area', 'line_loc']


class RoutingProductInlineForm(ModelForm):
    """
    Custom form for RoutingProductInline to provide better validation messages
    """
    class Meta:
        model = config_models.RoutingProduct
        fields = ['product']

    def clean(self):
        cleaned_data = super().clean()

        # Get the product from cleaned_data, not from instance
        product = cleaned_data.get('product')
        if not product:
            return cleaned_data

        # Get the routing from the parent form
        if hasattr(self, 'instance') and hasattr(self.instance, 'routing') and self.instance.routing:
            routing = self.instance.routing
        else:
            # If we're adding to a new routing that doesn't exist yet, we can't validate
            # This will be caught later when the instance is saved
            return cleaned_data

        # Check if product already has a different routing
        existing_routing = config_models.RoutingProduct.objects.filter(
            product=product
        ).exclude(routing=routing).first()

        if existing_routing:
            raise ValidationError({
                'product': f"(workflow.admin)This product is already associated with routing '{existing_routing.routing.name}'. "
                          f"A product can only have one routing at this time."
            })

        return cleaned_data


class RoutingProductInline(admin.TabularInline):
    """
    Inline admin for adding products to a routing
    """
    model = config_models.RoutingProduct
    # form = RoutingProductInlineForm
    extra = 0
    autocomplete_fields = ['product']
    verbose_name = "Product"
    verbose_name_plural = "Products"


@admin.register(config_models.Routing)
class RoutingAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'get_products_count', 'is_active', 'created_by', 'created_at')
    search_fields = ('name', 'code', 'products__name', 'products__code')
    list_filter = ('is_active', 'created_at')
    inlines = [RoutingProductInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Routing Details', {
            'fields': ('schema',)
        }),
        ('Metadata', {
            'fields': ('created_by',)
        }),
    )

    def get_products_count(self, obj):
        """Display the number of products associated with this routing"""
        return obj.products.count()
    get_products_count.short_description = 'Products Count'
