from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from core.pagination import CustomPageNumberPagination
from authentication.decorators import requires_permission

from ..models import ProcessBlock
from ..serializers.process_serializers import ProcessBlockSerializer


class ProcessBlockViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows ProcessBlocks to be viewed or edited.
    
    list:
    Return a list of all ProcessBlocks.
    
    create:
    Create a new ProcessBlock instance.
    
    retrieve:
    Return the given ProcessBlock.
    
    update:
    Update the given ProcessBlock.
    
    partial_update:
    Partially update the given ProcessBlock.
    
    destroy:
    Delete the given ProcessBlock.
    """
    queryset = ProcessBlock.objects.all().order_by('id')
    serializer_class = ProcessBlockSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = CustomPageNumberPagination

    @requires_permission(('process_block', 'get'))
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        # Apply filters
        area_filter = request.query_params.get('area')
        line_loc_filter = request.query_params.get('line_loc')
        is_active_filter = request.query_params.get('is_active')
        name_search = request.query_params.get('name')
        code_search = request.query_params.get('code')
        description_search = request.query_params.get('description')

        if area_filter:
            queryset = queryset.filter(area=area_filter)
        if line_loc_filter:
            queryset = queryset.filter(line_loc=line_loc_filter)
        if is_active_filter:
            queryset = queryset.filter(is_active=is_active_filter)
        if name_search:
            queryset = queryset.filter(name__icontains=name_search)
        if code_search:
            queryset = queryset.filter(code__icontains=code_search)
        if description_search:
            queryset = queryset.filter(description__icontains=description_search)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @requires_permission(('process_block', 'create'))
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @requires_permission(('process_block', 'get'))
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @requires_permission(('process_block', 'update'))
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @requires_permission(('process_block', 'update'))
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @requires_permission(('process_block', 'delete'))
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
