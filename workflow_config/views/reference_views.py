from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters import rest_framework as filters
from ..models import ReferenceCategory, ReferenceValue
from ..serializers.reference_serializers import ReferenceCategorySerializer, ReferenceValueSerializer


class ReferenceValueFilter(filters.FilterSet):
    class Meta:
        model = ReferenceValue
        fields = {
            'category': ['exact'],
            'code': ['exact', 'in'],
            'is_active': ['exact'],
        }


class ReferenceCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for reference categories and their values
    """
    queryset = ReferenceCategory.objects.prefetch_related('values').all()
    serializer_class = ReferenceCategorySerializer
    filterset_fields = ['code', 'is_active']

    @action(detail=False, methods=['get'])
    def by_code(self, request):
        """Get reference values by category code"""
        category_code = request.query_params.get('code')
        if not category_code:
            return Response({'error': 'Category code is required'}, status=400)

        try:
            category = self.queryset.get(code=category_code, is_active=True)
            serializer = self.serializer_class(category)
            return Response(serializer.data)
        except ReferenceCategory.DoesNotExist:
            return Response({'error': 'Category not found'}, status=404)


class ReferenceValueViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for reference values
    """
    queryset = ReferenceValue.objects.select_related('category').all()
    serializer_class = ReferenceValueSerializer
    filterset_class = ReferenceValueFilter

    def get_queryset(self):
        queryset = super().get_queryset()
        category_code = self.request.query_params.get('category_code')
        if category_code:
            queryset = queryset.filter(category__code=category_code)
        return queryset.order_by('sort_order', 'value')
