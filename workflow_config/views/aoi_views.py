from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser
from core.services.import_service import ExcelImportService
from core.validators import ExcelValidator
from authentication.decorators import requires_permission
from ..config.aoi import ImportConfig


class ExcelUploadView(APIView):
    parser_classes = (MultiPartParser,)

    @requires_permission(('aoi', 'create'))
    def post(self, request):
        if 'file' not in request.FILES:
            return Response({
                'success': False,
                'error': 'No file provided'
            }, status=400)

        file_obj = request.FILES['file']

        validator = ExcelValidator(ImportConfig())
        import_service = ExcelImportService(validator)

        result = import_service.process_file(file_obj)

        status_code = 200 if result['success'] else 400
        return Response(result, status=status_code)
