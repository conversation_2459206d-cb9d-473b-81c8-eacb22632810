from rest_framework.views import APIView
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from workflow_config.models import Routing, RoutingExecution
from workflow_config.serializers.routing_serializers import (
    RoutingSummarySerializer,
    RoutingDetailSerializer,
    ExecutedRoutingSerializer,
)
from workflow_config.filters import Routing<PERSON>ilter
from authentication.decorators import requires_permission


class RoutingViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing routing configurations.
    Provides CRUD operations with filtering capabilities.
    """
    permission_classes = [IsAuthenticated]
    queryset = Routing.objects.all().order_by('-created_at')
    filter_backends = [DjangoFilterBackend]
    filterset_class = RoutingFilter

    def get_serializer_class(self):
        """
        Return appropriate serializer class based on action
        """
        if self.action == 'list':
            return RoutingSummarySerializer
        return RoutingDetailSerializer

    @requires_permission(('routing', 'get'))
    def list(self, request, *args, **kwargs):
        """
        List all routings with pagination and filtering
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @requires_permission(('routing', 'get'))
    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a specific routing by ID
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @requires_permission(('routing', 'create'))
    def create(self, request, *args, **kwargs):
        """
        Create a new routing
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(created_by=request.user)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @requires_permission(('routing', 'update'))
    def update(self, request, *args, **kwargs):
        """
        Update an existing routing
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @requires_permission(('routing', 'delete'))
    def destroy(self, request, *args, **kwargs):
        """
        Delete a routing
        """
        instance = self.get_object()
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class RoutingProductView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = RoutingDetailSerializer

    @requires_permission(('routing', 'get'))
    def get(self, request, product_code):
        """
        Get routing for a specific product
        """
        try:
            # Since a product can only have one routing (as per current business rule),
            # we'll get the first routing associated with the product
            routing = Routing.objects.filter(products__code=product_code).first()
            if not routing:
                return Response(
                    {"error": f"No routing found for product code {product_code}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            serializer = self.serializer_class(routing)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {"error": f"Error retrieving routing: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RoutingSerialView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = RoutingDetailSerializer

    @requires_permission(('routing', 'get'))
    def get(self, request, serial_number):
        """
        Get routing execution details for a specific serial number
        """
        try:
            # Get execution record
            execution = RoutingExecution.objects.get(serial_number=serial_number)

            # Get associated routing - a product can only have one routing
            routing = Routing.objects.filter(products=execution.product).first()
            if not routing:
                return Response(
                    {"error": f"No routing found for product associated with serial {serial_number}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Serialize with context
            serializer = ExecutedRoutingSerializer(
                routing,
                context={'execution': execution}
            )

            return Response(serializer.data)

        except RoutingExecution.DoesNotExist:
            return Response(
                {"error": f"No routing execution found for serial {serial_number}"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"Error retrieving routing: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
