from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.db.models import Prefetch

from workflow_config.models import Routing, ProcessBlock, FormConfig
from catalog.models import Product
from authentication.decorators import requires_permission
from authentication.serializers.auth_serializers import UserSummarySerializer


class ProcessBlockFormView(APIView):
    """
    API to retrieve process blocks and their associated forms for a specific
    routing or product.
    """
    permission_classes = [IsAuthenticated]

    @requires_permission(('routing', 'get'))
    def get(self, request):
        """
        Get process blocks and their associated forms for a specific routing or product.
        
        Query Parameters:
            product_id: Optional ID of the product
            product_code: Optional code of the product (alternative to product_id)
            routing_id: Optional ID of the routing
            routing_code: Optional code of the routing (alternative to routing_id)
            include_inactive: Optional boolean to include inactive forms (default: false)
            filter_node_types: Optional comma-separated list of node types to filter by
        
        Returns:
            Process blocks and their associated forms for the specified routing or product
        """
        # Parse query parameters
        product_id = request.query_params.get('product_id')
        product_code = request.query_params.get('product_code')
        routing_id = request.query_params.get('routing_id')
        routing_code = request.query_params.get('routing_code')
        include_inactive = request.query_params.get('include_inactive', 'false').lower() == 'true'
        filter_node_types = request.query_params.get('filter_node_types')
        
        if filter_node_types:
            filter_node_types = filter_node_types.split(',')
        
        # Validate input parameters
        if not any([product_id, product_code, routing_id, routing_code]):
            return Response(
                {
                    "error": "Invalid request",
                    "detail": "You must provide either product_id, product_code, routing_id, or routing_code."
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Priority: product_id > product_code > routing_id > routing_code
        routing = None
        product = None
        
        # First try to find by product
        if product_id or product_code:
            try:
                if product_id:
                    product = Product.objects.get(id=product_id)
                else:
                    product = Product.objects.get(code=product_code)
                
                # Get the routing associated with this product
                routing = Routing.objects.filter(products=product).first()
                if not routing:
                    return Response(
                        {
                            "error": "Resource not found",
                            "detail": f"No routing found for the product: {product.code if product else (product_id or product_code)}"
                        },
                        status=status.HTTP_404_NOT_FOUND
                    )
            except Product.DoesNotExist:
                return Response(
                    {
                        "error": "Resource not found",
                        "detail": f"No product found with the specified parameters: {product_id or product_code}"
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
        
        # If no routing found by product, try to find directly by routing
        if not routing:
            try:
                if routing_id:
                    routing = Routing.objects.get(id=routing_id)
                else:
                    routing = Routing.objects.get(code=routing_code)
            except Routing.DoesNotExist:
                return Response(
                    {
                        "error": "Resource not found",
                        "detail": f"No routing found with the specified parameters: {routing_id or routing_code}"
                    },
                    status=status.HTTP_404_NOT_FOUND
                )
        
        # Get process blocks from routing schema
        routing_schema = routing.schema.get('routing_schema', {})
        components = routing_schema.get('components', {})
        
        # Filter by node types if specified
        if filter_node_types:
            components = {
                code: component for code, component in components.items()
                if component.get('node_type') in filter_node_types
            }
        
        # Get process blocks associated with this routing
        process_block_codes = list(components.keys())
        
        # Filter for forms query
        forms_filter = {'is_active': True} if not include_inactive else {}
        
        # Get process blocks with their forms
        process_blocks = ProcessBlock.objects.filter(code__in=process_block_codes).prefetch_related(
            Prefetch('forms', queryset=FormConfig.objects.filter(**forms_filter).select_related('created_by'))
        )
        
        # Build the response
        process_blocks_data = []
        
        for pb in process_blocks:
            # Extract node_type from routing schema components
            node_type = components.get(pb.code, {}).get('node_type', '')
            
            # Format forms data
            forms_data = []
            for form in pb.forms.all():
                forms_data.append({
                    'id': form.id,
                    'code': form.code,
                    'name': form.name,
                    'version': form.version,
                    'created_at': form.created_at,
                    'created_by': {
                        'id': form.created_by.id,
                        'username': form.created_by.username
                    },
                    'is_active': form.is_active
                })
            
            # Add process block to response
            process_blocks_data.append({
                'code': pb.code,
                'name': pb.name,
                'node_type': node_type,
                'forms': forms_data
            })
        
        # Prepare final response
        response_data = {
            'routing': {
                'id': routing.id,
                'code': routing.code,
                'name': routing.name
            },
            'process_blocks': process_blocks_data
        }
        
        # Include product in response if requested by product
        if product:
            response_data['product'] = {
                'id': product.id,
                'code': product.code,
                'name': product.name
            }
        
        return Response(response_data)
