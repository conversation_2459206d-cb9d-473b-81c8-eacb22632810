#!/usr/bin/env python3
"""
Real-time Sync Test with Proper Schema Compliance
Tests mes_products and mes_manufacturing_events with correct data formats
"""

import psycopg2
import requests
import time
import json

# Database connection parameters
PG_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'mes_db',
    'user': 'postgres',
    'password': 'postgres'
}

CH_CONFIG = {
    'host': 'localhost',
    'port': 8123,
    'user': 'default',
    'password': 'password'
}

def query_clickhouse(query):
    """Execute query on ClickHouse"""
    try:
        url = f"http://{CH_CONFIG['host']}:{CH_CONFIG['port']}/"
        response = requests.post(
            url,
            data=query,
            auth=(CH_CONFIG['user'], CH_CONFIG['password']),
            timeout=10
        )
        if response.status_code == 200:
            return response.text.strip()
        else:
            return f"Error: {response.status_code} - {response.text}"
    except Exception as e:
        return f"Exception: {e}"

def query_postgresql(query, params=None):
    """Execute query on PostgreSQL"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            result = cursor.fetchall()
        else:
            conn.commit()
            result = cursor.fetchall() if cursor.description else []
        
        cursor.close()
        conn.close()
        return result
    except Exception as e:
        return f"Exception: {e}"

def test_mes_products_sync():
    """Test mes_products sync with proper schema"""
    print("🧪 Testing mes_products Real-time Sync")
    print("=" * 45)
    
    # Get current counts
    pg_count_before = query_postgresql("SELECT count(*) FROM mes_products")
    ch_count_before = query_clickhouse("SELECT count(*) FROM default.mes_products")
    
    print(f"📊 Before test:")
    print(f"   PostgreSQL: {pg_count_before[0][0] if not isinstance(pg_count_before, str) else 'Error'}")
    print(f"   ClickHouse: {ch_count_before}")
    
    # Insert test product with proper schema
    test_name = f"PeerDB_Test_Product_{int(time.time())}"
    test_code = f"TEST_{int(time.time())}"
    
    insert_query = """
    INSERT INTO mes_products (
        code, name, description, is_active, 
        created_at, updated_at, commodity_id
    ) VALUES (%s, %s, %s, %s, NOW(), NOW(), 1) 
    RETURNING id, name, code
    """
    
    result = query_postgresql(insert_query, (test_code, test_name, "PeerDB sync test", True))
    
    if isinstance(result, str):
        print(f"❌ Failed to insert: {result}")
        return False
    
    product_id = result[0][0]
    print(f"✅ Inserted product: ID {product_id}, Code: {test_code}, Name: {test_name}")
    
    # Wait for sync
    print(f"⏳ Waiting 8 seconds for sync...")
    time.sleep(8)
    
    # Check ClickHouse
    ch_check = query_clickhouse(f"SELECT count(*) FROM default.mes_products WHERE code = '{test_code}'")
    ch_count_after = query_clickhouse("SELECT count(*) FROM default.mes_products")
    
    print(f"📊 After test:")
    print(f"   ClickHouse total: {ch_count_after}")
    print(f"   Test record found: {ch_check}")
    
    if ch_check == "1":
        print(f"✅ SUCCESS: Product synced to ClickHouse!")
        
        # Check the actual data
        ch_data = query_clickhouse(f"SELECT id, code, name FROM default.mes_products WHERE code = '{test_code}'")
        print(f"📋 Synced data: {ch_data}")
        return True
    else:
        print(f"❌ FAILED: Product not found in ClickHouse")
        return False

def test_mes_manufacturing_events_sync():
    """Test mes_manufacturing_events sync with proper schema"""
    print(f"\n🧪 Testing mes_manufacturing_events Real-time Sync")
    print("=" * 55)
    
    # Get current counts
    pg_count_before = query_postgresql("SELECT count(*) FROM mes_manufacturing_events")
    ch_count_before = query_clickhouse("SELECT count(*) FROM default.mes_manufacturing_events")
    
    print(f"📊 Before test:")
    print(f"   PostgreSQL: {pg_count_before[0][0] if not isinstance(pg_count_before, str) else 'Error'}")
    print(f"   ClickHouse: {ch_count_before}")
    
    # Insert test event with proper JSON schema
    test_serial = f"TEST_SERIAL_{int(time.time())}"
    test_event_data = {
        "test": True,
        "timestamp": int(time.time()),
        "source": "peerdb_sync_test"
    }
    
    insert_query = """
    INSERT INTO mes_manufacturing_events (
        serial_number, event_type, event_data, created_at
    ) VALUES (%s, %s, %s, NOW()) 
    RETURNING id, serial_number, event_type
    """
    
    result = query_postgresql(insert_query, (test_serial, "SYNC_TEST", json.dumps(test_event_data)))
    
    if isinstance(result, str):
        print(f"❌ Failed to insert: {result}")
        return False
    
    event_id = result[0][0]
    print(f"✅ Inserted event: ID {event_id}, Serial: {test_serial}, Type: SYNC_TEST")
    
    # Wait for sync
    print(f"⏳ Waiting 8 seconds for sync...")
    time.sleep(8)
    
    # Check ClickHouse
    ch_check = query_clickhouse(f"SELECT count(*) FROM default.mes_manufacturing_events WHERE serial_number = '{test_serial}'")
    ch_count_after = query_clickhouse("SELECT count(*) FROM default.mes_manufacturing_events")
    
    print(f"📊 After test:")
    print(f"   ClickHouse total: {ch_count_after}")
    print(f"   Test record found: {ch_check}")
    
    if ch_check == "1":
        print(f"✅ SUCCESS: Event synced to ClickHouse!")
        
        # Check the actual data
        ch_data = query_clickhouse(f"SELECT id, serial_number, event_type FROM default.mes_manufacturing_events WHERE serial_number = '{test_serial}'")
        print(f"📋 Synced data: {ch_data}")
        return True
    else:
        print(f"❌ FAILED: Event not found in ClickHouse")
        return False

def check_sync_lag():
    """Check if there's any sync lag between databases"""
    print(f"\n🔍 Sync Lag Analysis")
    print("=" * 25)
    
    # Check latest records in both databases
    pg_latest_product = query_postgresql("SELECT id, name, created_at FROM mes_products ORDER BY created_at DESC LIMIT 1")
    ch_latest_product = query_clickhouse("SELECT id, name, created_at FROM default.mes_products ORDER BY created_at DESC LIMIT 1")
    
    print(f"📊 Latest mes_products:")
    if not isinstance(pg_latest_product, str) and pg_latest_product:
        print(f"   PostgreSQL: ID {pg_latest_product[0][0]}, {pg_latest_product[0][1]}, {pg_latest_product[0][2]}")
    
    if "Exception" not in ch_latest_product and "Error" not in ch_latest_product:
        print(f"   ClickHouse: {ch_latest_product}")
    
    # Check latest events
    pg_latest_event = query_postgresql("SELECT id, event_type, created_at FROM mes_manufacturing_events ORDER BY created_at DESC LIMIT 1")
    ch_latest_event = query_clickhouse("SELECT id, event_type, created_at FROM default.mes_manufacturing_events ORDER BY created_at DESC LIMIT 1")
    
    print(f"\n📊 Latest mes_manufacturing_events:")
    if not isinstance(pg_latest_event, str) and pg_latest_event:
        print(f"   PostgreSQL: ID {pg_latest_event[0][0]}, {pg_latest_event[0][1]}, {pg_latest_event[0][2]}")
    
    if "Exception" not in ch_latest_event and "Error" not in ch_latest_event:
        print(f"   ClickHouse: {ch_latest_event}")

def main():
    print("🔍 Real-time Sync Test with Schema Compliance")
    print("=" * 55)
    print("🎯 Testing mes_products and mes_manufacturing_events")
    
    # Check current sync lag
    check_sync_lag()
    
    # Test products sync
    products_success = test_mes_products_sync()
    
    # Test events sync
    events_success = test_mes_manufacturing_events_sync()
    
    # Final analysis
    print(f"\n" + "=" * 55)
    print(f"📊 **SYNC TEST RESULTS**")
    print("=" * 55)
    
    print(f"✅ mes_products sync: {'WORKING' if products_success else 'FAILED'}")
    print(f"✅ mes_manufacturing_events sync: {'WORKING' if events_success else 'FAILED'}")
    
    if products_success and events_success:
        print(f"\n🎉 **CONCLUSION: PeerDB sync is working correctly!**")
        print(f"📋 Both tables are syncing in real-time with 5-second intervals")
        print(f"📋 Your original issue may have been resolved or was a temporary glitch")
    elif products_success or events_success:
        print(f"\n⚠️  **PARTIAL SUCCESS: Some tables syncing, others not**")
        print(f"📋 Check PeerDB dashboard for specific mirror status")
    else:
        print(f"\n❌ **SYNC FAILURE: Neither table is syncing**")
        print(f"📋 Check PeerDB infrastructure and mirror configurations")
    
    print(f"\n🔧 **NEXT STEPS:**")
    print(f"1. Monitor PeerDB dashboard: http://localhost:3000")
    print(f"2. Check mirror status and logs")
    print(f"3. Verify your specific data inserts are following proper schema")
    print(f"4. Run this test again to confirm consistent behavior")

if __name__ == "__main__":
    main()
