#!/usr/bin/env python3
"""
Comprehensive UPDATE Synchronization Verification Script
Tests UPDATE operations across all entity tables to ensure proper sync behavior
"""

import psycopg2
import requests
import time
import random
import string

# Database connection parameters
PG_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'mes_db',
    'user': 'postgres',
    'password': 'postgres'
}

CH_CONFIG = {
    'host': 'localhost',
    'port': 8123,
    'user': 'default',
    'password': 'password'
}

def query_clickhouse(query):
    """Execute query on ClickHouse"""
    try:
        url = f"http://{CH_CONFIG['host']}:{CH_CONFIG['port']}/"
        response = requests.post(
            url,
            data=query,
            auth=(CH_CONFIG['user'], CH_CONFIG['password']),
            timeout=10
        )
        if response.status_code == 200:
            return response.text.strip()
        else:
            return f"Error: {response.status_code} - {response.text}"
    except Exception as e:
        return f"Exception: {e}"

def query_postgresql(query, params=None):
    """Execute query on PostgreSQL"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            result = cursor.fetchall()
        else:
            conn.commit()
            result = cursor.fetchall() if cursor.description else []
        
        cursor.close()
        conn.close()
        return result
    except Exception as e:
        return f"Exception: {e}"

def test_scanner_update():
    """Test UPDATE operation on mes_scanners table"""
    
    print("🧪 Testing UPDATE Synchronization: mes_scanners")
    print("-" * 50)
    
    # Step 1: Check current state
    print("📋 Step 1: Current state verification")
    
    # PostgreSQL current state
    pg_current = query_postgresql("SELECT id, name, updated_at FROM mes_scanners WHERE id = 1")
    print(f"   PostgreSQL: {pg_current}")
    
    # ClickHouse raw table (with duplicates)
    ch_raw = query_clickhouse("SELECT id, name, _peerdb_version FROM default.mes_scanners WHERE id = 1 ORDER BY _peerdb_version")
    print(f"   ClickHouse raw: {ch_raw}")
    
    # ClickHouse current view (deduplicated)
    ch_current = query_clickhouse("SELECT id, name FROM default.mes_scanners_current WHERE id = 1")
    print(f"   ClickHouse current: {ch_current}")
    
    # Step 2: Perform UPDATE
    print(f"\n🔄 Step 2: Performing UPDATE operation")
    
    new_name = f"Updated_Scanner_{int(time.time())}"
    update_result = query_postgresql(
        "UPDATE mes_scanners SET name = %s, updated_at = NOW() WHERE id = 1 RETURNING id, name, updated_at",
        (new_name,)
    )
    print(f"   UPDATE result: {update_result}")
    
    # Step 3: Wait for sync
    print(f"\n⏳ Step 3: Waiting 8 seconds for sync...")
    time.sleep(8)
    
    # Step 4: Verify sync results
    print(f"\n✅ Step 4: Verification after UPDATE")
    
    # PostgreSQL after update
    pg_after = query_postgresql("SELECT id, name, updated_at FROM mes_scanners WHERE id = 1")
    print(f"   PostgreSQL after: {pg_after}")
    
    # ClickHouse raw table (should have new version)
    ch_raw_after = query_clickhouse("SELECT id, name, _peerdb_version FROM default.mes_scanners WHERE id = 1 ORDER BY _peerdb_version")
    print(f"   ClickHouse raw after: {ch_raw_after}")
    
    # ClickHouse current view (should show latest)
    ch_current_after = query_clickhouse("SELECT id, name FROM default.mes_scanners_current WHERE id = 1")
    print(f"   ClickHouse current after: {ch_current_after}")
    
    # Step 5: Analysis
    print(f"\n📊 Step 5: Analysis")
    
    versions_count = query_clickhouse("SELECT count(*) FROM default.mes_scanners WHERE id = 1")
    print(f"   Total versions in ClickHouse: {versions_count}")
    
    latest_matches = new_name in ch_current_after if ch_current_after else False
    print(f"   Latest version matches: {latest_matches}")
    
    return latest_matches

def test_commodities_update():
    """Test UPDATE operation on mes_commodities table"""
    
    print("\n🧪 Testing UPDATE Synchronization: mes_commodities")
    print("-" * 50)
    
    # Find an existing commodity to update
    existing = query_postgresql("SELECT id, name FROM mes_commodities ORDER BY id LIMIT 1")
    if not existing or isinstance(existing, str):
        print("❌ No commodities found to test")
        return False
    
    commodity_id = existing[0][0]
    old_name = existing[0][1]
    
    print(f"📋 Testing with commodity ID: {commodity_id}, current name: {old_name}")
    
    # Perform update
    new_name = f"Updated_Commodity_{int(time.time())}"
    update_result = query_postgresql(
        "UPDATE mes_commodities SET name = %s, updated_at = NOW() WHERE id = %s RETURNING id, name",
        (new_name, commodity_id)
    )
    print(f"🔄 UPDATE result: {update_result}")
    
    # Wait for sync
    print(f"⏳ Waiting 8 seconds for sync...")
    time.sleep(8)
    
    # Verify in ClickHouse current view
    ch_result = query_clickhouse(f"SELECT id, name FROM default.mes_commodities_current WHERE id = {commodity_id}")
    print(f"✅ ClickHouse current view: {ch_result}")
    
    # Check if update synced correctly
    success = new_name in ch_result if ch_result else False
    print(f"📊 Update sync successful: {success}")
    
    return success

def test_multiple_table_updates():
    """Test UPDATE operations across multiple tables"""
    
    print("\n🧪 Testing UPDATE Synchronization: Multiple Tables")
    print("-" * 55)
    
    # Tables to test (those that likely have data)
    test_tables = [
        ('mes_commodities', 'name', 'description'),
        ('mes_scanners', 'name', 'description'),
        ('mes_users', 'first_name', 'last_name'),
        ('mes_areas', 'name', 'description')
    ]
    
    results = {}
    
    for table, col1, col2 in test_tables:
        print(f"\n📋 Testing {table}...")
        
        # Check if table has data
        count_query = f"SELECT count(*) FROM {table}"
        count = query_postgresql(count_query)
        
        if isinstance(count, str) or not count or count[0][0] == 0:
            print(f"   ⚠️  No data in {table}, skipping")
            results[table] = "No data"
            continue
        
        # Get first record
        select_query = f"SELECT id, {col1} FROM {table} ORDER BY id LIMIT 1"
        record = query_postgresql(select_query)
        
        if isinstance(record, str) or not record:
            print(f"   ❌ Failed to get record from {table}")
            results[table] = "Failed"
            continue
        
        record_id = record[0][0]
        old_value = record[0][1]
        
        # Update the record
        new_value = f"Test_Update_{int(time.time())}"
        update_query = f"UPDATE {table} SET {col1} = %s, updated_at = NOW() WHERE id = %s"
        
        update_result = query_postgresql(update_query, (new_value, record_id))
        
        if isinstance(update_result, str):
            print(f"   ❌ Update failed: {update_result}")
            results[table] = "Update failed"
            continue
        
        print(f"   🔄 Updated {table} ID {record_id}: {old_value} → {new_value}")
        
        # Wait a bit for sync
        time.sleep(3)
        
        # Check ClickHouse current view
        ch_query = f"SELECT {col1} FROM default.{table}_current WHERE id = {record_id}"
        ch_result = query_clickhouse(ch_query)
        
        if new_value in ch_result:
            print(f"   ✅ Sync successful")
            results[table] = "Success"
        else:
            print(f"   ❌ Sync failed: {ch_result}")
            results[table] = "Sync failed"
    
    return results

def create_usage_guide():
    """Create a usage guide for the deduplication views"""
    
    print(f"\n📚 Usage Guide: Working with Deduplication Views")
    print("=" * 60)
    
    print(f"\n🎯 **PROBLEM SOLVED:**")
    print(f"   ❌ Original issue: UPDATE operations created duplicate rows")
    print(f"   ✅ Solution: Deduplication views show only latest versions")
    
    print(f"\n📋 **HOW TO USE:**")
    print(f"   🔧 For analytics queries: Use *_current views")
    print(f"   🔧 For audit/history: Use original tables")
    
    print(f"\n💡 **EXAMPLES:**")
    print(f"   📊 Analytics: SELECT * FROM mes_scanners_current")
    print(f"   📊 History: SELECT * FROM mes_scanners ORDER BY _peerdb_version")
    print(f"   📊 Count latest: SELECT count(*) FROM mes_commodities_current")
    
    print(f"\n🔧 **AVAILABLE VIEWS:**")
    views = query_clickhouse("SHOW TABLES FROM default LIKE '%_current'")
    if views and "Exception" not in views:
        view_list = views.split('\n')
        print(f"   📋 Total deduplication views: {len(view_list)}")
        for i, view in enumerate(view_list[:5]):  # Show first 5
            print(f"   - {view}")
        if len(view_list) > 5:
            print(f"   ... and {len(view_list) - 5} more")
    
    print(f"\n⚡ **PERFORMANCE TIPS:**")
    print(f"   🚀 Views are computed on-demand (no storage overhead)")
    print(f"   🚀 Use views for real-time analytics")
    print(f"   🚀 Original tables preserve complete audit trail")

def main():
    print("🔍 Comprehensive UPDATE Synchronization Verification")
    print("=" * 60)
    
    # Test 1: Scanner update (detailed)
    scanner_success = test_scanner_update()
    
    # Test 2: Commodities update
    commodity_success = test_commodities_update()
    
    # Test 3: Multiple tables
    multi_results = test_multiple_table_updates()
    
    # Usage guide
    create_usage_guide()
    
    # Final summary
    print(f"\n" + "=" * 60)
    print(f"📊 **VERIFICATION SUMMARY**")
    print(f"=" * 60)
    
    print(f"\n🧪 **TEST RESULTS:**")
    print(f"   Scanner UPDATE: {'✅ PASSED' if scanner_success else '❌ FAILED'}")
    print(f"   Commodity UPDATE: {'✅ PASSED' if commodity_success else '❌ FAILED'}")
    
    print(f"\n📋 **MULTI-TABLE RESULTS:**")
    for table, result in multi_results.items():
        status = "✅" if result == "Success" else "⚠️" if "No data" in result else "❌"
        print(f"   {table}: {status} {result}")
    
    success_count = sum(1 for r in multi_results.values() if r == "Success")
    total_tested = len([r for r in multi_results.values() if r not in ["No data", "Failed"]])
    
    print(f"\n🎯 **OVERALL ASSESSMENT:**")
    if scanner_success and commodity_success and success_count > 0:
        print(f"   🎉 **SUCCESS**: UPDATE synchronization is working correctly!")
        print(f"   📊 Tested {total_tested} tables successfully")
        print(f"   📊 Deduplication views provide clean, latest data")
        print(f"   📊 Original tables preserve complete history")
    else:
        print(f"   ⚠️  **PARTIAL SUCCESS**: Some issues detected")
        print(f"   💡 Check individual test results above")
    
    print(f"\n📚 **NEXT STEPS:**")
    print(f"   1. Use *_current views for all analytics queries")
    print(f"   2. Monitor sync performance in PeerDB dashboard")
    print(f"   3. Test with your specific use cases")
    print(f"   4. Update application queries to use deduplication views")

if __name__ == "__main__":
    main()
